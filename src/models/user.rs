use chrono::{
    DateTime,
    Utc,
};
use serde::{
    Deserialize,
    Serialize,
};

use crate::models::Theme;

#[derive(Clone, PartialEq, Eq, Debug, Deserialize, Serialize)]
pub struct UserProfile {
    pub id:                    String, // UUID from Supabase auth
    pub email:                 Option<String>,
    pub provider:              Option<String>, // "email", "google", "apple"
    pub notifications_enabled: bool,
    pub unit_system:           String, // "metric" or "imperial"
    pub preferred_language:    String, // "en", "ru", "es"
    pub theme:                 Theme,
    pub onboarding_completed:  bool,
    pub created_at:            Option<DateTime<Utc>>,
    pub updated_at:            Option<DateTime<Utc>>,
}

impl Default for UserProfile {
    fn default() -> Self {
        Self {
            id:                    String::new(),
            email:                 None,
            provider:              None,
            notifications_enabled: false,
            unit_system:           "metric".to_string(),
            preferred_language:    "en".to_string(),
            theme:                 Theme::Light,
            onboarding_completed:  false,
            created_at:            None,
            updated_at:            None,
        }
    }
}

impl UserProfile {
    pub const fn is_authenticated(&self) -> bool { !self.id.is_empty() }

    pub const fn is_anonymous(&self) -> bool { self.id.is_empty() }
}
