use chrono::{
    DateTime,
    Duration,
    Utc,
};
use serde::{
    Deserialize,
    Serialize,
};

#[derive(<PERSON><PERSON>, PartialEq, Eq, Debug, Deserialize, Serialize)]
pub enum ActivityType {
    Walking,
    Running,
    Mixed,
}

impl Default for ActivityType {
    fn default() -> Self { Self::Walking }
}

#[derive(<PERSON><PERSON>, PartialEq, Eq, Debug, Deserialize, Serialize)]
pub struct TypicalActivity {
    pub duration:      u32,
    pub activity_type: Option<String>, // "walking", "running", "mixed"
}

#[derive(Clone, PartialEq, Eq, Debug, Deserialize, Serialize)]
pub struct Activity {
    pub id:               String, // UUID
    pub dog_id:           String, // UUID reference
    pub activity_type:    ActivityType,
    pub start_time:       DateTime<Utc>,
    pub end_time:         Option<DateTime<Utc>>,
    pub duration_minutes: Option<i32>, // Calculated by database trigger
    pub calories_burned:  Option<i32>,
    pub distance_meters:  Option<i32>,
    pub notes:            Option<String>,
    pub created_at:       Option<DateTime<Utc>>,
    pub updated_at:       Option<DateTime<Utc>>,
}

impl Default for Activity {
    fn default() -> Self {
        Self {
            id:               String::new(),
            dog_id:           String::new(),
            activity_type:    ActivityType::default(),
            start_time:       Utc::now(),
            end_time:         None,
            duration_minutes: None,
            calories_burned:  None,
            distance_meters:  None,
            notes:            None,
            created_at:       None,
            updated_at:       None,
        }
    }
}

impl Activity {
    pub fn duration(&self) -> Duration {
        self.end_time.map_or_else(
            || chrono::offset::Utc::now() - self.start_time,
            |end_time| end_time - self.start_time,
        )
    }

    // Legacy compatibility methods
    pub const fn this_type(&self) -> &ActivityType { &self.activity_type }
}
