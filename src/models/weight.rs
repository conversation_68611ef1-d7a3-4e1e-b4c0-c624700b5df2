use chrono::{
    DateTime,
    NaiveDate,
    Utc,
};
use serde::{
    Deserialize,
    Serialize,
};

#[derive(Clone, PartialEq, Debug, Deserialize, Serialize)]
pub struct WeightLog {
    pub id:         String, // UUID
    pub dog_id:     String, // UUID reference
    pub weight_kg:  f32,
    pub date:       NaiveDate,
    pub notes:      Option<String>,
    pub created_at: Option<DateTime<Utc>>,
}

impl Eq for WeightLog {}

impl Default for WeightLog {
    fn default() -> Self {
        Self {
            id:         String::new(),
            dog_id:     String::new(),
            weight_kg:  0.0,
            date:       chrono::offset::Utc::now().date_naive(),
            notes:      None,
            created_at: None,
        }
    }
}

#[derive(Clone, PartialEq, Eq, Debug, Deserialize, Serialize)]
pub struct BcsLog {
    pub id:         String, // UUID
    pub dog_id:     String, // UUID reference
    pub bcs_score:  i32,    // 1-9 scale
    pub date:       NaiveDate,
    pub notes:      Option<String>,
    pub created_at: Option<DateTime<Utc>>,
}

impl Default for BcsLog {
    fn default() -> Self {
        Self {
            id:         String::new(),
            dog_id:     String::new(),
            bcs_score:  5, // Default to middle of scale
            date:       chrono::offset::Utc::now().date_naive(),
            notes:      None,
            created_at: None,
        }
    }
}

// Legacy compatibility - keep the original WeightLog structure for backward compatibility
#[derive(Clone, PartialEq, Eq, Debug, Deserialize, Serialize)]
pub struct LegacyWeightLog {
    pub id:      String,
    pub pet_id:  String,
    pub weight:  u32,
    pub date:    String, // Using String for simplicity
    pub comment: Option<String>,
}
