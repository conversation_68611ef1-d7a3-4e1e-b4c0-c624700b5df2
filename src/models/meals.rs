use chrono::{
    DateTime,
    NaiveDate,
    Utc,
};
use serde::{
    Deserialize,
    Serialize,
};

#[derive(Clone, PartialEq, Eq, Debug, Deserialize, Serialize)]
pub enum MealType {
    Breakfast,
    Lunch,
    Dinner,
    Snack,
}

impl Default for MealType {
    fn default() -> Self { Self::Breakfast }
}

#[derive(Clone, PartialEq, Debug, Deserialize, Serialize)]
pub struct Meal {
    pub id:             String, // UUID
    pub dog_id:         String, // UUID reference
    pub date:           NaiveDate,
    pub meal_type:      MealType,
    pub total_calories: f32,
    pub notes:          Option<String>,
    pub created_at:     Option<DateTime<Utc>>,

    // Optional field for UI - populated when fetching with meal items
    #[serde(skip_serializing_if = "Option::is_none")]
    pub meal_items: Option<Vec<MealItem>>,
}

impl Default for Meal {
    fn default() -> Self {
        Self {
            id:             String::new(),
            dog_id:         String::new(),
            date:           chrono::offset::Utc::now().date_naive(),
            meal_type:      MealType::default(),
            total_calories: 0.0,
            notes:          None,
            created_at:     None,
            meal_items:     None,
        }
    }
}

#[derive(Clone, PartialEq, Debug, Deserialize, Serialize)]
pub struct MealItem {
    pub id:             String, // UUID
    pub meal_id:        String, // UUID reference
    pub food_id:        String, // UUID reference
    pub quantity_grams: f32,
    pub calories:       f32,
    pub created_at:     Option<DateTime<Utc>>,

    // Optional field for UI - populated when fetching with food details
    #[serde(skip_serializing_if = "Option::is_none")]
    pub food: Option<crate::models::food::Food>,
}

impl Default for MealItem {
    fn default() -> Self {
        Self {
            id:             String::new(),
            meal_id:        String::new(),
            food_id:        String::new(),
            quantity_grams: 0.0,
            calories:       0.0,
            created_at:     None,
            food:           None,
        }
    }
}

// Convenience struct for creating new meal items
#[derive(Clone, PartialEq, Debug, Deserialize, Serialize)]
pub struct NewMealItem {
    pub food_id:        String,
    pub quantity_grams: f32,
}

// Convenience struct for creating new meals with items
#[derive(Clone, PartialEq, Debug, Deserialize, Serialize)]
pub struct NewMeal {
    pub dog_id:     String,
    pub date:       NaiveDate,
    pub meal_type:  MealType,
    pub notes:      Option<String>,
    pub meal_items: Vec<NewMealItem>,
}

impl Default for NewMeal {
    fn default() -> Self {
        Self {
            dog_id:     String::new(),
            date:       chrono::offset::Utc::now().date_naive(),
            meal_type:  MealType::default(),
            notes:      None,
            meal_items: Vec::new(),
        }
    }
}

// Aggregated meal data for dashboard/analytics
#[derive(Clone, PartialEq, Debug, Deserialize, Serialize)]
pub struct MealSummary {
    pub date:              NaiveDate,
    pub total_calories:    f32,
    pub total_meals:       i32,
    pub meal_breakdown:    Vec<MealTypeCalories>,
    pub protein_percentage: f32,
    pub fat_percentage:     f32,
    pub carbs_percentage:   f32,
}

#[derive(Clone, PartialEq, Debug, Deserialize, Serialize)]
pub struct MealTypeCalories {
    pub meal_type:  MealType,
    pub calories:   f32,
    pub meal_count: i32,
}

// Legacy compatibility structures for existing UI code
#[derive(Clone, PartialEq, Debug, Deserialize, Serialize)]
pub struct LegacyMeal {
    pub id:        String,
    pub pet_id:    String,
    pub food_type: String,
    pub quantity:  f32,
    pub calories:  f32,
    pub timestamp: String,
}

// Helper implementations for UI compatibility
impl Meal {
    /// Calculate total calories from meal items
    pub fn calculate_total_calories(&self) -> f32 {
        if let Some(items) = &self.meal_items {
            items.iter().map(|item| item.calories).sum()
        } else {
            self.total_calories
        }
    }

    /// Get meal type as string for UI display
    pub const fn meal_type_display(&self) -> &'static str {
        match self.meal_type {
            MealType::Breakfast => "Breakfast",
            MealType::Lunch => "Lunch",
            MealType::Dinner => "Dinner",
            MealType::Snack => "Snack",
        }
    }

    /// Check if meal is today
    pub fn is_today(&self) -> bool {
        let today = chrono::offset::Utc::now().date_naive();
        self.date == today
    }
}

impl MealItem {
    /// Calculate calories based on food's calories per 100g and quantity
    pub fn calculate_calories(&self, calories_per_100g: f32) -> f32 {
        (self.quantity_grams / 100.0) * calories_per_100g
    }

    /// Get display name from associated food
    pub fn food_name(&self) -> Option<&str> { self.food.as_ref().map(|f| f.name.as_str()) }
}
