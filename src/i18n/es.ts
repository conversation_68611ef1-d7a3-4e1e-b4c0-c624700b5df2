
export const es = {
  settings: {
    appSettings: "Configuración de la Aplicación",
    units: "Unidades",
    theme: "Tema",
    language: "Idioma",
    about: "Acerca de",
    metric: "Métrico",
    imperial: "Imperial",
    english: "Inglés",
    spanish: "Español",
    russian: "Ruso",
    view: "Ver",
    debugMode: "Modo Depuración"
  },
  common: {
    save: "Guardar",
    cancel: "Cancelar",
    delete: "Eliminar",
    edit: "Editar",
    add: "Añadir",
    back: "Atrás",
    next: "Siguiente",
    food: "Comida",
    treats: "Premios",
    weight: "Peso",
    date: "Fecha",
    notes: "Notas",
    loggedOut: "Sesión cerrada",
    loggedOutSuccess: "Has cerrado sesión correctamente.",
    authSuccess: "Autenticación exitosa",
    authSuccessMsg: "Has iniciado sesión correctamente.",
    unitsUpdated: "Unidades actualizadas",
    unitsUpdatedMsg: "Ahora estás usando unidades {system}.",
    treatAdded: "Premio añadido",
    treatAddedMsg: "La dieta de hoy ha sido ajustada en consecuencia.",
    noWeightLogs: "No se encontraron registros de peso",
    age: "Edad",
    birthday: "Cumpleaños",
    breed: "Raza",
    dogProfile: "Perfil del Perro",
    viewResults: "Ver Resultados de Fitness",
    noResults: "No se encontraron resultados"
  },
  units: {
    kg: "kg",
    lb: "lb"
  },
  foods: {
    addFood: "Añadir Comida",
    foodName: "Nombre de la Comida",
    brand: "Marca",
    caloriesPerCup: "Calorías por taza",
    selectIcon: "Seleccionar Icono",
    startDate: "Fecha de Inicio",
    selectDate: "Seleccionar una fecha",
    noFoods: "Aún no se ha añadido ningún alimento",
    addFoodPrompt: "Añade la comida de tu perro para obtener recomendaciones de alimentación personalizadas",
    usedSince: "Usado desde {date}",
    active: "Activo",
    use: "Usar",
    foodHistory: "Historial de Alimentos"
  },
  treats: {
    treatsToday: "Premios Hoy",
    dietImpact: "Impacto en la Dieta",
    addTreat: "Añadir Premio",
    withinAllowance: "Los premios están dentro de la asignación diaria recomendada",
    reduceTreats: "Considera reducir los premios mañana para mantener el plan de dieta de tu perro"
  },
  pages: {
    settings: "Configuración",
    home: "Inicio",
    profile: "Perfil",
    weightHistory: "Historial de Peso",
    activityHistory: "Historial de Actividades"
  },
  navigation: {
    home: "Inicio",
    history: "Historial",
    meals: "Comidas",
    settings: "Ajustes",
    back: "Atrás",
    profile: "Perfil",
    ai: "IA",
  },
  diet: {
    todaysDiet: "Dieta de Hoy",
    cups: "tazas",
    kcal: "kcal",
    totalDailyCalories: "Total de Calorías Diarias",
    adjusted: "ajustado",
    noPreferredFoods: "No hay alimentos preferidos establecidos",
    addFoodsPrompt: "Añade comida a tu perro para ver recomendaciones personalizadas de alimentación"
  },
  weightHistory: {
    weightSummary: "Resumen de Peso",
    current: "Actual",
    target: "Objetivo",
    progress: "Progreso",
    weightTrend: "Tendencia de Peso",
    weightLogs: "Registros de Peso",
    logNewWeight: "Registrar Nuevo Peso",
    noWeightLogs: "No se encontraron registros de peso"
  },
  activityHistory: {
    activitySummary: "Resumen de Actividad",
    thisWeek: "Esta Semana",
    thisMonth: "Este Mes",
    dailyAvg: "Promedio Diario",
    activityTrend: "Tendencia de Actividad",
    week: "Semana",
    month: "Mes",
    year: "Año",
    listView: "Vista de Lista",
    tableView: "Vista de Tabla",
    noActivityHistory: "No se encontró historial de actividad",
    date: "Fecha",
    activity: "Actividad",
    duration: "Duración",
    calories: "Calorías",
    noActivitiesFound: "No se encontraron actividades"
  },
  breeds: {
    // Only include a few common breeds as an example
    "labrador retriever": "Labrador Retriever",
    "german shepherd": "Pastor Alemán",
    "golden retriever": "Golden Retriever",
    "french bulldog": "Bulldog Francés",
    "bulldog": "Bulldog",
    "poodle": "Caniche",
    "beagle": "Beagle",
    "rottweiler": "Rottweiler",
    "yorkshire terrier": "Yorkshire Terrier",
    "dachshund": "Dachshund"
    // Full list would be too long to include here
  }
};
