
export const en = {
  settings: {
    appSettings: "App Settings",
    units: "Units",
    theme: "Theme",
    language: "Language",
    about: "About",
    metric: "Metric",
    imperial: "Imperial",
    english: "English",
    spanish: "Spanish",
    russian: "Russian",
    view: "View",
    debugMode: "Debug Mode"
  },
  common: {
    save: "Save",
    cancel: "Cancel",
    delete: "Delete",
    edit: "Edit",
    add: "Add",
    back: "Back",
    next: "Next",
    food: "Food",
    treats: "Treats",
    weight: "Weight",
    date: "Date",
    notes: "Notes",
    loggedOut: "Logged out",
    loggedOutSuccess: "You have been logged out successfully.",
    authSuccess: "Authentication successful",
    authSuccessMsg: "You have been signed in successfully.",
    unitsUpdated: "Units updated",
    unitsUpdatedMsg: "You are now using {system} units.",
    treatAdded: "Treat added",
    treatAddedMsg: "Today's diet has been adjusted accordingly.",
    noWeightLogs: "No weight logs found",
    age: "Age",
    birthday: "Birthday",
    breed: "Breed",
    dogProfile: "Dog Profile",
    viewResults: "View Fitness Results",
    noResults: "No results found"
  },
  units: {
    kg: "kg",
    lb: "lb"
  },
  foods: {
    addFood: "Add Food",
    foodName: "Food Name",
    brand: "Brand",
    caloriesPerCup: "Calories per cup",
    selectIcon: "Select Icon",
    startDate: "Start Date",
    selectDate: "Select a date",
    noFoods: "No foods added yet",
    addFoodPrompt: "Add your dog's food to get personalized feeding recommendations",
    usedSince: "Used since {date}",
    active: "Active",
    use: "Use",
    foodHistory: "Food History"
  },
  treats: {
    treatsToday: "Treats Today",
    dietImpact: "Diet Impact",
    addTreat: "Add Treat",
    withinAllowance: "Treats are within the recommended daily allowance",
    reduceTreats: "Consider reducing treats tomorrow to maintain your dog's diet plan"
  },
  pages: {
    settings: "Settings",
    home: "Home",
    profile: "Profile",
    weightHistory: "Weight History",
    activityHistory: "Activity History"
  },
  navigation: {
    home: "Home",
    profile: "Profile",
    ai: "AI",
    history: "History",
    meals: "Meals",
    settings: "Settings",
    back: "Back"
  },
  diet: {
    todaysDiet: "Today's Diet",
    cups: "cups",
    kcal: "kcal",
    totalDailyCalories: "Total Daily Calories",
    adjusted: "adjusted",
    noPreferredFoods: "No preferred foods set",
    addFoodsPrompt: "Add foods in your dog's profile to see daily feeding recommendations"
  },
  weightHistory: {
    weightSummary: "Weight Summary",
    current: "Current",
    target: "Target",
    progress: "Progress",
    weightTrend: "Weight Trend",
    weightLogs: "Weight Logs",
    logNewWeight: "Log New Weight",
    noWeightLogs: "No weight logs found"
  },
  activityHistory: {
    activitySummary: "Activity Summary",
    thisWeek: "This Week",
    thisMonth: "This Month",
    dailyAvg: "Daily Avg",
    activityTrend: "Activity Trend",
    week: "Week",
    month: "Month",
    year: "Year",
    listView: "List View",
    tableView: "Table View",
    noActivityHistory: "No activity history found",
    date: "Date",
    activity: "Activity",
    duration: "Duration",
    calories: "Calories",
    noActivitiesFound: "No activities found"
  },
  breeds: {
    // Only include a few common breeds as an example
    "labrador retriever": "Labrador Retriever",
    "german shepherd": "German Shepherd",
    "golden retriever": "Golden Retriever",
    "french bulldog": "French Bulldog",
    "bulldog": "Bulldog",
    "poodle": "Poodle",
    "beagle": "Beagle",
    "rottweiler": "Rottweiler",
    "yorkshire terrier": "Yorkshire Terrier",
    "dachshund": "Dachshund"
    // Full list would be too long to include here, but in a real app you'd have all breeds
  }
};
