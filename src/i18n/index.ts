
import { en } from './en';
import { es } from './es';
import { ru } from './ru';

export type Language = 'en' | 'es' | 'ru';
export type Translations = typeof en;

export const translations: Record<Language, Translations> = {
  en,
  es,
  ru
};

export const languageNames: Record<Language, string> = {
  en: 'English',
  es: 'Español',
  ru: 'Русский'
};

// Helper function to format strings with parameters
export const formatString = (
  str: string,
  params?: Record<string, string | number>
): string => {
  if (!params) return str;
  
  return Object.entries(params).reduce((result, [key, value]) => {
    return result.replace(new RegExp(`\\{${key}\\}`, 'g'), String(value));
  }, str);
};
