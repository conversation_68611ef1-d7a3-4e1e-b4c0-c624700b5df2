
export const ru = {
  settings: {
    appSettings: "Настройки приложения",
    units: "Единицы измерения",
    theme: "Тема",
    language: "Язык",
    about: "О приложении",
    metric: "Метрическая",
    imperial: "Имперская",
    english: "Английский",
    spanish: "Испанский",
    russian: "Русский",
    view: "Посмотреть",
    debugMode: "Режим отладки"
  },
  common: {
    save: "Сохранить",
    cancel: "Отменить",
    delete: "Удалить",
    edit: "Редактировать",
    add: "Добавить",
    back: "Назад",
    next: "Далее",
    food: "Корм",
    treats: "Лакомства",
    weight: "Вес",
    date: "Дата",
    notes: "Заметки",
    loggedOut: "Вы вышли из системы",
    loggedOutSuccess: "Вы успешно вышли из системы.",
    authSuccess: "Аутентификация прошла успешно",
    authSuccessMsg: "Вы успешно вошли в систему.",
    unitsUpdated: "Единицы обновлены",
    unitsUpdatedMsg: "Теперь вы используете {system} единицы.",
    treatAdded: "Лакомство добавлено",
    treatAddedMsg: "Сегодняшняя диета была скорректирована соответствующим образом.",
    noWeightLogs: "Записи о весе не найдены",
    age: "Возраст",
    birthday: "День рождения",
    breed: "Порода",
    dogProfile: "Профиль собаки",
    viewResults: "Просмотр результатов фитнеса",
    noResults: "Результаты не найдены"
  },
  units: {
    kg: "кг",
    lb: "фнт"
  },
  foods: {
    addFood: "Добавить корм",
    foodName: "Название корма",
    brand: "Бренд",
    caloriesPerCup: "Калорий в чашке",
    selectIcon: "Выбрать иконку",
    startDate: "Дата начала",
    selectDate: "Выберите дату",
    noFoods: "Корм пока не добавлен",
    addFoodPrompt: "Добавьте корм вашей собаки, чтобы получить персонализированные рекомендации по кормлению",
    usedSince: "Используется с {date}",
    active: "Активный",
    use: "Использовать",
    foodHistory: "История кормления"
  },
  treats: {
    treatsToday: "Лакомства сегодня",
    dietImpact: "Влияние на диету",
    addTreat: "Добавить лакомство",
    withinAllowance: "Лакомства находятся в пределах рекомендуемой дневной нормы",
    reduceTreats: "Рассмотрите возможность сократить количество лакомств завтра, чтобы поддерживать план диеты вашей собаки"
  },
  pages: {
    settings: "Настройки",
    home: "Главная",
    profile: "Профиль",
    weightHistory: "История веса",
    activityHistory: "История активности"
  },
  navigation: {
    home: "Главная",
    history: "История",
    meals: "Питание",
    settings: "Настройки",
    back: "Назад",
    profile: "Профиль",
    ai: "ИИ",
  },
  diet: {
    todaysDiet: "Диета на сегодня",
    cups: "чашек",
    kcal: "ккал",
    totalDailyCalories: "Всего калорий в день",
    adjusted: "скорректировано",
    noPreferredFoods: "Предпочитаемые корма не установлены",
    addFoodsPrompt: "Добавьте корм вашей собаки, чтобы увидеть персонализированные рекомендации по кормлению"
  },
  weightHistory: {
    weightSummary: "Сводка по весу",
    current: "Текущий",
    target: "Целевой",
    progress: "Прогресс",
    weightTrend: "Тенденция веса",
    weightLogs: "Записи о весе",
    logNewWeight: "Записать новый вес",
    noWeightLogs: "Записи о весе не найдены"
  },
  activityHistory: {
    activitySummary: "Сводка активности",
    thisWeek: "За неделю",
    thisMonth: "За месяц",
    dailyAvg: "Ежедневно",
    activityTrend: "Тенденция активности",
    week: "Неделя",
    month: "Месяц",
    year: "Год",
    listView: "Список",
    tableView: "Таблица",
    noActivityHistory: "История активности не найдена",
    date: "Дата",
    activity: "Активность",
    duration: "Продолжительность",
    calories: "Калории",
    noActivitiesFound: "Активности не найдены"
  },
  breeds: {
    // Only include a few common breeds as an example
    "labrador retriever": "Лабрадор-ретривер",
    "german shepherd": "Немецкая овчарка",
    "golden retriever": "Золотистый ретривер",
    "french bulldog": "Французский бульдог",
    "bulldog": "Бульдог",
    "poodle": "Пудель",
    "beagle": "Бигль",
    "rottweiler": "Ротвейлер",
    "yorkshire terrier": "Йоркширский терьер",
    "dachshund": "Такса"
    // Full list would be too long to include here
  }
};
