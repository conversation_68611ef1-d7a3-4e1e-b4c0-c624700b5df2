//! My Dog in Fit mobile app

#![feature(
    closure_lifetime_binder,
    coroutines,
    coroutine_trait,
    iterator_try_collect,
    generic_const_exprs,
    generic_const_items,
    // non_lifetime_binders,
    step_trait,
    stmt_expr_attributes,
    thread_id_value,
    try_blocks,
    type_alias_impl_trait,
)]

// #[cfg(target_os = "ios")]
// use aloe_notifications::{
//     PushNotification,
//     PushNotifications,
// };
// #[cfg(target_os = "android")]
// use android_activity::{
//     AndroidApp,
//     GameActivity,
// };
use dioxus::logger::tracing::{
    debug,
    error,
    // trace,
    info,
    warn,
};
use dioxus::prelude::*;

mod config;
mod hooks;
mod i18n;
mod integrations;
mod models;
mod prelude;
mod state;
mod ui;
mod utils;

use ui::App;


// #[cfg(target_os = "android")]
// use dioxus::mobile::wry::prelude::{
//     JNIEnv,
//     JObject,
// };

// #[cfg(target_os = "android")]
// fn android_call_activity_method(method_name: &str, args: &[JValueGen<&JObject<'_>>]) {
//     use dioxus::mobile::wry::prelude::dispatch;
//     dispatch(|env, activity, _webview| {
//         env.call_method(
//             activity,
//             method_name,
//             "()Landroid/view/Window;",
//             // args.into_iter().map(|arg| arg.into()).collect(),
//             args,
//         )
//         .unwrap()
//         .l()
//         .unwrap()
//     });
// }

// #[cfg(target_os = "android")]
// fn android_call_window_method(method_name: &str, args: &[&str]) {
//     use dioxus::mobile::wry::prelude::dispatch;
//     dispatch(|env, _activity, webview| {
//         let window = android_call_activity_method("getWindow", &[]);
//         env.call_method(window, method_name, "()V", args).unwrap();
//     });
// }

// #[cfg(target_os = "android")]
// fn android_set_system_ui_visibility(flags: i32) {}


/// Sets Android appearance flags.
/// Use `use_effect(|| set_android_flags())` on demand.
/// ? Use use_window!() hook
#[cfg(target_os = "android")]
fn set_android_appearance() {
    // * https://stackoverflow.com/questions/29311078/android-completely-transparent-status-bar
    use dioxus::mobile::wry::prelude::dispatch;
    dispatch(|env, activity, _webview| {
        // * Get the window
        let window = env
            .call_method(activity, "getWindow", "()Landroid/view/Window;", &[])
            .unwrap()
            .l()
            .unwrap();
        // let call_window_method = |method_name: &str, scope: &str, args: &[&str]| {
        //     env.call_method(window, method_name, scope, args).unwrap();
        // };

        // * Set window flags. Use `clearFlags` to clear a respective flag.
        // * Flags: https://developer.android.com/reference/android/view/WindowManager.LayoutParams#constants_1
        let mut set_window_flags = |flag: i32| {
            env.call_method(&window, "addFlags", "(I)V", &[flag.into()])
                .unwrap();
        };

        // // * Keep screen on
        // const FLAG_KEEP_SCREEN_ON: i32 = 128;
        // set_window_flags(FLAG_KEEP_SCREEN_ON);

        // * Set translucent status and navigation bar
        const FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS: i32 = 0x80000000u32 as i32;
        const FLAG_LAYOUT_NO_LIMITS: i32 = 0x02000000;
        // const FLAG_TRANSLUCENT_STATUS: i32 = 0x04000000;
        // const FLAG_TRANSLUCENT_NAVIGATION: i32 = 0x08000000;
        set_window_flags(
            FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS
            //     | FLAG_TRANSLUCENT_STATUS
            //     | FLAG_TRANSLUCENT_NAVIGATION
                | FLAG_LAYOUT_NO_LIMITS,
        );

        // * SDK 19+
        let decor_view = env
            .call_method(&window, "getDecorView", "()Landroid/view/View;", &[])
            .unwrap()
            .l()
            .unwrap();


        let mut set_window_decor_flags = |flag: i32| {
            env.call_method(&decor_view, "setSystemUiVisibility", "(I)V", &[flag.into()])
                .unwrap();
        };

        const SYSTEM_UI_FLAG_LAYOUT_STABLE: i32 = 0x00000100;
        const SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN: i32 = 0x00000400;
        const SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION: i32 = 0x00000200;
        // const SYSTEM_UI_FLAG_FULLSCREEN: i32 = 0x00000004;
        // const SYSTEM_UI_FLAG_HIDE_NAVIGATION: i32 = 0x00000002;
        set_window_decor_flags(
            SYSTEM_UI_FLAG_LAYOUT_STABLE
                | SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION,
        );

        // * Use dark icons for status bar
        let insets_controller = env
            .call_method(&window, "getInsetsController", "()Landroid/view/WindowInsetsController;", &[])
            .unwrap()
            .l()
            .unwrap();

        if !insets_controller.is_null() {
            const APPEARANCE_LIGHT_STATUS_BARS: i32 = 0x00000008;
            const APPEARANCE_DARK_STATUS_BARS: i32 = 0x00000000;
            env.call_method(&insets_controller, "setSystemBarsAppearance", "(II)V", &[
                APPEARANCE_DARK_STATUS_BARS.into(),
                APPEARANCE_DARK_STATUS_BARS.into(),
                // APPEARANCE_LIGHT_STATUS_BARS.into(),
                // APPEARANCE_LIGHT_STATUS_BARS.into(),
            ])
            .unwrap();
        }

        // * Set status bar color
        // let color = 0xffcde099u32 as i32; // ARGB
        const TRANSPARENT: i32 = 0x00000000u32 as i32;
        env.call_method(&window, "setStatusBarColor", "(I)V", &[TRANSPARENT.into()])
            .unwrap();

        // * Set navigation bar color
        env.call_method(&window, "setNavigationBarColor", "(I)V", &[TRANSPARENT.into()])
            .unwrap();
    });
}

// #[cfg(target_os = "android")]
// #[no_mangle]
// fn android_main(app: AndroidApp) {
//     dioxus::logger::init(dioxus::logger::tracing::Level::DEBUG).unwrap();

//     set_android_appearance();
//     // Initialize NDK context for proper Android linking
//     ndk_context::initialize_android_context(app.vm_as_ptr(), app.activity_as_ptr());

//     dioxus::launch(|| {
//         info!("\n\n--------\n\nApplication started.\n");
//         App()
//     });
// }

// #[cfg(not(target_os = "android"))]
fn main() {
    dioxus::logger::init(dioxus::logger::tracing::Level::DEBUG).unwrap();

    #[cfg(target_os = "android")]
    set_android_appearance();

    // Initialize NDK context for proper Android linking
    // #[cfg(target_os = "android")]
    // ndk_context::initialize_android_context(app.vm_as_ptr(), app.activity_as_ptr());

    // #[cfg(target_os = "ios" || target_os = "android")]
    // {
    //     let mut push_notifications = PushNotifications::new();
    //     let notification = PushNotification::default();
    //     push_notifications.send_local_notification(&notification);
    // }
    // dioxus::launch(AppDummy);
    dioxus::launch(|| {
        info!("\n\n--------\n\nApplication started.\n");
        App()
    });
}

#[component]
fn AppDummy() -> Element {
    rsx! {
        div { "Hello, World!" }
    }
}
