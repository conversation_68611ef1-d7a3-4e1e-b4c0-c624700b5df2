export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      activities: {
        Row: {
          calories: number
          created_at: string | null
          date: string
          duration: number
          id: string
          notes: string | null
          pet_id: string
          time: string | null
          type: string
        }
        Insert: {
          calories: number
          created_at?: string | null
          date: string
          duration: number
          id?: string
          notes?: string | null
          pet_id: string
          time?: string | null
          type: string
        }
        Update: {
          calories?: number
          created_at?: string | null
          date?: string
          duration?: number
          id?: string
          notes?: string | null
          pet_id?: string
          time?: string | null
          type?: string
        }
        Relationships: [
          {
            foreignKeyName: "activities_pet_id_fkey"
            columns: ["pet_id"]
            isOneToOne: false
            referencedRelation: "pets"
            referencedColumns: ["id"]
          },
        ]
      }
      meals: {
        Row: {
          calories: number
          created_at: string | null
          date: string
          food: string
          id: string
          name: string
          notes: string | null
          pet_id: string
          portion: string | null
          time: string
        }
        Insert: {
          calories: number
          created_at?: string | null
          date: string
          food: string
          id?: string
          name: string
          notes?: string | null
          pet_id: string
          portion?: string | null
          time: string
        }
        Update: {
          calories?: number
          created_at?: string | null
          date?: string
          food?: string
          id?: string
          name?: string
          notes?: string | null
          pet_id?: string
          portion?: string | null
          time?: string
        }
        Relationships: [
          {
            foreignKeyName: "meals_pet_id_fkey"
            columns: ["pet_id"]
            isOneToOne: false
            referencedRelation: "pets"
            referencedColumns: ["id"]
          },
        ]
      }
      pets: {
        Row: {
          age: number | null
          bmi: number | null
          body_fit_state: number | null
          breed: string | null
          created_at: string | null
          daily_activity_goal: number
          daily_calorie_goal: number
          id: string
          image: string | null
          last_photo_update: string | null
          last_weight_update: string | null
          name: string
          owner_id: string | null
          size: string | null
          target_bmi: number | null
          target_weight: number | null
          type: string | null
          updated_at: string | null
          water_consumption: number | null
          weight: number
        }
        Insert: {
          age?: number | null
          bmi?: number | null
          body_fit_state?: number | null
          breed?: string | null
          created_at?: string | null
          daily_activity_goal: number
          daily_calorie_goal: number
          id?: string
          image?: string | null
          last_photo_update?: string | null
          last_weight_update?: string | null
          name: string
          owner_id?: string | null
          size?: string | null
          target_bmi?: number | null
          target_weight?: number | null
          type?: string | null
          updated_at?: string | null
          water_consumption?: number | null
          weight: number
        }
        Update: {
          age?: number | null
          bmi?: number | null
          body_fit_state?: number | null
          breed?: string | null
          created_at?: string | null
          daily_activity_goal?: number
          daily_calorie_goal?: number
          id?: string
          image?: string | null
          last_photo_update?: string | null
          last_weight_update?: string | null
          name?: string
          owner_id?: string | null
          size?: string | null
          target_bmi?: number | null
          target_weight?: number | null
          type?: string | null
          updated_at?: string | null
          water_consumption?: number | null
          weight?: number
        }
        Relationships: []
      }
      profiles: {
        Row: {
          created_at: string | null
          email: string | null
          id: string
          last_login: string | null
          name: string | null
        }
        Insert: {
          created_at?: string | null
          email?: string | null
          id: string
          last_login?: string | null
          name?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string | null
          id?: string
          last_login?: string | null
          name?: string | null
        }
        Relationships: []
      }
      recommendations: {
        Row: {
          category: string
          created_at: string | null
          id: string
          seen: boolean | null
          text: string
        }
        Insert: {
          category: string
          created_at?: string | null
          id?: string
          seen?: boolean | null
          text: string
        }
        Update: {
          category?: string
          created_at?: string | null
          id?: string
          seen?: boolean | null
          text?: string
        }
        Relationships: []
      }
      weight_logs: {
        Row: {
          created_at: string | null
          date: string
          id: string
          notes: string | null
          pet_id: string
          weight: number
        }
        Insert: {
          created_at?: string | null
          date: string
          id?: string
          notes?: string | null
          pet_id: string
          weight: number
        }
        Update: {
          created_at?: string | null
          date?: string
          id?: string
          notes?: string | null
          pet_id?: string
          weight?: number
        }
        Relationships: [
          {
            foreignKeyName: "weight_logs_pet_id_fkey"
            columns: ["pet_id"]
            isOneToOne: false
            referencedRelation: "pets"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
