// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://gdqtxuqsjmivbzcgkjjk.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdkcXR4dXFzam1pdmJ6Y2dramprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY0NzI4MzcsImV4cCI6MjA2MjA0ODgzN30._KMtREPsF5YA9biuw2bLVfGwTM-Fzou1W29T1lPSJaY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);