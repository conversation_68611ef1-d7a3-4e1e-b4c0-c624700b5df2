use std::collections::HashMap;

use reqwest::Client;
use serde::{
    Deserialize,
    Serialize,
};

use crate::config::Config;

/// Supabase authentication client
pub struct SupabaseAuth {
    config: Config,
    client: Client,
}

#[derive(Debug, Serialize)]
pub struct SignUpRequest<'a> {
    pub email:    &'a str,
    pub password: &'a str,
    pub data:     Option<HashMap<String, serde_json::Value>>,
}

#[derive(Debug, Serialize)]
pub struct SignInRequest<'a> {
    pub email:    &'a str,
    pub password: &'a str,
}

#[derive(Debug, Deserialize)]
pub struct AuthUser {
    pub id:                 String,
    pub email:              String,
    pub email_confirmed_at: Option<String>,
    pub created_at:         String,
    pub updated_at:         String,
    pub user_metadata:      serde_json::Value,
}

#[derive(Debug, Deserialize)]
pub struct Session {
    pub access_token:  String,
    pub refresh_token: String,
    pub expires_in:    u64,
    pub token_type:    String,
    pub user:          AuthUser,
}

#[derive(Debug, Deserialize)]
pub struct AuthResponse {
    pub user:    Option<AuthUser>,
    pub session: Option<Session>,
}

#[derive(Debug, thiserror::Error)]
pub enum AuthError {
    #[error("Network error: {0}")]
    Network(#[from] reqwest::Error),
    #[error("Authentication failed: {0}")]
    AuthFailed(String),
    #[error("Invalid credentials")]
    InvalidCredentials,
    #[error("User already exists")]
    UserExists,
    #[error("JSON error: {0}")]
    Json(#[from] serde_json::Error),
}

impl SupabaseAuth {
    pub fn new(config: Config) -> Self {
        Self {
            config,
            client: Client::new(),
        }
    }

    /// Sign up a new user with email and password
    pub async fn sign_up(&self, request: &SignUpRequest<'_>) -> Result<AuthResponse, AuthError> {
        let url = format!("{}/auth/v1/signup", self.config.supabase_url);

        let response = self
            .client
            .post(&url)
            .header("apikey", &self.config.supabase_anon_key)
            .header("Content-Type", "application/json")
            .json(request)
            .send()
            .await?;

        if response.status().is_success() {
            let auth_response: AuthResponse = response.json().await?;
            Ok(auth_response)
        } else {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());
            Err(AuthError::AuthFailed(error_text))
        }
    }

    /// Sign in an existing user with email and password
    pub async fn sign_in(&self, request: &SignInRequest<'_>) -> Result<AuthResponse, AuthError> {
        let url = format!("{}/auth/v1/token?grant_type=password", self.config.supabase_url);

        let response = self
            .client
            .post(&url)
            .header("apikey", &self.config.supabase_anon_key)
            .header("Content-Type", "application/json")
            .json(request)
            .send()
            .await?;

        if response.status().is_success() {
            let auth_response: AuthResponse = response.json().await?;
            Ok(auth_response)
        } else {
            let status = response.status();
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());

            match status {
                reqwest::StatusCode::BAD_REQUEST => Err(AuthError::InvalidCredentials),
                _ => Err(AuthError::AuthFailed(error_text)),
            }
        }
    }

    /// Sign out the current user
    pub async fn sign_out(&self, access_token: &str) -> Result<(), AuthError> {
        let url = format!("{}/auth/v1/logout", self.config.supabase_url);

        let response = self
            .client
            .post(&url)
            .header("apikey", &self.config.supabase_anon_key)
            .header("Authorization", format!("Bearer {access_token}"))
            .send()
            .await?;

        if response.status().is_success() {
            Ok(())
        } else {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());
            Err(AuthError::AuthFailed(error_text))
        }
    }

    /// Refresh an access token using a refresh token
    pub async fn refresh_token(&self, refresh_token: &str) -> Result<AuthResponse, AuthError> {
        let url = format!("{}/auth/v1/token?grant_type=refresh_token", self.config.supabase_url);

        let mut body = HashMap::new();
        body.insert("refresh_token", refresh_token);

        let response = self
            .client
            .post(&url)
            .header("apikey", &self.config.supabase_anon_key)
            .header("Content-Type", "application/json")
            .json(&body)
            .send()
            .await?;

        if response.status().is_success() {
            let auth_response: AuthResponse = response.json().await?;
            Ok(auth_response)
        } else {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());
            Err(AuthError::AuthFailed(error_text))
        }
    }

    /// Get user info from access token
    pub async fn get_user(&self, access_token: &str) -> Result<AuthUser, AuthError> {
        let url = format!("{}/auth/v1/user", self.config.supabase_url);

        let response = self
            .client
            .get(&url)
            .header("apikey", &self.config.supabase_anon_key)
            .header("Authorization", format!("Bearer {access_token}"))
            .send()
            .await?;

        if response.status().is_success() {
            let user: AuthUser = response.json().await?;
            Ok(user)
        } else {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());
            Err(AuthError::AuthFailed(error_text))
        }
    }
}
