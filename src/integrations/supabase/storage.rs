use std::collections::HashMap;

use reqwest::multipart::{Form, Part};
use serde::{Deserialize, Serialize};

use crate::config::Config;

#[derive(Debug, thiserror::Error)]
pub enum StorageError {
    #[error("HTTP request error: {0}")]
    Request(#[from] reqwest::Error),
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
    #[error("Upload error: {0}")]
    Upload(String),
    #[error("Invalid file format: {0}")]
    InvalidFormat(String),
    #[error("File too large: {0} bytes (max: {1} bytes)")]
    FileTooLarge(usize, usize),
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UploadResponse {
    #[serde(rename = "Key")]
    pub key: Option<String>,
    #[serde(rename = "Id")]
    pub id: Option<String>,
    #[serde(rename = "fullPath")]
    pub full_path: Option<String>,
}

#[derive(Debug, Clone)]
pub struct SupabaseStorage {
    client: reqwest::Client,
    config: Config,
    bucket_name: String,
}

impl SupabaseStorage {
    pub fn new(config: Config, bucket_name: String) -> Self {
        Self {
            client: reqwest::Client::new(),
            config,
            bucket_name,
        }
    }

    /// Upload a file to Supabase storage
    pub async fn upload_file(
        &self,
        file_path: &str,
        file_data: Vec<u8>,
        content_type: Option<&str>,
        access_token: Option<&str>,
    ) -> Result<String, StorageError> {
        // Validate file size (max 10MB for images)
        const MAX_FILE_SIZE: usize = 10 * 1024 * 1024;
        if file_data.len() > MAX_FILE_SIZE {
            return Err(StorageError::FileTooLarge(file_data.len(), MAX_FILE_SIZE));
        }

        // Validate file format based on content type
        if let Some(content_type) = content_type
            && !content_type.starts_with("image/") {
                return Err(StorageError::InvalidFormat(format!(
                    "Only image files are supported, got: {content_type}"
                )));
            }

        let url = format!(
            "{}/storage/v1/object/{}/{}",
            self.config.supabase_url, self.bucket_name, file_path
        );

        let mut request = self
            .client
            .post(&url)
            .header("apikey", &self.config.supabase_anon_key);

        // Add authorization header if access token is provided
        if let Some(token) = access_token {
            request = request.header("Authorization", format!("Bearer {token}"));
        }

        // Create multipart form with file data
        let part = Part::bytes(file_data)
            .file_name(file_path.split('/').next_back().unwrap_or("file").to_string());
        
        let part = if let Some(content_type) = content_type {
            part.mime_str(content_type)?
        } else {
            part
        };

        let form = Form::new().part("file", part);
        let response = request.multipart(form).send().await?;

        if response.status().is_success() {
            // Supabase returns the public URL directly in success cases
            let public_url = self.get_public_url(file_path);
            Ok(public_url)
        } else {
            let error_text = response.text().await?;
            Err(StorageError::Upload(format!("Upload failed: {error_text}")))
        }
    }

    /// Get the public URL for a file
    pub fn get_public_url(&self, file_path: &str) -> String {
        format!(
            "{}/storage/v1/object/public/{}/{}",
            self.config.supabase_url, self.bucket_name, file_path
        )
    }

    /// Delete a file from storage
    pub async fn delete_file(
        &self,
        file_path: &str,
        access_token: Option<&str>,
    ) -> Result<(), StorageError> {
        let url = format!(
            "{}/storage/v1/object/{}/{}",
            self.config.supabase_url, self.bucket_name, file_path
        );

        let mut request = self
            .client
            .delete(&url)
            .header("apikey", &self.config.supabase_anon_key);

        if let Some(token) = access_token {
            request = request.header("Authorization", format!("Bearer {token}"));
        }

        let response = request.send().await?;

        if response.status().is_success() {
            Ok(())
        } else {
            let error_text = response.text().await?;
            Err(StorageError::Upload(format!("Delete failed: {error_text}")))
        }
    }

    /// Upload an image with automatic path generation
    pub async fn upload_dog_image(
        &self,
        dog_id: &str,
        file_data: Vec<u8>,
        file_extension: &str,
        access_token: Option<&str>,
    ) -> Result<String, StorageError> {
        // Generate a unique filename with timestamp
        let timestamp = chrono::Utc::now().timestamp();
        let file_path = format!("dogs/{dog_id}/{dog_id}-{timestamp}.{file_extension}");
        
        // Determine content type from extension
        let content_type = match file_extension.to_lowercase().as_str() {
            "jpg" | "jpeg" => Some("image/jpeg"),
            "png" => Some("image/png"),
            "gif" => Some("image/gif"),
            "webp" => Some("image/webp"),
            _ => None,
        };

        self.upload_file(&file_path, file_data, content_type, access_token)
            .await
    }

    /// Create a new storage bucket (requires service role key)
    pub async fn create_bucket(
        &self,
        bucket_id: &str,
        public: bool,
        service_role_key: &str,
    ) -> Result<(), StorageError> {
        let url = format!("{}/storage/v1/bucket", self.config.supabase_url);

        let bucket_config = serde_json::json!({
            "id": bucket_id,
            "name": bucket_id,
            "public": public,
            "file_size_limit": 52428800, // 50MB
            "allowed_mime_types": ["image/jpeg", "image/png", "image/gif", "image/webp"]
        });

        let response = self
            .client
            .post(&url)
            .header("apikey", &self.config.supabase_anon_key)
            .header("Authorization", format!("Bearer {service_role_key}"))
            .header("Content-Type", "application/json")
            .json(&bucket_config)
            .send()
            .await?;

        if response.status().is_success() {
            Ok(())
        } else {
            let error_text = response.text().await?;
            Err(StorageError::Upload(format!(
                "Bucket creation failed: {error_text}"
            )))
        }
    }
}

/// Utility functions for file handling
pub mod utils {
    use super::StorageError;

    /// Extract file extension from filename
    pub fn get_file_extension(filename: &str) -> Option<&str> {
        filename.split('.').next_back()
    }

    /// Validate if the file extension is supported
    pub fn is_supported_image_format(extension: &str) -> bool {
        matches!(
            extension.to_lowercase().as_str(),
            "jpg" | "jpeg" | "png" | "gif" | "webp"
        )
    }

    /// Convert file data to base64 for preview
    pub fn file_to_base64(file_data: &[u8], mime_type: &str) -> String {
        use base64::{Engine as _, engine::general_purpose};
        let base64_data = general_purpose::STANDARD.encode(file_data);
        format!("data:{mime_type};base64,{base64_data}")
    }

    /// Resize image data (placeholder - would need image processing library)
    pub fn resize_image(
        _file_data: Vec<u8>,
        _max_width: u32,
        _max_height: u32,
    ) -> Result<Vec<u8>, StorageError> {
        // TODO: Implement image resizing using image crate or similar
        // For now, return original data
        todo!("Image resizing not implemented yet")
    }
}

/// Creates a configured Supabase storage client
pub fn create_storage_client() -> Result<SupabaseStorage, StorageError> {
    let config = crate::config::Config::from_env();
    Ok(SupabaseStorage::new(config, "dog-images".to_string()))
}