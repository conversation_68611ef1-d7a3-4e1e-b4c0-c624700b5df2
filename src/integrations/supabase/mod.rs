pub mod auth;
pub use auth::*;

pub mod client;
pub use client::*;

pub mod storage;
pub use storage::*;

use crate::config::Config;

/// Initialize a new Supabase client with the given configuration
pub fn create_client(config: Config) -> SupabaseClient { SupabaseClient::new(config) }

/// Initialize a new Supabase storage client with the given configuration
pub fn create_storage(config: Config, bucket_name: String) -> SupabaseStorage {
    SupabaseStorage::new(config, bucket_name)
}
