//! Gemini AI integration module for the My Dog in Fit application.
//! 
//! This module provides integration with Google's Gemini 1.5 Pro API for
//! AI-powered dog health recommendations and assistance.

mod client;
pub mod models;
mod dog_context;
mod prompt_templates;

pub use client::*;
pub use models::*;
pub use dog_context::DogContextService;
pub use prompt_templates::PromptTemplates;

use crate::config::Config;
use std::sync::Arc;

/// Initialize Gemini client with application configuration
pub fn init_gemini_client(config: Arc<Config>) -> GeminiResult<GeminiClient> {
    GeminiClient::new(config)
}

/// Re-export commonly used types
pub type GeminiResult<T> = std::result::Result<T, GeminiError>;