//! Specialized Prompt Templates for Dog Health Recommendations
//!
//! This module provides carefully crafted prompt templates for different types
//! of AI-powered dog health recommendations, ensuring consistent, safe, and
//! helpful responses from the Gemini AI assistant.

use crate::integrations::gemini::models::DogContext;

/// Prompt template builder for dog health recommendations
pub struct PromptTemplates;

impl PromptTemplates {
    /// Generate a nutrition-focused prompt with dog context
    pub fn nutrition_recommendation(dog_context: &DogContext) -> String {
        let base_context = Self::generate_base_context(dog_context);

        format!(
            r"You are a knowledgeable pet nutrition advisor. Based on the following dog profile, provide specific nutrition recommendations.

{base_context}

Please provide recommendations for:

1. **Daily Calorie Intake**: Confirm or adjust the current calorie goal based on the dog's profile
2. **Food Type & Quality**: Recommend appropriate food types (dry kibble, wet food, raw diet, etc.)
3. **Feeding Schedule**: Suggest optimal meal frequency and timing
4. **Treats & Supplements**: Advise on healthy treats and any beneficial supplements
5. **Special Considerations**: Address any weight management or health-specific dietary needs

**Important Guidelines:**
- Provide specific, actionable advice
- Include portion sizes when relevant
- Always recommend consulting with a veterinarian for medical concerns
- Focus on general nutritional wellness, not medical treatment
- Consider the dog's current weight status and goals

Format your response in clear sections with specific recommendations."
        )
    }

    /// Generate an exercise-focused prompt with dog context
    pub fn exercise_recommendation(dog_context: &DogContext) -> String {
        let base_context = Self::generate_base_context(dog_context);

        format!(
            r"You are a professional dog fitness advisor. Based on the following dog profile, provide specific exercise recommendations.

{base_context}

Please provide recommendations for:

1. **Daily Activity Duration**: Recommend total daily exercise time
2. **Exercise Types**: Suggest appropriate activities (walks, runs, play, training)
3. **Intensity Levels**: Advise on appropriate exercise intensity
4. **Schedule & Frequency**: Recommend optimal timing and frequency of activities
5. **Safety Considerations**: Important safety tips based on dog's profile
6. **Progressive Training**: How to gradually increase activity if needed

**Important Guidelines:**
- Consider the dog's age, size, and current fitness level
- Provide breed-specific exercise recommendations where relevant
- Address any weight management through exercise
- Always emphasize safety and gradual progression
- Recommend veterinary clearance for significant activity changes
- Include mental stimulation activities

Format your response with clear, actionable exercise plans."
        )
    }

    /// Generate a general health advice prompt with dog context
    pub fn health_advice(dog_context: &DogContext) -> String {
        let base_context = Self::generate_base_context(dog_context);

        format!(
            r"You are a knowledgeable dog wellness advisor. Based on the following dog profile, provide general health and wellness guidance.

{base_context}

Please provide advice on:

1. **Weight Management**: Strategies for achieving/maintaining optimal weight
2. **General Wellness**: Overall health maintenance tips
3. **Preventive Care**: Important health monitoring and care practices
4. **Lifestyle Optimization**: Ways to improve overall quality of life
5. **Warning Signs**: When to consult a veterinarian
6. **Age-Appropriate Care**: Specific considerations for the dog's life stage

**Important Guidelines:**
- Focus on preventive care and general wellness
- Provide practical, actionable advice
- Always emphasize that this is general guidance, not medical diagnosis
- Strongly recommend regular veterinary check-ups
- Consider the dog's specific characteristics and needs
- Include both physical and mental well-being

Format your response with clear sections and practical tips."
        )
    }

    /// Generate a daily tip prompt with dog context
    pub fn daily_tip(dog_context: &DogContext) -> String {
        let base_context = Self::generate_base_context(dog_context);

        format!(
            r"You are a friendly dog care expert. Based on the following dog profile, provide a helpful daily tip for the dog owner.

{base_context}

Provide a single, practical tip that:
- Is relevant to this specific dog's profile and needs
- Can be easily implemented today
- Promotes the dog's health, happiness, or well-being
- Is safe and appropriate for the dog's characteristics

Keep the tip concise (2-3 sentences) but actionable. Focus on one specific area like:
- Nutrition optimization
- Exercise enhancement
- Mental stimulation
- Health monitoring
- Bonding activities
- Safety practices

End with a gentle reminder that for medical concerns, always consult with a veterinarian."
        )
    }

    /// Generate a general chat prompt with optional dog context
    pub fn general_chat(message: &str, dog_context: Option<&DogContext>) -> String {
        let context_section = if let Some(context) = dog_context {
            format!("\n**Dog Profile Context:**\n{}\n", Self::generate_base_context(context))
        } else {
            String::new()
        };

        format!(
            r"You are a knowledgeable and friendly dog care assistant. Help answer the following question or respond to the message about dog care, health, nutrition, or general pet ownership.

**User Message:** {message}{context_section}

**Guidelines:**
- Provide helpful, accurate information about dog care
- Be friendly and supportive in your tone
- Focus on practical, actionable advice
- Always recommend consulting a veterinarian for medical concerns
- Use the dog profile context (if provided) to personalize your response
- Keep responses informative but conversational

If the question is outside your expertise or involves serious medical concerns, politely redirect to veterinary consultation."
        )
    }

    /// Generate a breed-specific advice prompt
    pub fn breed_specific_advice(dog_context: &DogContext, focus_area: &str) -> String {
        let base_context = Self::generate_base_context(dog_context);

        format!(
            r"You are a dog breed specialist and pet care advisor. Based on the following dog profile, provide breed-specific advice focused on {focus_area}.

{base_context}

Please provide specific advice that considers:
- Breed-typical characteristics and tendencies
- Common breed-related health considerations
- Breed-appropriate activities and care practices
- Genetic predispositions and prevention strategies
- Optimal care practices for this breed type

**Guidelines:**
- Be specific to the dog's breed characteristics
- Provide actionable, practical advice
- Include both preventive and proactive care suggestions
- Always recommend professional veterinary guidance for health concerns
- Consider the individual dog's profile within breed context

Format your response with clear, breed-informed recommendations."
        )
    }

    /// Generate a weight management specific prompt
    pub fn weight_management_advice(dog_context: &DogContext) -> String {
        let base_context = Self::generate_base_context(dog_context);
        let weight_status = Self::determine_weight_status(dog_context);

        format!(
            r"You are a certified pet nutrition and fitness specialist. Based on the following dog profile, provide comprehensive weight management advice.

{base_context}

**Current Weight Status:** {weight_status}

Please provide a detailed weight management plan including:

1. **Calorie Management**: Precise daily calorie recommendations and adjustments
2. **Portion Control**: Specific feeding guidelines and measurement tips
3. **Exercise Plan**: Tailored activity recommendations for weight goals
4. **Timeline & Expectations**: Realistic weight loss/gain timeline
5. **Monitoring Strategy**: How to track progress effectively
6. **Treat Management**: Healthy treat options and limits

**Guidelines:**
- Provide specific, measurable recommendations
- Create a safe, gradual approach to weight changes
- Consider the dog's age, activity level, and health status
- Include motivational milestones and progress markers
- Emphasize the importance of veterinary supervision for significant weight changes

Format as a comprehensive but easy-to-follow weight management plan."
        )
    }

    /// Generate emergency preparedness prompt
    pub fn emergency_preparedness(dog_context: &DogContext) -> String {
        let base_context = Self::generate_base_context(dog_context);

        format!(
            r"You are a veterinary emergency preparedness advisor. Based on the following dog profile, provide emergency preparedness guidance.

{base_context}

Please provide advice on:

1. **Emergency Kit**: Essential items to have on hand for this dog
2. **Warning Signs**: Critical symptoms that require immediate veterinary attention
3. **First Aid Basics**: Safe first aid measures appropriate for this dog's profile
4. **Emergency Contacts**: What information to have readily available
5. **Transport Preparation**: How to safely transport this dog in an emergency
6. **Prevention**: How to minimize emergency risks based on the dog's characteristics

**Important Guidelines:**
- Emphasize that this is preparedness information, not emergency treatment
- Always stress the importance of immediate veterinary care in emergencies
- Provide practical, implementable preparation steps
- Consider the dog's specific needs and potential risks
- Include both medical and non-medical emergency scenarios

Format as a comprehensive emergency preparedness checklist."
        )
    }

    // Helper methods

    /// Generate base dog context for all prompts
    fn generate_base_context(dog_context: &DogContext) -> String {
        let mut context = format!("**Dog Profile:**\n- Name: {}", dog_context.name);

        if let Some(age) = dog_context.age_years {
            context.push_str(&format!("\n- Age: {age:.1} years"));
        }

        context.push_str(&format!("\n- Gender: {}\n- Size: {}", dog_context.gender, dog_context.size));

        if let Some(weight) = dog_context.current_weight_kg {
            context.push_str(&format!("\n- Current Weight: {weight:.1} kg"));
        }

        if let Some(target) = dog_context.target_weight_kg {
            context.push_str(&format!("\n- Target Weight: {target:.1} kg"));
        }

        if let Some(bcs) = dog_context.body_condition_score
            && bcs != 5 {
                context.push_str(&format!("\n- Body Condition Score: {bcs}/9"));
            }

        if !dog_context.activity_level.is_empty() && dog_context.activity_level != "moderate" {
            context.push_str(&format!("\n- Activity Level: {}", dog_context.activity_level));
        }

        if let Some(typical_activity) = &dog_context.typical_activity {
            context.push_str(&format!("\n- Typical Activity: {typical_activity}"));
        }

        if let Some(neutered) = dog_context.is_neutered {
            context.push_str(&format!("\n- Neutered: {}", if neutered { "Yes" } else { "No" }));
        }

        if let Some(breed) = &dog_context.breed
            && !breed.is_empty() && breed != "Unknown" {
                context.push_str(&format!("\n- Breed: {breed}"));
            }

        if !dog_context.recent_activities.is_empty() {
            context
                .push_str(&format!("\n- Recent Activities: {}", dog_context.recent_activities.join(", ")));
        }

        if !dog_context.preferred_foods.is_empty() {
            context.push_str(&format!("\n- Current Foods: {}", dog_context.preferred_foods.join(", ")));
        }

        if !dog_context.health_concerns.is_empty() {
            context.push_str(&format!(
                "\n- Health Considerations: {}",
                dog_context.health_concerns.join(", ")
            ));
        }

        if let Some(calorie_goal) = dog_context.daily_calorie_goal {
            context.push_str(&format!("\n- Daily Calorie Goal: {calorie_goal} calories"));
        }

        context.push_str(&format!("\n- Activity Summary: {}", dog_context.activity_summary));

        context
    }

    /// Determine weight status for targeted advice
    fn determine_weight_status(dog_context: &DogContext) -> String {
        if let (Some(current), Some(target)) = (dog_context.current_weight_kg, dog_context.target_weight_kg)
        {
            let diff_percent = ((current - target).abs() / target) * 100.0;
            if current > target {
                if diff_percent > 10.0 {
                    "Significantly overweight - needs structured weight loss plan".to_string()
                } else {
                    "Slightly overweight - needs minor adjustments".to_string()
                }
            } else if current < target {
                if diff_percent > 10.0 {
                    "Significantly underweight - needs structured weight gain plan".to_string()
                } else {
                    "Slightly underweight - needs minor adjustments".to_string()
                }
            } else {
                "At target weight - focus on maintenance".to_string()
            }
        } else if let Some(bcs) = dog_context.body_condition_score {
            match bcs {
                1..=3 => "Underweight based on body condition score".to_string(),
                4..=5 => "Ideal weight based on body condition score".to_string(),
                6..=7 => "Overweight based on body condition score".to_string(),
                8..=9 => "Obese based on body condition score".to_string(),
                _ => "Weight status assessment needed - recommend veterinary consultation".to_string(),
            }
        } else {
            "Weight status assessment needed - recommend veterinary consultation".to_string()
        }
    }

    /// Generate age-appropriate care prompt
    pub fn age_appropriate_care(dog_context: &DogContext) -> String {
        let base_context = Self::generate_base_context(dog_context);
        let life_stage = Self::determine_life_stage(dog_context);

        format!(
            r"You are a veterinary life-stage specialist. Based on the following dog profile, provide age-appropriate care recommendations.

{base_context}

**Life Stage:** {life_stage}

Please provide specific advice for:

1. **Nutrition Needs**: Age-appropriate dietary requirements and adjustments
2. **Exercise Guidelines**: Safe and beneficial activity levels for this age
3. **Health Monitoring**: Key health indicators to watch at this life stage
4. **Preventive Care**: Age-specific preventive measures and veterinary care
5. **Comfort & Quality of Life**: Ways to enhance well-being at this stage
6. **Transition Planning**: Preparing for the next life stage if applicable

**Guidelines:**
- Focus on the specific needs of this life stage
- Provide practical, implementable advice
- Consider breed-specific aging patterns if known
- Balance activity with appropriate rest and recovery
- Emphasize quality of life and comfort
- Recommend appropriate veterinary care frequency

Format your response with clear, age-appropriate recommendations."
        )
    }

    /// Determine life stage for targeted advice
    fn determine_life_stage(dog_context: &DogContext) -> String {
        if let Some(age) = dog_context.age_years {
            match dog_context.size.as_str() {
                "small" =>
                    if age < 1.0 {
                        "Puppy (Small breed)"
                    } else if age < 7.0 {
                        "Adult (Small breed)"
                    } else {
                        "Senior (Small breed)"
                    },
                "large" =>
                    if age < 1.5 {
                        "Puppy (Large breed)"
                    } else if age < 6.0 {
                        "Adult (Large breed)"
                    } else {
                        "Senior (Large breed)"
                    },
                _ => {
                    // medium
                    if age < 1.2 {
                        "Puppy (Medium breed)"
                    } else if age < 7.0 {
                        "Adult (Medium breed)"
                    } else {
                        "Senior (Medium breed)"
                    }
                },
            }
        } else {
            "Age not specified - general adult care"
        }
        .to_string()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::integrations::gemini::models::DogContext;

    fn create_test_dog_context() -> DogContext {
        DogContext {
            name:                 "Buddy".to_string(),
            age_years:            Some(3.5),
            age_months:           Some(42),
            gender:               "male".to_string(),
            size:                 "medium".to_string(),
            current_weight_kg:    Some(25.0),
            weight_kg:            Some(25.0),
            target_weight_kg:     Some(23.0),
            body_condition_score: Some(6),
            activity_level:       "moderate".to_string(),
            typical_activity:     Some("walking".to_string()),
            is_neutered:          Some(true),
            breed:                Some("Labrador Retriever".to_string()),
            recent_activities:    vec!["Walking: 30 minutes".to_string()],
            preferred_foods:      vec!["Dry kibble".to_string()],
            health_concerns:      vec!["Overweight".to_string()],
            health_notes:         "No health concerns noted".to_string(),
            daily_calorie_goal:   Some(2000),
            daily_calories:       Some(1800),
            activity_summary:     "Today: 30 minutes, This week: 180 minutes".to_string(),
        }
    }

    #[test]
    fn test_nutrition_recommendation_prompt() {
        let context = create_test_dog_context();
        let prompt = PromptTemplates::nutrition_recommendation(&context);

        assert!(prompt.contains("nutrition advisor"));
        assert!(prompt.contains("Buddy"));
        assert!(prompt.contains("Daily Calorie Intake"));
        assert!(prompt.contains("veterinarian"));
    }

    #[test]
    fn test_exercise_recommendation_prompt() {
        let context = create_test_dog_context();
        let prompt = PromptTemplates::exercise_recommendation(&context);

        assert!(prompt.contains("fitness advisor"));
        assert!(prompt.contains("Daily Activity Duration"));
        assert!(prompt.contains("Safety Considerations"));
    }

    #[test]
    fn test_general_chat_prompt() {
        let context = create_test_dog_context();
        let message = "How often should I walk my dog?";
        let prompt = PromptTemplates::general_chat(message, Some(&context));

        assert!(prompt.contains(message));
        assert!(prompt.contains("Buddy"));
        assert!(prompt.contains("dog care assistant"));
    }

    #[test]
    fn test_weight_status_determination() {
        let context = create_test_dog_context();
        let status = PromptTemplates::determine_weight_status(&context);

        // Current: 25kg, Target: 23kg, should be slightly overweight
        assert!(status.contains("overweight"));
    }

    #[test]
    fn test_life_stage_determination() {
        let context = create_test_dog_context(); // 3.5 years, medium
        let stage = PromptTemplates::determine_life_stage(&context);

        assert!(stage.contains("Adult"));
        assert!(stage.contains("Medium"));
    }
}
