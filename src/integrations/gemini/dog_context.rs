//! Dog Context Service for AI Assistant Integration
//!
//! This module provides functionality to extract and format dog profile data
//! for personalized AI recommendations. It creates structured context that
//! enables the Gemini AI to provide targeted advice based on specific dog
//! characteristics, health metrics, and lifestyle factors.

use chrono::{
    DateTime,
    Utc,
};
use serde::{
    Deserialize,
    Serialize,
};

use crate::{
    integrations::gemini::models::DogContext,
    models::{
        ActivityType,
        dogs::{
            ActivityLevel,
            Dog,
            DogSize,
            Gender,
        },
    },
};

/// Service for extracting and formatting dog profile data for AI prompts
pub struct DogContextService;

impl DogContextService {
    /// Create comprehensive dog context for AI assistant
    ///
    /// This extracts all relevant information from a dog's profile and recent
    /// activity to provide personalized context for AI recommendations.
    pub fn create_context(dog: &Dog) -> DogContext {
        DogContext {
            name:                 dog.name.clone(),
            age_years:            dog.age_years(),
            age_months:           dog.age_months(),
            gender:               Self::format_gender(&dog.gender),
            size:                 Self::format_size(&dog.size),
            weight_kg:            dog.current_weight,
            current_weight_kg:    dog.current_weight,
            target_weight_kg:     dog.target_weight,
            body_condition_score: dog.body_condition_score,
            activity_level:       dog
                .activity_level
                .as_ref()
                .map_or_else(|| "moderate".to_string(), Self::format_activity_level),
            typical_activity:     dog.typical_activity.clone(),
            is_neutered:          dog.is_neutered,
            breed:                Some(dog.breed()),
            recent_activities:    Self::extract_recent_activities(dog),
            preferred_foods:      Self::extract_preferred_foods(dog),
            health_concerns:      Self::identify_health_concerns(dog),
            health_notes:         Some("No health concerns noted".to_string()),
            daily_calorie_goal:   dog.daily_calorie_goal.map(|goal| goal as i32),
            daily_calories:       dog.daily_calorie_goal.map(|goal| goal as f32),
            activity_summary:     Self::create_activity_summary(dog),
        }
    }

    /// Create context specifically for nutrition advice
    pub fn create_nutrition_context(dog: &Dog) -> DogContext {
        let mut context = Self::create_context(dog);

        // Add nutrition-specific emphasis
        context.preferred_foods = Self::extract_preferred_foods(dog);

        // Add weight management context
        if let (Some(current), Some(target)) = (dog.current_weight, dog.target_weight) {
            if current > target {
                context
                    .health_concerns
                    .push("Weight loss needed".to_string());
            } else if current < target {
                context
                    .health_concerns
                    .push("Weight gain needed".to_string());
            }
        }

        context
    }

    /// Create context specifically for exercise recommendations
    pub fn create_exercise_context(dog: &Dog) -> DogContext {
        let mut context = Self::create_context(dog);

        // Add exercise-specific data
        context.activity_summary = Self::create_detailed_activity_summary(dog);

        // Add size-based exercise considerations
        match dog.size {
            DogSize::Small => context
                .health_concerns
                .push("Small breed exercise considerations".to_string()),
            DogSize::Large => context
                .health_concerns
                .push("Large breed exercise considerations".to_string()),
            _ => {},
        }

        context
    }

    /// Create context for general health advice
    pub fn create_health_context(dog: &Dog) -> DogContext {
        let mut context = Self::create_context(dog);

        // Add comprehensive health considerations
        context.health_concerns = Self::comprehensive_health_assessment(dog);

        context
    }

    // Private helper methods

    fn format_gender(gender: &Gender) -> String {
        match gender {
            Gender::Male => "male".to_string(),
            Gender::Female => "female".to_string(),
        }
    }

    fn format_size(size: &DogSize) -> String {
        match size {
            DogSize::Small => "small".to_string(),
            DogSize::Medium => "medium".to_string(),
            DogSize::Large => "large".to_string(),
        }
    }

    fn format_activity_level(level: &ActivityLevel) -> String {
        match level {
            ActivityLevel::Low => "low".to_string(),
            ActivityLevel::Moderate => "moderate".to_string(),
            ActivityLevel::High => "high".to_string(),
        }
    }

    fn extract_recent_activities(dog: &Dog) -> Vec<String> {
        let now = Utc::now();
        let week_ago = now - chrono::Duration::days(7);

        dog.activities
            .iter()
            .filter(|activity| activity.start_time >= week_ago)
            .map(|activity| {
                format!(
                    "{:?}: {} minutes",
                    activity.activity_type,
                    activity
                        .duration_minutes
                        .unwrap_or(activity.duration().num_minutes() as i32)
                )
            })
            .collect()
    }

    fn extract_preferred_foods(dog: &Dog) -> Vec<String> {
        dog.preferred_foods
            .iter()
            .map(|food| food.name.clone())
            .collect()
    }

    fn identify_health_concerns(dog: &Dog) -> Vec<String> {
        let mut concerns = Vec::new();

        // Body condition score analysis
        if let Some(bcs) = dog.body_condition_score {
            match bcs {
                1..=3 => concerns.push("Underweight".to_string()),
                4..=5 => concerns.push("Ideal weight".to_string()),
                6..=7 => concerns.push("Overweight".to_string()),
                8..=9 => concerns.push("Obese".to_string()),
                _ => {},
            }
        }

        // Age-related considerations
        if let Some(age) = dog.age_years() {
            if age < 1.0 {
                concerns.push("Puppy nutrition needs".to_string());
            } else if age > 7.0 {
                concerns.push("Senior dog care".to_string());
            }
        }

        // Weight discrepancy
        if let (Some(current), Some(target)) = (dog.current_weight, dog.target_weight) {
            let diff_percent = ((current - target).abs() / target) * 100.0;
            if diff_percent > 10.0 {
                concerns.push("Significant weight management needed".to_string());
            }
        }

        concerns
    }

    fn comprehensive_health_assessment(dog: &Dog) -> Vec<String> {
        let mut concerns = Self::identify_health_concerns(dog);

        // Add breed-specific considerations (would be expanded with real breed data)
        if !dog.breed().is_empty() && dog.breed() != "Unknown" {
            concerns.push(format!("{} breed-specific considerations", dog.breed()));
        }

        // Add activity level concerns
        if let Some(activity_level) = &dog.activity_level {
            match activity_level {
                ActivityLevel::Low => concerns.push("Low activity level concerns".to_string()),
                ActivityLevel::High => concerns.push("High activity level management".to_string()),
                _ => {},
            }
        }

        concerns
    }

    fn create_activity_summary(dog: &Dog) -> String {
        let today_duration = dog.get_total_activity_duration_today();
        let week_duration = dog.get_total_activity_duration_this_week();

        format!(
            "Today: {} minutes, This week: {} minutes",
            today_duration.num_minutes(),
            week_duration.num_minutes()
        )
    }

    fn create_detailed_activity_summary(dog: &Dog) -> String {
        let today = dog.get_total_activity_duration_today();
        let week = dog.get_total_activity_duration_this_week();
        let month = dog.get_total_activity_duration_this_month();

        format!(
            "Today: {} min, Week: {} min, Month: {} min. Recent activities: {}",
            today.num_minutes(),
            week.num_minutes(),
            month.num_minutes(),
            Self::extract_recent_activities(dog).join(", ")
        )
    }

    // /// Generate a structured prompt context string for AI
    // pub fn generate_prompt_context(context: &DogContext) -> String {
    //     let mut prompt = format!("Dog Profile:\n- Name: {}\n", context.name);

    //     if let Some(age) = context.age_years {
    //         prompt.push_str(&format!("- Age: {age:.1} years\n"));
    //     }

    //     prompt.push_str(&format!("- Gender: {}\n- Size: {}\n", context.gender, context.size));

    //     if let Some(weight) = context.current_weight_kg {
    //         prompt.push_str(&format!("- Current Weight: {weight:.1} kg\n"));
    //     }

    //     if let Some(target) = context.target_weight_kg {
    //         prompt.push_str(&format!("- Target Weight: {target:.1} kg\n"));
    //     }

    //     let bcs = context.body_condition_score.unwrap_or(5);
    //     if bcs != 5 {
    //         prompt.push_str(&format!("- Body Condition Score: {bcs}/9\n"));
    //     }

    //     if !context.activity_level.is_empty() && context.activity_level != "moderate" {
    //         prompt.push_str(&format!("- Activity Level: {}\n", context.activity_level));
    //     }

    //     if let Some(typical_activity) = &context.typical_activity {
    //         prompt.push_str(&format!("- Typical Activity: {typical_activity}\n"));
    //     }

    //     if let Some(neutered) = context.is_neutered {
    //         prompt.push_str(&format!("- Neutered: {}\n", if neutered { "Yes" } else { "No" }));
    //     }

    //     if let Some(breed) = &context.breed
    //         && !breed.is_empty()
    //         && breed != "Unknown"
    //     {
    //         prompt.push_str(&format!("- Breed: {breed}\n"));
    //     }

    //     if !context.recent_activities.is_empty() {
    //         prompt.push_str(&format!("- Recent Activities: {}\n", context.recent_activities.join(", ")));
    //     }

    //     if !context.preferred_foods.is_empty() {
    //         prompt.push_str(&format!("- Preferred Foods: {}\n", context.preferred_foods.join(", ")));
    //     }

    //     if !context.health_concerns.is_empty() {
    //         prompt.push_str(&format!("- Health Considerations: {}\n", context.health_concerns.join(",
    // ")));     }

    //     if let Some(calorie_goal) = context.daily_calorie_goal {
    //         prompt.push_str(&format!("- Daily Calorie Goal: {calorie_goal} calories\n"));
    //     }

    //     prompt.push_str(&format!("- Activity Summary: {}\n", context.activity_summary));

    //     prompt.push_str(
    //         "\nPlease provide personalized, non-medical advice based on this information. Always include
    // \          appropriate disclaimers about consulting veterinarians for medical concerns.\n",
    //     );

    //     prompt
    // }
}

#[cfg(test)]
mod tests {
    use chrono::NaiveDate;

    use super::*;
    use crate::models::dogs::{
        Dog,
        DogSize,
        Gender,
    };

    #[test]
    fn test_create_basic_context() {
        let mut dog = Dog::default();
        dog.name = "Buddy".to_string();
        dog.gender = Gender::Male;
        dog.size = DogSize::Medium;
        dog.current_weight = Some(25.0);
        dog.target_weight = Some(23.0);
        dog.body_condition_score = Some(6);

        let context = DogContextService::create_context(&dog);

        assert_eq!(context.name, "Buddy");
        assert_eq!(context.gender, "male");
        assert_eq!(context.size, "medium");
        assert_eq!(context.current_weight_kg, Some(25.0));
        assert_eq!(context.target_weight_kg, Some(23.0));
        assert_eq!(context.body_condition_score, Some(6));
    }

    #[test]
    fn test_generate_prompt_context() {
        let dog_context = DogContext {
            name:                 "Rex".to_string(),
            age_years:            Some(3.5),
            gender:               "male".to_string(),
            size:                 "large".to_string(),
            current_weight_kg:    Some(30.0),
            target_weight_kg:     Some(28.0),
            body_condition_score: Some(7),
            activity_level:       Some("moderate".to_string()),
            typical_activity:     Some("walking".to_string()),
            is_neutered:          Some(true),
            breed:                Some("Labrador Retriever".to_string()),
            recent_activities:    vec!["Walking: 30 minutes".to_string()],
            preferred_foods:      vec!["Dry kibble".to_string()],
            health_concerns:      vec!["Overweight".to_string()],
            age_months:           Some(42),
            health_notes:         "No health concerns noted".to_string(),
            daily_calorie_goal:   Some(2000),
            daily_calories:       2000,
            activity_summary:     "Today: 30 minutes, This week: 180 minutes".to_string(),
        };

        let prompt = DogContextService::generate_prompt_context(&dog_context);

        assert!(prompt.contains("Name: Rex"));
        assert!(prompt.contains("Age: 3.5 years"));
        assert!(prompt.contains("Current Weight: 30.0 kg"));
        assert!(prompt.contains("Target Weight: 28.0 kg"));
        assert!(prompt.contains("Body Condition Score: 7/9"));
        assert!(prompt.contains("Breed: Labrador Retriever"));
        assert!(prompt.contains("Recent Activities: Walking: 30 minutes"));
        assert!(prompt.contains("Health Considerations: Overweight"));
        assert!(prompt.contains("veterinarian"));
    }

    #[test]
    fn test_nutrition_specific_context() {
        let mut dog = Dog::default();
        dog.current_weight = Some(30.0);
        dog.target_weight = Some(28.0);

        let context = DogContextService::create_nutrition_context(&dog);

        assert!(
            context
                .health_concerns
                .contains(&"Weight loss needed".to_string())
        );
    }

    #[test]
    fn test_health_concerns_identification() {
        let mut dog = Dog::default();
        dog.body_condition_score = Some(8); // Obese
        dog.birthday = Some(NaiveDate::from_ymd_opt(2015, 1, 1).unwrap()); // Senior dog

        let context = DogContextService::create_health_context(&dog);

        assert!(context.health_concerns.iter().any(|c| c.contains("Obese")));
        assert!(context.health_concerns.iter().any(|c| c.contains("Senior")));
    }
}
