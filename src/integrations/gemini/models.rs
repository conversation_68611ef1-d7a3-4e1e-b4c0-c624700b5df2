//! Data models for Gemini API integration.

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use thiserror::Error;

/// Errors that can occur during Gemini API operations
#[derive(Erro<PERSON>, Debug, Clone)]
pub enum GeminiError {
    #[error("Network error: {0}")]
    Network(String),
    
    #[error("API error: {0}")]
    Api(String),
    
    #[error("Configuration error: {0}")]
    Config(String),
    
    #[error("Configuration error: {message}")]
    Configuration {
        message: String,
    },
    
    #[error("Rate limited. Retry after: {0}s")]
    RateLimit(u64),
    
    #[error("Request timeout")]
    Timeout,
    
    #[error("Invalid response format")]
    InvalidResponse,
    
    #[error("No API key configured")]
    MissingApiKey,
    
    #[error("Content filtered by safety settings")]
    ContentFiltered,
}

/// Request to Gemini API
#[derive(Debug, <PERSON><PERSON>, Serialize)]
pub struct GeminiRequest {
    pub contents: Vec<Content>,
    pub safety_settings: Option<Vec<SafetySetting>>,
    pub generation_config: Option<GenerationConfig>,
}

/// Content part of a Gemini request
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Content {
    pub parts: Vec<Part>,
    pub role: Option<String>,
}

/// Part of the content (text only for now)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Part {
    pub text: Option<String>,
}

/// Safety settings for content filtering
#[derive(Debug, Clone, Serialize)]
pub struct SafetySetting {
    pub category: String,
    pub threshold: String,
}

/// Generation configuration
#[derive(Debug, Clone, Serialize)]
pub struct GenerationConfig {
    pub temperature: Option<f32>,
    pub top_p: Option<f32>,
    pub top_k: Option<i32>,
    pub max_output_tokens: Option<i32>,
}

/// Response from Gemini API
#[derive(Debug, Clone, Deserialize)]
pub struct GeminiResponse {
    pub candidates: Option<Vec<Candidate>>,
    pub prompt_feedback: Option<PromptFeedback>,
    pub usage_metadata: Option<UsageMetadata>,
}

/// Candidate response from Gemini
#[derive(Debug, Clone, Deserialize)]
pub struct Candidate {
    pub content: Option<Content>,
    pub finish_reason: Option<String>,
    pub index: Option<i32>,
    pub safety_ratings: Option<Vec<SafetyRating>>,
}

/// Content in response (reusing from request for consistency)
#[derive(Debug, Clone, Deserialize)]
pub struct ResponseContent {
    pub parts: Vec<ResponsePart>,
    pub role: Option<String>,
}

/// Part in response content
#[derive(Debug, Clone, Deserialize)]
pub struct ResponsePart {
    pub text: Option<String>,
}

/// Safety rating in response
#[derive(Debug, Clone, Deserialize)]
pub struct SafetyRating {
    pub category: String,
    pub probability: String,
}

/// Prompt feedback
#[derive(Debug, Clone, Deserialize)]
pub struct PromptFeedback {
    pub safety_ratings: Option<Vec<SafetyRating>>,
    pub block_reason: Option<String>,
}

/// Usage metadata
#[derive(Debug, Clone, Deserialize)]
pub struct UsageMetadata {
    pub prompt_token_count: Option<i32>,
    pub candidates_token_count: Option<i32>,
    pub total_token_count: Option<i32>,
}

/// Dog context for personalized AI recommendations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DogContext {
    pub name: String,
    pub breed: Option<String>,
    pub age_months: Option<i32>,
    pub age_years: Option<f32>,
    pub weight_kg: Option<f32>,
    pub gender: String,
    pub size: String,
    pub current_weight_kg: Option<f32>,
    pub target_weight_kg: Option<f32>,
    pub activity_level: String,
    pub body_condition_score: Option<i32>,
    pub daily_calories: Option<f32>,
    pub typical_activity: Option<String>,
    pub is_neutered: Option<bool>,
    pub recent_activities: Vec<String>,
    pub preferred_foods: Vec<String>,
    pub health_concerns: Vec<String>,
    pub daily_calorie_goal: Option<i32>,
    pub activity_summary: String,
    pub health_notes: Option<String>,
}

/// AI conversation message type
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageType {
    Text,
    Recommendation,
    HealthTip,
    NutritionAdvice,
    ExerciseAdvice,
}

/// AI conversation message for offline queue
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiMessage {
    pub id: String,
    pub user_message: String,
    pub message_type: MessageType,
    pub dog_context: Option<DogContext>,
    pub timestamp: DateTime<Utc>,
    pub response: Option<String>,
    pub status: MessageStatus,
}

/// Status of AI message processing
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum MessageStatus {
    Pending,
    Processing,
    Completed,
    Failed,
    Queued, // For offline mode
    Sent,
    Delivered,
}

/// Predefined fallback responses for offline mode
#[derive(Debug, Clone)]
pub struct FallbackResponse {
    pub message_type: MessageType,
    pub response: String,
}

impl FallbackResponse {
    /// Get appropriate fallback responses based on message type
    pub fn get_fallbacks() -> Vec<Self> {
        vec![
            Self {
                message_type: MessageType::NutritionAdvice,
                response: "I'd love to help with nutrition advice! When I'm back online, I can provide personalized recommendations based on your dog's profile. For now, remember that fresh water should always be available, and portion control is key to maintaining a healthy weight.".to_string(),
            },
            Self {
                message_type: MessageType::ExerciseAdvice,
                response: "Exercise is so important for your dog's health! While I'm offline, here's a general tip: most dogs need at least 30 minutes of exercise daily, but this varies by breed, age, and health. When I'm back online, I can give you specific recommendations for your pup!".to_string(),
            },
            Self {
                message_type: MessageType::HealthTip,
                response: "I'm currently offline, but I'll be back soon with personalized health tips! In the meantime, keep monitoring your dog's eating habits, energy levels, and bathroom routines - these are great indicators of overall health.".to_string(),
            },
            Self {
                message_type: MessageType::Text,
                response: "I'm temporarily offline, but your question has been saved and I'll respond with personalized advice once I'm back online. Thank you for your patience!".to_string(),
            },
            Self {
                message_type: MessageType::Recommendation,
                response: "I'm currently unable to generate new recommendations, but I'll provide personalized suggestions once I'm back online. Your request has been saved and will be processed soon!".to_string(),
            },
        ]
    }
    
    /// Get fallback response for a specific message type
    pub fn get_for_type(message_type: &MessageType) -> String {
        Self::get_fallbacks()
            .into_iter()
            .find(|f| std::mem::discriminant(&f.message_type) == std::mem::discriminant(message_type))
            .map(|f| f.response)
            .unwrap_or_else(|| {
                "I'm currently offline, but I'll be back soon with helpful advice for you and your dog!".to_string()
            })
    }
    
    /// Get fallback response for general errors
    pub fn general_error() -> Self {
        Self {
            message_type: MessageType::Text,
            response: "I encountered an error while processing your request. Please try again later, and I'll do my best to help you and your dog!".to_string(),
        }
    }
    
    /// Get fallback response for offline mode
    pub fn offline() -> Self {
        Self {
            message_type: MessageType::Text,
            response: "I'm currently offline, but your message has been saved and I'll respond once I'm back online. Thank you for your patience!".to_string(),
        }
    }
}

impl Default for GenerationConfig {
    fn default() -> Self {
        Self {
            temperature: Some(0.7),
            top_p: Some(0.9),
            top_k: Some(40),
            max_output_tokens: Some(1024),
        }
    }
}

impl GeminiRequest {
    /// Create a new text request
    pub fn new_text(text: String) -> Self {
        Self {
            contents: vec![Content {
                parts: vec![Part { text: Some(text) }],
                role: Some("user".to_string()),
            }],
            safety_settings: Some(Self::default_safety_settings()),
            generation_config: Some(GenerationConfig::default()),
        }
    }
    
    /// Default safety settings for dog health content
    fn default_safety_settings() -> Vec<SafetySetting> {
        vec![
            SafetySetting {
                category: "HARM_CATEGORY_HARASSMENT".to_string(),
                threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
            },
            SafetySetting {
                category: "HARM_CATEGORY_HATE_SPEECH".to_string(),
                threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
            },
            SafetySetting {
                category: "HARM_CATEGORY_SEXUALLY_EXPLICIT".to_string(),
                threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
            },
            SafetySetting {
                category: "HARM_CATEGORY_DANGEROUS_CONTENT".to_string(),
                threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
            },
        ]
    }
}

impl GeminiResponse {
    /// Extract the text response from candidates
    pub fn get_text(&self) -> Option<String> {
        self.candidates
            .as_ref()?
            .first()?
            .content
            .as_ref()?
            .parts
            .first()?
            .text
            .clone()
    }
    
    /// Check if the response was blocked by safety filters
    pub fn is_blocked(&self) -> bool {
        if let Some(feedback) = &self.prompt_feedback
            && feedback.block_reason.is_some() {
                return true;
            }
        
        if let Some(candidates) = &self.candidates {
            for candidate in candidates {
                if let Some(finish_reason) = &candidate.finish_reason
                    && finish_reason == "SAFETY" {
                        return true;
                    }
            }
        }
        
        false
    }
}

impl From<reqwest::Error> for GeminiError {
    fn from(error: reqwest::Error) -> Self {
        if error.is_timeout() {
            Self::Timeout
        } else if error.is_request() {
            Self::Network(format!("Connection error: {error}"))
        } else {
            Self::Network(error.to_string())
        }
    }
}

impl From<serde_json::Error> for GeminiError {
    fn from(_error: serde_json::Error) -> Self {
        Self::InvalidResponse
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_gemini_request_creation() {
        let request = GeminiRequest::new_text("Test message".to_string());
        assert_eq!(request.contents.len(), 1);
        assert_eq!(request.contents[0].parts[0].text, "Test message");
        assert!(request.safety_settings.is_some());
        assert!(request.generation_config.is_some());
    }

    #[test]
    fn test_fallback_responses() {
        let fallbacks = FallbackResponse::get_fallbacks();
        assert!(!fallbacks.is_empty());
        
        let nutrition_fallback = FallbackResponse::get_for_type(&MessageType::NutritionAdvice);
        assert!(nutrition_fallback.contains("nutrition"));
        
        let exercise_fallback = FallbackResponse::get_for_type(&MessageType::ExerciseAdvice);
        assert!(exercise_fallback.contains("exercise") || exercise_fallback.contains("Exercise"));
    }

    #[test]
    fn test_dog_context_serialization() {
        let context = DogContext {
            name: "Buddy".to_string(),
            breed: Some("Golden Retriever".to_string()),
            age_months: Some(24),
            age_years: Some(2.0),
            weight_kg: Some(25.5),
            gender: "Male".to_string(),
            size: "medium".to_string(),
            current_weight_kg: Some(25.5),
            target_weight_kg: Some(24.0),
            activity_level: "High".to_string(),
            body_condition_score: Some(5),
            daily_calories: Some(1200.0),
            typical_activity: Some("walking".to_string()),
            is_neutered: Some(true),
            recent_activities: vec![],
            preferred_foods: vec![],
            health_concerns: vec![],
            daily_calorie_goal: Some(1200),
            activity_summary: "No activity data available".to_string(),
            health_notes: None,
        };
        
        let serialized = serde_json::to_string(&context).expect("Failed to serialize");
        let _deserialized: DogContext = serde_json::from_str(&serialized).expect("Failed to deserialize");
    }
}