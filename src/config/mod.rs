use dioxus::logger::tracing::debug;
use dotenv_codegen::dotenv;
use serde::{
    Deserialize,
    Serialize,
};

const GEMINI_API_KEY: &str = dotenv!("GEMINI_API_KEY");
const SUPABASE_URL: &str = dotenv!("SUPABASE_URL");
const SUPABASE_ANON_KEY: &str = dotenv!("SUPABASE_ANON_KEY");

/// Application configuration containing Supabase and other service credentials
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub gemini_api_key:    String,
    pub supabase_anon_key: String,
    pub supabase_url:      String,
}

impl Config {
    /// Create a new Config from environment variables
    /// Uses build-time embedding for web/mobile builds, runtime loading for native development
    pub fn from_env() -> Self {
        // Use dotenv!() macro to load from .env file at compile time
        // This ensures all required configuration is available and loaded from .env
        Self {
            gemini_api_key:    GEMINI_API_KEY.to_string(),
            supabase_anon_key: SUPABASE_ANON_KEY.to_string(),
            supabase_url:      SUPABASE_URL.to_string(),
        }
    }

    /// Create a default config for development/testing
    pub fn default_dev() -> Self {
        Self {
            supabase_url:      "https://localhost:54321".to_string(),
            supabase_anon_key: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.\
                                eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.\
                                CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
                .to_string(),
            gemini_api_key:    "dev_key".to_string(),
        }
    }
}

impl Default for Config {
    fn default() -> Self { Self::from_env() }
}
