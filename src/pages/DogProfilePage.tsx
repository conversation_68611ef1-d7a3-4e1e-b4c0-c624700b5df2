import React, {useState, useEffect} from 'react'
import {useNavigate, useParams} from 'react-router-dom'
import {Button} from '@/components/ui/button'
import {useLocalData} from '@/hooks/useLocalData'
import {
  Form,
  FormField,
} from '@/components/ui/form';
import {Input} from '@/components/ui/input'
import {useForm} from 'react-hook-form'
import * as z from 'zod'
import {zodResolver} from '@hookform/resolvers/zod'
import {Image, Camera, Dog, Pencil, Trash, ChartPieIcon, Weight, Edit2 as Edit} from 'lucide-react'
import BreedSelector from "@/components/BreedSelector"
import {fileToBase64} from '@/utils/breed-recognition/fileUtils'
import FoodManagement from '@/components/FoodManagement'
import DeleteDogDialog from '@/components/DeleteDogDialog'
import {useLanguage} from '@/hooks/useLanguage'
import {PageHeaderProfile} from '@/components/PageHeader'
import {<PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle} from '@/components/ui/card'
import {formatDistanceToNow} from 'date-fns'
import {formatDate} from '@/lib/utils'
import EditDogProfileModal from '@/components/EditDogProfileModal'
import {cn} from '@/lib/utils'


const DogProfilePage = () => {
  const navigate = useNavigate();
  const {getPetById, deletePet, addPetFood, removePetFood, setFoodPreferred, updatePet,
    selectedPetId,
    pets,
  } = useLocalData();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingBreed, setIsEditingBreed] = useState(false);
  const {t} = useLanguage();
  const pet = getPetById(selectedPetId);

  const needsWeightUpdate = pet.lastWeightUpdate
    ? new Date().getTime() - new Date(pet.lastWeightUpdate).getTime() > 14 * 24 * 60 * 60 * 1000 // 14 days
    : true;

  const handleEditName = () => {
    setIsEditingName(true);
  };

  const handleSaveName = () => {
    const newName = form.getValues('name');
    setIsEditingName(false);
    console.log('newName', newName);
    if(!newName) return;

    updatePet({
      ...pet,
      name: newName,
    });
  };

  const handleEditBreed = () => {
    setIsEditingBreed(true);
  };

  const handleSaveBreed = () => {
    const newBreed = form.getValues('breed');
    setIsEditingBreed(false);
    if(!newBreed) return;

    updatePet({
      ...pet,
      breed: newBreed,
    });
  };

  const handleDelete = () => {
    deletePet(pet.id);
    if(pets.length === 0) navigate('/');
  };

  // const handleViewResults = () => {
  //   navigate(`/results?petId=${pet.id}`);
  // };

  const handleFoodPreferred = (foodId: string, isPreferred: boolean, startDate?: Date) => {
    // If we're setting a food as preferred
    if(isPreferred && startDate) {
      // Get the food that's being set as preferred
      const selectedFood = pet.preferredFoods?.find(f => f.id === foodId);

      if(selectedFood) {
        // Find the currently preferred food, if any
        const currentPreferred = pet.preferredFoods?.find(f => f.isPreferred);

        // If there was a previously preferred food, add it to history with an end date
        if(currentPreferred && currentPreferred.id !== foodId) {
          const foodHistory = [...(pet.foodHistory || [])];

          // Add current food to history
          foodHistory.push({
            foodId: currentPreferred.id,
            name: currentPreferred.name,
            brand: currentPreferred.brand,
            startDate: currentPreferred.startDate || new Date(),
            endDate: new Date()
          });

          // Update the pet with new food history
          updatePet({
            ...pet,
            foodHistory
          });
        }

        // Update the selected food with start date
        selectedFood.startDate = startDate;
        selectedFood.isPreferred = true;

        // Create a new preferredFoods array with the updated food
        const updatedFoods = pet.preferredFoods.map(food =>
          food.id === foodId ? selectedFood : {...food, isPreferred: false}
        );

        // Update the pet
        updatePet({
          ...pet,
          preferredFoods: updatedFoods
        });
      }
    } else {
      // If we're just toggling preference without a start date
      setFoodPreferred(pet.id, foodId, isPreferred);
    }
  };

  const petSchema = z.object({
    name: z.string().min(2, {message: "Name must be at least 2 characters."}),
    breed: z.string().optional(),
    birthday: z.date().optional(),
    weight: z.coerce.number().min(1),
    size: z.enum(['small', 'medium', 'large']),
    bodyFitState: z.coerce.number().min(1).max(5),
    activityType: z.enum(['walking', 'running', 'mixed']).optional(),
    activityDuration: z.coerce.number().min(5).max(240).optional(),
  });

  const form = useForm<z.infer<typeof petSchema>>({
    resolver: zodResolver(petSchema),
  }
  );
  useEffect(() => {
    form.setValue('name', pet?.name || '');
    form.setValue('breed', pet?.breed || '');
    form.setValue('birthday', pet?.birthday || undefined);
    form.setValue('size', (pet?.size as "small" | "medium" | "large") || "medium");
    console.log('form:', form.getValues('name'), pet?.name, form.formState.defaultValues);
  }, [pet]);

  const handlePhotoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if(file) {
      const url = await fileToBase64(file);
      if(!url) return;

      updatePet({
        ...pet,
        image: url,
      });
    }
  };

  return (
    <div className="">
      <PageHeaderProfile />

      <Card className="border-none shadow-xs overflow-hidden bg-background mb-0 rounded-3xl">
        <div className={cn(
          "flex flex-col items-center justify-center text-gray-300 hover:text-gray-200 m-6",
          pet.image ? "relative" : "border-2 border-dashed border-secondary rounded-lg p-6 bg-secondary/5 text-secondary/80 hover:text-secondary m-6 mb-0",
        )}
          style={pet.image && {aspectRatio: '4/3'}}
        >
          {pet.image && (
            <img src={pet.image} alt={pet.name} className="absolute w-full h-full object-cover shadow-xl" />
          )}
          <div className="mt-2 flex text-sm">
            <label
              htmlFor="file-upload"
              className="relative cursor-pointer rounded-md font-medium"
            >
              <Camera className="mx-auto h-12 w-12" />
              {!pet.image && <span className="text-gray-800">Upload a photo</span>}
              <input
                id="file-upload"
                name="file-upload"
                type="file"
                className="sr-only"
                accept="image/*"
                onChange={handlePhotoUpload}
              />
            </label>
          </div>
        </div>

        <CardContent className="pb-28 pt-4 px-6">
          <Form {...form}>
            <div className="space-y-3 my-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center text-sm">Name:</div>
                {isEditingName ? (
                  <div className="flex items-center">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({field}) => (
                        <Input {...field} />
                      )}
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-secondary rounded-full"
                      onClick={handleSaveName}
                    >
                      Save
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center">
                    <span className="text-sm font-medium capitalize mr-2">{pet.name || 'Not set'}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-secondary rounded-full"
                      onClick={handleEditName}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>

              <div className="flex justify-between items-center">
                <div className="flex items-center text-sm">Breed:</div>
                {isEditingBreed ? (
                  <div className="flex items-center">
                    <BreedSelector
                      control={form.control}
                      name="breed"
                      showLabel={false}
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-secondary rounded-full"
                      onClick={handleSaveBreed}
                    >
                      Save
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center">
                    <span className="text-sm font-medium capitalize mr-2">{pet.breed || 'Not set'}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-secondary rounded-full"
                      onClick={handleEditBreed}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>

              <div className="flex justify-between items-center">
                <div className="flex items-center text-sm">Size:</div>
                <div className="flex items-center">
                  <span className="text-sm font-medium capitalize mr-2">{pet.size || 'Not set'}</span>
                  <EditDogProfileModal
                    pet={pet}
                    field="size"
                    onSave={(newSize) => {
                      if(newSize) {
                        updatePet({
                          ...pet,
                          size: newSize as "small" | "medium" | "large",
                        });
                      }
                    }}
                    onCancel={() => { }}
                  />
                </div>
              </div>

              <div className="flex justify-between items-center">
                <div className="flex items-center text-sm">Birthday:</div>
                <div className="flex items-center">
                  <span className="text-sm font-medium capitalize mr-2">{pet.birthday ? formatDate(pet.birthday) : 'Not set'}</span>
                  <EditDogProfileModal
                    pet={pet}
                    field="birthday"
                    onSave={(newBirthday) => {
                      if(newBirthday) {
                        updatePet({
                          ...pet,
                          birthday: newBirthday as Date,
                        });
                      }
                    }}
                    onCancel={() => { }}
                  />
                </div>
              </div>
            </div>
          </Form>

          {/* <Button
                variant="outline"
                className="w-full justify-start"
                onClick={handleViewResults}
              >
                <ChartPieIcon className="mr-2 h-4 w-4" />
                {t.common.viewResults}
              </Button> */}
          <br />
          <br />
          <Button
            variant="destructive"
            className="w-full text-white bg-pet-red hover:bg-pet-red/90"
            onClick={() => setShowDeleteDialog(true)}
          >
            {/* <Trash className="mr-2 h-4 w-4" /> */}
            {t.common.delete} {t.common.dogProfile}
          </Button>
        </CardContent>
      </Card>

      {/* Delete confirmation dialog */}
      <DeleteDogDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onDelete={handleDelete}
        dogName={pet.name}
      />

      {/* Food Management Section */}
      {/* <div className="px-5 py-4 border-t">
        <FoodManagement
          petFoods={pet.preferredFoods || []}
          foodHistory={pet.foodHistory}
          onFoodAdded={(food) => addPetFood(pet.id, food)}
          onFoodRemoved={(foodId) => removePetFood(pet.id, foodId)}
          onFoodPreferred={handleFoodPreferred}
        />
      </div> */}
    </div>
  );
};

export default DogProfilePage;
