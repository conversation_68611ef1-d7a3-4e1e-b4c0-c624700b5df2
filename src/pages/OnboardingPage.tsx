import React, {useEffect} from 'react'
import {useNavigate, useLocation} from 'react-router-dom'

import {useToast} from '@/hooks/use-toast'
import OnboardingSection from '@/components/onboarding/OnboardingSection'
import {useLocalData} from '@/hooks/useLocalData'
import {Pet} from '@/data/types'

const OnboardingPage = () => {
  const {toast} = useToast();
  const location = useLocation();
  const navigate = useNavigate();
  const {addPet, pets} = useLocalData();

  useEffect(() => {
    if(pets.length > 0 &&
      location.pathname === '/onboarding') {
      navigate('/');
    }
  }, [location.pathname, pets]);

  React.useEffect(() => {
    // Show welcome toast only on first launch
    if(pets.length === 0) {
      toast({
        title: "Welcome to My Dog In Fit!",
        description: "Let's set up your dog's profile to get started.",
        duration: 5000,
      });
    }
  }, [toast, pets.length]);

  const handleComplete = (petData: Omit<Pet, 'id'>) => {
    // Save the pet data using the addPet function from useLocalData hook
    // const newPet = addPet(petData);

    // Set onboarding as completed
    // localStorage.setItem('onboardingCompleted', 'true');

    // Navigate to results page with the new pet's ID
    // navigate(`/results?petId=${newPet.id}`);

    // toast({
    //   title: "Profile created!",
    //   description: `${petData.name}'s profile has been successfully created.`,
    // });
  };

  return (
    <div className="pb-20">
      <div className="pt-8 px-6 text-center mb-6">
        <h1 className="text-2xl font-bold mb-2">Welcome to My Dog In Fit</h1>
        <p className="text-gray-600">
          Let's create your dog's profile to get personalized fitness recommendations.
        </p>
      </div>

      <OnboardingSection onComplete={handleComplete} />
    </div>
  );
};

export default OnboardingPage;
