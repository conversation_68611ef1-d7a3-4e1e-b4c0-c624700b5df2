
import React, {useState} from 'react'
import {ArrowLeft, Calendar, Plus, Activity, ChevronLeft} from 'lucide-react'
import {Button} from '@/components/ui/button'
import {Card, CardContent} from '@/components/ui/card'
import {activities} from '@/data/mockData'
import {Tabs, TabsContent, TabsList, TabsTrigger} from '@/components/ui/tabs'
import {format} from 'date-fns'
import ActivityCard from '@/components/ActivityCard'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import ActivityHistoryChart from '@/components/ActivityHistoryChart'
import {useLocalData} from '@/hooks/useLocalData'

const ActivityHistoryPage = () => {
  const {getPetById, selectedPetId, } = useLocalData();
  const pet = getPetById(selectedPetId);
  const [selectedTab, setSelectedTab] = useState('list');
  const [timeframe, setTimeframe] = useState<'week' | 'month' | 'year'>('week');

  // Filter activities for the selected pet
  const petActivities = activities
    .filter(activity => activity.petId === selectedPetId)
    .sort((a, b) => b.date.getTime() - a.date.getTime());

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'});
  };

  // Group activities by date for the list view
  const groupedActivities = petActivities.reduce((acc, activity) => {
    const dateString = format(activity.date, 'yyyy-MM-dd');
    if(!acc[dateString]) {
      acc[dateString] = [];
    }
    acc[dateString].push(activity);
    return acc;
  }, {} as Record<string, typeof petActivities>);

  const groupedDates = Object.keys(groupedActivities).sort().reverse();

  // Calculate activity stats
  const calculateTotalMinutes = (period: 'week' | 'month') => {
    const today = new Date();
    const cutoffDate = new Date();

    if(period === 'week') {
      cutoffDate.setDate(today.getDate() - 7);
    } else {
      cutoffDate.setMonth(today.getMonth() - 1);
    }

    return petActivities
      .filter(activity => activity.date >= cutoffDate)
      .reduce((sum, activity) => sum + activity.duration, 0);
  };

  const weeklyMinutes = calculateTotalMinutes('week');
  const monthlyMinutes = calculateTotalMinutes('month');
  const dailyAvg = Math.round(weeklyMinutes / 7);
  const isAndroid = /Android/i.test(navigator.userAgent);

  return (
    <div className="pb-20">
      {/* Header */}
      <div className="bg-linear-to-b from-pet-blue to-pet-lightBlue text-white p-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            {isAndroid ? <ArrowLeft className="h-6 w-6 mr-2" onClick={() => window.history.back()} /> : <ChevronLeft className="h-6 w-6 mr-2" onClick={() => window.history.back()} />}
            <h1 className="text-xl font-bold">Activity History</h1>
          </div>
          <Button size="icon" variant="ghost" className="text-white">
            <Plus className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Activity Summary */}
      <div className="px-5 py-6">
        <Card className="bg-pet-lightBlue border-none">
          <CardContent className="p-5">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-bold">Activity Summary</h2>
              <Activity className="h-5 w-5 text-pet-blue" />
            </div>

            <div className="grid grid-cols-3 gap-2 mt-4">
              <div className="text-center p-3 bg-white rounded-lg">
                <p className="text-sm text-gray-500">This Week</p>
                <p className="font-bold text-pet-blue">{weeklyMinutes} min</p>
              </div>
              <div className="text-center p-3 bg-white rounded-lg">
                <p className="text-sm text-gray-500">This Month</p>
                <p className="font-bold text-pet-blue">{monthlyMinutes} min</p>
              </div>
              <div className="text-center p-3 bg-white rounded-lg">
                <p className="text-sm text-gray-500">Daily Avg</p>
                <p className="font-bold text-pet-blue">{dailyAvg} min</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activity Chart */}
      <div className="px-5 mb-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-md font-bold">Activity Trend</h3>
              <div className="flex space-x-1">
                <Button
                  variant={timeframe === 'week' ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTimeframe('week')}
                >
                  Week
                </Button>
                <Button
                  variant={timeframe === 'month' ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTimeframe('month')}
                >
                  Month
                </Button>
                <Button
                  variant={timeframe === 'year' ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTimeframe('year')}
                >
                  Year
                </Button>
              </div>
            </div>
            <ActivityHistoryChart activities={petActivities} timeframe={timeframe} />
          </CardContent>
        </Card>
      </div>

      {/* Activity Tabs */}
      <div className="px-5">
        <Tabs defaultValue="list" onValueChange={setSelectedTab} className="w-full">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="list">List View</TabsTrigger>
            <TabsTrigger value="table">Table View</TabsTrigger>
          </TabsList>

          {/* List View */}
          <TabsContent value="list">
            {petActivities.length === 0 ? (
              <div className="text-center py-10">
                <Activity className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No activity history found</p>
              </div>
            ) : (
              <div>
                {groupedDates.map(dateString => (
                  <div key={dateString} className="mb-6">
                    <div className="flex items-center mb-3">
                      <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                      <h3 className="text-sm font-medium text-gray-500">
                        {format(new Date(dateString), 'EEEE, MMMM d, yyyy')}
                      </h3>
                    </div>
                    <div className="space-y-3">
                      {groupedActivities[dateString].map(activity => (
                        <ActivityCard
                          key={activity.id}
                          icon={<Activity className="h-5 w-5 text-pet-blue" />}
                          title={activity.type}
                          time={formatTime(activity.date)}
                          duration={`${activity.duration} min`}
                          calories={activity.calories}
                          color="bg-pet-lightBlue"
                        />
                      ))}
                    </div>
                  </div>
                ))}

                <Pagination className="mt-6">
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious href="#" />
                    </PaginationItem>
                    <PaginationItem>
                      <PaginationLink href="#" isActive>1</PaginationLink>
                    </PaginationItem>
                    <PaginationItem>
                      <PaginationLink href="#">2</PaginationLink>
                    </PaginationItem>
                    <PaginationItem>
                      <PaginationLink href="#">3</PaginationLink>
                    </PaginationItem>
                    <PaginationItem>
                      <PaginationNext href="#" />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </TabsContent>

          {/* Table View */}
          <TabsContent value="table">
            <Card className="overflow-hidden border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Activity</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead className="hidden sm:table-cell">Calories</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {petActivities.length > 0 ? (
                    petActivities.map((activity) => (
                      <TableRow key={activity.id}>
                        <TableCell>
                          {format(activity.date, 'MMM dd, yyyy')}
                          <div className="text-xs text-gray-500">
                            {formatTime(activity.date)}
                          </div>
                        </TableCell>
                        <TableCell>{activity.type}</TableCell>
                        <TableCell>{activity.duration} min</TableCell>
                        <TableCell className="hidden sm:table-cell">{activity.calories} kcal</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-4 text-gray-500">
                        No activities found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </Card>

            <Pagination className="mt-6">
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious href="#" />
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink href="#" isActive>1</PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink href="#">2</PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink href="#">3</PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationNext href="#" />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default ActivityHistoryPage;
