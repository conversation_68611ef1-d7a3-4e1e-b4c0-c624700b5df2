
import React, {useState} from 'react'
import {ArrowLeft, Calendar, Plus, Weight, Share} from 'lucide-react'
import {Button} from '@/components/ui/button'
import {Card, CardContent} from '@/components/ui/card'
import WeightHistoryTable from '@/components/WeightHistoryTable'
import {Tabs, TabsContent, TabsList, TabsTrigger} from '@/components/ui/tabs'
import {format} from 'date-fns'
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import {
  Line,
  LineChart,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import {getUserProfile} from '@/lib/localStorageHelpers'
import {formatWeight, convertToDisplayUnit} from '@/utils/unitConversion'
import {useLanguage} from '@/hooks/useLanguage'
import {useLocalData} from '@/hooks/useLocalData'

const WeightHistoryPage = () => {
  const {getPetById, selectedPetId, } = useLocalData();
  const pet = getPetById(selectedPetId);
  const userProfile = getUserProfile();
  const unitSystem = (userProfile?.unitSystem as 'metric' | 'imperial') || 'metric';
  const {t} = useLanguage();

  // Mock weight history data (would come from API/database in a real app)
  const weightHistory = [
    {date: new Date(2023, 3, 1), weight: 32.5},
    {date: new Date(2023, 3, 15), weight: 32.0},
    {date: new Date(2023, 4, 1), weight: 31.2},
    {date: new Date(2023, 4, 15), weight: 30.8},
    {date: new Date(2023, 5, 1), weight: 30.4},
    {date: new Date(2023, 5, 15), weight: 30.0}
  ];

  // Format data for the chart, converting to display units
  const chartData = weightHistory.map(record => ({
    date: format(record.date, 'MMM dd'),
    weight: convertToDisplayUnit(record.weight, unitSystem)
  }));

  // Calculate the min and max values for the chart to set appropriate Y-axis bounds
  const weights = chartData.map(record => record.weight);
  const minWeight = Math.floor(Math.min(...weights) - 1);
  const maxWeight = Math.ceil(Math.max(...weights) + 1);

  // Format current and target weights for display
  const currentWeightDisplay = formatWeight(pet.weight, unitSystem);
  const targetWeightDisplay = pet.targetWeight ?
    formatWeight(pet.targetWeight, unitSystem) : null;

  // Calculate progress for display
  const progressDisplay = pet.targetWeight && pet.weight > pet.targetWeight ?
    formatWeight(pet.weight - pet.targetWeight, unitSystem) : '0 ' + (unitSystem === 'metric' ? 'kg' : 'lb');

  const handleShare = () => {
    // In a real app, this would open share dialog
    if(navigator.share) {
      navigator.share({
        title: `${pet.name}'s Fitness Journey`,
        text: `Check out ${pet.name}'s progress on My Dog In Fit!`,
        url: window.location.href,
      });
    }
  };

  return (
    <div className="pb-20">
      {/* Header */}
      <div className="bg-linear-to-b from-pet-purple to-pet-darkPurple text-white p-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <ArrowLeft className="h-6 w-6 mr-2" />
            <h1 className="text-xl font-bold">{t.pages.weightHistory}</h1>
          </div>
          <Button size="icon" variant="ghost" className="text-white">
            <Plus className="h-5 w-5" />
          </Button>
        </div>
      </div>


      <div className="flex justify-between mt-4">
        <Button variant="outline" size="sm" onClick={handleShare}>
          <Share className="h-4 w-4 mr-2" />
          Share Progress
        </Button>
      </div>

      {/* Weight Summary */}
      <div className="px-5 py-6">
        <Card className="bg-pet-green border-none">
          <CardContent className="p-5">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-bold">{t.weightHistory.weightSummary}</h2>
              <Weight className="h-5 w-5 text-pet-purple" />
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-2 mt-4">
                <div className="text-center p-3 bg-white rounded-lg">
                  <p className="text-sm text-gray-500">{t.weightHistory.current}</p>
                  <p className="font-bold text-pet-purple">{currentWeightDisplay}</p>
                </div>
                {pet.targetWeight && (
                  <div className="text-center p-3 bg-white rounded-lg">
                    <p className="text-sm text-gray-500">{t.weightHistory.target}</p>
                    <p className="font-bold text-pet-purple">{targetWeightDisplay}</p>
                  </div>
                )}
                <div className="text-center p-3 bg-white rounded-lg">
                  <p className="text-sm text-gray-500">{t.weightHistory.progress}</p>
                  <p className="font-bold text-pet-purple">{progressDisplay}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Weight Chart */}
      <div className="px-5 mb-4">
        <Card>
          <CardContent className="p-4">
            <h3 className="text-md font-bold mb-4">{t.weightHistory.weightTrend}</h3>
            <ChartContainer
              className="h-[200px]"
              config={{
                weight: {
                  label: `${t.common.weight} (${unitSystem === 'metric' ? t.units.kg : t.units.lb})`,
                  color: "#9b87f5"
                }
              }}
            >
              <LineChart data={chartData} margin={{top: 5, right: 20, bottom: 5, left: 0}}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="date"
                  tick={{fontSize: 12}}
                  tickLine={{stroke: "#e2e8f0"}}
                  axisLine={{stroke: "#e2e8f0"}}
                />
                <YAxis
                  domain={[minWeight, maxWeight]}
                  tick={{fontSize: 12}}
                  tickLine={{stroke: "#e2e8f0"}}
                  axisLine={{stroke: "#e2e8f0"}}
                />
                <Tooltip content={<ChartTooltipContent />} />
                <Line
                  type="monotone"
                  dataKey="weight"
                  stroke="#9b87f5"
                  strokeWidth={2}
                  name="weight"
                  dot={{stroke: "#9b87f5", strokeWidth: 2, r: 4, fill: "#ffffff"}}
                  activeDot={{r: 6, stroke: "#6E59A5", fill: "#9b87f5"}}
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* Weight Log Table */}
      <div className="px-5">
        <h3 className="text-md font-bold mb-4">{t.weightHistory.weightLogs}</h3>
        <WeightHistoryTable petId={selectedPetId} unitSystem={unitSystem} />
        <div className="flex justify-center mt-4">
          <Button>
            <Plus className="h-4 w-4 mr-2" /> {t.weightHistory.logNewWeight}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default WeightHistoryPage;
