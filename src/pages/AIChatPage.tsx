import React, {useState, useRef, useEffect} from 'react'
import {Send, ArrowLeft, Image as ImageIcon} from 'lucide-react'
import {Button} from '@/components/ui/button'
import {Input} from '@/components/ui/input'
import {useNavigate} from 'react-router-dom'
import {useToast} from '@/hooks/use-toast'
import {Avatar, AvatarFallback, AvatarImage} from '@/components/ui/avatar'
import {cn} from '@/lib/utils'
import {geminiService} from '@/services/geminiService'
import {useLocalData} from '@/hooks/useLocalData'
import {PageHeaderProfile} from '@/components/PageHeader'

// Define message types
interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  imageUrl?: string;
}

// Storage key for chat history with pet ID placeholder
const CHAT_HISTORY_KEY_PREFIX = 'myDogInFit_chatHistory_';

const AIChatPage = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();
  const {toast} = useToast();

  const {
    // pets,
    selectedPetId,
  } = useLocalData();
  const {getPetById} = useLocalData();
  const pet = getPetById(selectedPetId);
  const getChatHistoryKey = (petId: string) => `${CHAT_HISTORY_KEY_PREFIX}${petId}`;
  const defaultMessage: Message = {
    id: '1',
    content: `Hello! I'm your AI dog consultant. I can help with questions about ${pet?.name || 'your dog'}'s breed, training, nutrition and health. How can I assist you and your furry friend today?`,
    role: 'assistant',
    timestamp: new Date(),
  };

  // Load chat history from localStorage for the selected pet
  useEffect(() => {
    if(!selectedPetId) return;

    try {
      const savedMessages = localStorage.getItem(getChatHistoryKey(selectedPetId));
      if(savedMessages) {
        // Parse the saved messages and convert timestamp strings back to Date objects
        const parsedMessages = JSON.parse(savedMessages).map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }));
        setMessages(parsedMessages);
      } else {
        // Add initial greeting message if no history exists for this pet
        setMessages([defaultMessage]);
      }
    } catch(error) {
      console.error("Error loading chat history:", error);
      // If there's an error, start with the initial greeting
      setMessages([defaultMessage]);
    }
  }, [selectedPetId, pet]);

  // Save messages to localStorage whenever they change
  useEffect(() => {
    if(messages.length > 0 && selectedPetId) {
      localStorage.setItem(getChatHistoryKey(selectedPetId), JSON.stringify(messages));
    }
  }, [messages, selectedPetId]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({behavior: 'smooth'});
  }, [messages]);

  const handleSendMessage = async () => {
    if((!input.trim() && !imageFile) || isLoading || !pet) return;

    let userImageUrl = '';

    // Process image if present
    if(imageFile) {
      userImageUrl = URL.createObjectURL(imageFile);
    }

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: input,
      role: 'user',
      timestamp: new Date(),
      imageUrl: userImageUrl || undefined,
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setImageFile(null);
    setImagePreview(null);
    setIsLoading(true);

    try {
      // Convert image to base64 if present
      let imageBase64 = '';
      if(imageFile) {
        imageBase64 = await fileToBase64(imageFile);
      }

      // Format previous messages for context
      const messageHistory = messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        imageData: msg.imageUrl
      }));

      // Add current message
      messageHistory.push({
        role: 'user',
        content: input,
        imageData: imageBase64
      });

      // Call Gemini API with pet data
      const response = await geminiService.sendMessage(messageHistory, pet, imageBase64);

      // Add AI response
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response.text,
        role: 'assistant',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, aiMessage]);

      // If breed was detected, show toast
      if(response.breedDetection) {
        toast({
          title: "Breed Detected",
          description: `Identified as ${response.breedDetection.breed} with ${Math.round(response.breedDetection.confidence * 100)}% confidence`,
        });
      }
    } catch(error) {
      toast({
        title: "Error",
        description: "Failed to get a response. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if(e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if(file) {
      if(file.type.startsWith('image/')) {
        setImageFile(file);
        setImagePreview(URL.createObjectURL(file));
      } else {
        toast({
          title: "Invalid file type",
          description: "Please upload an image file.",
          variant: "destructive",
        });
      }
    }
  };

  const triggerImageUpload = () => {
    fileInputRef.current?.click();
  };

  const removeImage = () => {
    setImageFile(null);
    setImagePreview(null);
    if(fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Clear chat history for the current pet
  const clearChatHistory = () => {
    if(!selectedPetId) return;

    localStorage.removeItem(getChatHistoryKey(selectedPetId));
    setMessages([
      {
        id: '1',
        content: `Hello! I'm your AI dog consultant powered by Gemini Flash 2.5. I can help with questions about ${pet?.name || 'your dog'}'s breed, training, nutrition, and health. I can even identify dog breeds from photos! How can I assist you and your furry friend today?`,
        role: 'assistant',
        timestamp: new Date(),
      },
    ]);
    toast({
      title: "Chat Cleared",
      description: `Chat history for ${pet?.name || 'your pet'} has been cleared.`,
    });
  };

  return (
    <div className="flex flex-col h-screen pb-20">
      <PageHeaderProfile />
      {/* <div className="px-6 text-black flex items-center mb-4">
        <h1 className="text-xl font-bold">AI Dog Consultant</h1>
      </div> */}

      {/* Chat messages */}
      <div className="flex-1 overflow-y-auto px-6 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={cn(
              "flex",
              message.role === 'user' ? "justify-end" : "justify-start"
            )}
          >
            <div
              className={cn(
                "max-w-[80%] rounded-lg p-3",
                message.role === 'user'
                  ? "bg-background text-black rounded-tr-none"
                  : "bg-gray-100/90 text-gray-800 rounded-tl-none"
              )}
            >
              {message.role === 'assistant' && (
                <div className="flex items-center mb-2">
                  <Avatar className="h-6 w-6 mr-2">
                    <AvatarImage src="/ai-avatar.png" />
                    <AvatarFallback className="bg-primary/70 text-black text-xs">AI</AvatarFallback>
                  </Avatar>
                  <span className="text-xs font-medium">AI Dog Consultant</span>
                </div>
              )}

              {message.imageUrl && (
                <div className="mb-2">
                  <img
                    src={message.imageUrl}
                    alt="Uploaded"
                    className="rounded-md max-h-48 max-w-full"
                  />
                </div>
              )}

              <p className="whitespace-pre-wrap">{message.content}</p>

              <div className="text-xs opacity-70 mt-1 text-right">
                {message.timestamp.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'})}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Image preview */}
      {imagePreview && (
        <div className="px-6 bg-gray-100 border-t">
          <div className="relative inline-block">
            <img
              src={imagePreview}
              alt="Preview"
              className="h-16 rounded-md"
            />
            <button
              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center"
              onClick={removeImage}
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Input area */}
      <div className="pl-3 pr-6 pt-2 pb-0">
        <div className="flex items-center justify-between gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={triggerImageUpload}
            className="p-0 m-0 h-9 w-9"
          >
            <ImageIcon className="p-0 m-0" />
          </Button>

          <Input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Ask about dog care, training or upload a photo..."
            className="flex-1 h-8 border-2 border-white/25 focus:border-white/35 bg-white/90"
            disabled={isLoading}
          />

          <Button
            onClick={handleSendMessage}
            disabled={(!input.trim() && !imageFile) || isLoading}
            className="bg-primary hover:bg-primary/90 shadow-xs shrink-0 text-black rounded-full w-9 h-9"
          >
            <Send className="h-5 w-5" />
          </Button>
        </div>

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleImageUpload}
          accept="image/*"
          className="hidden"
        />

        {isLoading && (
          <div className="text-sm text-gray-500 mt-2">
            AI is thinking...
          </div>
        )}
      </div>
    </div>
  );
};

export default AIChatPage;

// Add this helper function to convert File to base64
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
};
