
import React, {useState, useEffect} from 'react'
import {useToast} from '@/hooks/use-toast'
import useAuth from '@/hooks/useAuth'
import {handleLogout} from '@/lib/authHelpers'
import {getUserProfile, saveUserProfile} from '@/lib/localStorageHelpers'
import AuthDialog from '@/components/AuthDialog'
import {useLanguage} from '@/hooks/useLanguage'

// Import components
import {PageHeader} from '@/components/PageHeader'
import PetProfilesSection from '@/components/settings/PetProfilesSection'
import NotificationsSection from '@/components/settings/NotificationsSection'
import AppSettingsSection from '@/components/settings/AppSettingsSection'
import AccountSection from '@/components/settings/AccountSection'

// Unit type definition
type UnitType = 'metric' | 'imperial';

const SettingsPage = () => {
  const {toast} = useToast();
  const {user, isAuthenticated, isLoading, shouldPromptLogin, markLoginPrompted} = useAuth();
  const [authDialogOpen, setAuthDialogOpen] = useState(false);
  const [userProfile, setUserProfile] = useState(getUserProfile());
  const [unitSystem, setUnitSystem] = useState<UnitType>((userProfile?.unitSystem as UnitType) || 'metric');
  const {t, format} = useLanguage();

  useEffect(() => {
    // Load user profile from local storage
    const profile = getUserProfile();
    setUserProfile(profile);
    setUnitSystem((profile?.unitSystem as UnitType) || 'metric');

    // Show login dialog if needed (and not already logged in)
    if(shouldPromptLogin && !isAuthenticated && !isLoading) {
      setAuthDialogOpen(true);
      markLoginPrompted();
    }
  }, [user, shouldPromptLogin, isAuthenticated, isLoading, markLoginPrompted]);

  const handleNotificationToggle = (enabled: boolean) => {
    const updatedProfile = {
      ...userProfile,
      notificationsEnabled: enabled
    };
    saveUserProfile(updatedProfile);
    setUserProfile(updatedProfile);
  };

  const handleUnitSystemChange = (system: UnitType) => {
    const updatedProfile = {
      ...userProfile,
      unitSystem: system
    };
    saveUserProfile(updatedProfile);
    setUserProfile(updatedProfile);
    setUnitSystem(system);

    // toast({
    //   title: t.common.unitsUpdated,
    //   description: format('common.unitsUpdatedMsg', {
    //     system: system === 'metric' ? t.settings.metric.toLowerCase() : t.settings.imperial.toLowerCase()
    //   })
    // });
  };

  const handleLogoutClick = async () => {
    const success = await handleLogout();
    if(success) {
      toast({
        title: t.common.loggedOut,
        description: t.common.loggedOutSuccess
      });
    }
  };

  const handleLoginClick = () => {
    setAuthDialogOpen(true);
  };

  return (
    <div className="pb-20">
      {/* Header */}
      <PageHeader
        title={t.pages.settings}
        showBackButton={false}
      />

      {/* Settings Content */}
      <div className="px-6 pt-4">
        {/* Pet Profiles Section */}
        {/* <PetProfilesSection /> */}

        {/* Notifications Section */}
        <NotificationsSection
          userProfile={userProfile}
          onNotificationToggle={handleNotificationToggle}
        />

        {/* App Settings Section */}
        <AppSettingsSection
          unitSystem={unitSystem}
          onUnitSystemChange={handleUnitSystemChange}
        />

        {/* Account Section */}
        <AccountSection
          user={user}
          isAuthenticated={isAuthenticated}
          onLogoutClick={handleLogoutClick}
          onLoginClick={handleLoginClick}
        />
      </div>

      {/* Authentication Dialog */}
      <AuthDialog
        open={authDialogOpen}
        onOpenChange={setAuthDialogOpen}
        onSuccess={() => {
          toast({
            title: t.common.authSuccess,
            description: t.common.authSuccessMsg
          });
        }}
      />
    </div>
  );
};

export default SettingsPage;
