
import React, {useEffect, useState} from 'react'
import {useNavigate, useSearchParams} from 'react-router-dom'
import {Button} from '@/components/ui/button'
import {
  Activity,
  Weight,
  Heart,
  Dumbbell,
  Calendar,
  Droplet,
  Package,
} from 'lucide-react';
import MetricCard from '@/components/MetricCard'
import ProgressRing from '@/components/ProgressRing'
import {useLocalData} from '@/hooks/useLocalData'
import {Pet, PetFood} from '@/data/types'
import {calculateDogMetrics} from '@/utils/petCalculations'

const ResultsPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const petId = searchParams.get('petId');
  const {getPetById, selectedPetId,
    selectPet,
  } = useLocalData();
  const pet = getPetById(petId || selectedPetId);
  // const [dogMetrics, setDogMetrics] = useState<any>(null);
  const dogMetrics = calculateDogMetrics(pet);

  // useEffect(() => {
  //   if(petId) {
  //     const foundPet = getPetById(petId);
  //     if(foundPet) {
  //       setPet(foundPet);
  //       // Calculate metrics based on the pet's data
  //       const metrics = calculateDogMetrics(foundPet);
  //       setDogMetrics(metrics);
  //     }
  //   }
  // }, [petId, getPetById]);

  // if(!pet || !dogMetrics) {
  //   return (
  //     <div className="flex flex-col items-center justify-center h-screen">
  //       <p>Loading pet information...</p>
  //       <Button
  //         onClick={() => navigate('/')}
  //         className="mt-4"
  //       >
  //         Return to Home
  //       </Button>
  //     </div>
  //   );
  // }

  // Calculate the percentage difference between current and target BMI
  const bmiDifference = ((dogMetrics.bmi - dogMetrics.targetBmi) / dogMetrics.targetBmi) * 100;
  const needsToLoseWeight = bmiDifference > 0;

  // Function to render icon dynamically without using require
  const renderFoodIcon = (iconName: string) => {
    // Map of supported icons - extend this as needed
    const iconMap: Record<string, React.ReactNode> = {
      package: <Package className="h-5 w-5 text-white" />,
      activity: <Activity className="h-5 w-5 text-white" />,
      heart: <Heart className="h-5 w-5 text-white" />,
      dumbbell: <Dumbbell className="h-5 w-5 text-white" />,
      calendar: <Calendar className="h-5 w-5 text-white" />,
      droplet: <Droplet className="h-5 w-5 text-white" />,
      weight: <Weight className="h-5 w-5 text-white" />,
    };

    // Return the icon if it exists, or a default
    return iconMap[iconName.toLowerCase()] || <Package className="h-5 w-5 text-white" />;
  };

  return (
    <div className="pb-20 px-4 pt-6">
      <div className="text-center mb-6">
        <h1 className="text-2xl font-bold text-pet-purple">
          {pet.name}'s Fitness Plan
        </h1>
        <p className="text-gray-600">
          Based on your pet's profile, here's what we recommend
        </p>
      </div>

      <div className="space-y-8">
        {/* BCS Section */}
        <section>
          <h2 className="text-lg font-bold mb-4 flex items-center">
            <Weight className="mr-2 text-pet-purple" />
            Body Condition Score (BCS)
          </h2>

          <div className="flex justify-between space-x-4 mb-4">
            <div className="flex-1 flex flex-col items-center">
              <ProgressRing
                progress={pet.bodyFitState ? (pet.bodyFitState / 5) * 100 : 60}
                color={needsToLoseWeight ? "#FEC6A1" : "#9b87f5"}
                size={100}
                strokeWidth={10}
              >
                <div className="text-center">
                  <p className="text-xl font-bold">{pet.bodyFitState || 3}</p>
                  <p className="text-xs text-gray-500">Current</p>
                </div>
              </ProgressRing>
            </div>

            <div className="flex-1 flex flex-col items-center">
              <ProgressRing
                progress={60}
                color="#9b87f5"
                size={100}
                strokeWidth={10}
              >
                <div className="text-center">
                  <p className="text-xl font-bold">3</p>
                  <p className="text-xs text-gray-500">Target</p>
                </div>
              </ProgressRing>
            </div>
          </div>

          <div className="bg-gray-100 p-4 rounded-lg">
            <p className="text-sm">
              {pet.bodyFitState && pet.bodyFitState > 3 ?
                `${pet.name} has a BCS of ${pet.bodyFitState}/5, indicating they may be overweight. An ideal BCS is 3/5.` :
                pet.bodyFitState && pet.bodyFitState < 3 ?
                  `${pet.name} has a BCS of ${pet.bodyFitState}/5, indicating they may be underweight. An ideal BCS is 3/5.` :
                  `${pet.name} has an ideal BCS of 3/5. Keep up the good work!`
              }
            </p>
          </div>
        </section>

        {/* Diet Recommendations */}
        <section>
          <h2 className="text-lg font-bold mb-4 flex items-center">
            <Heart className="mr-2 text-pet-purple" />
            Daily Nutrition Plan
          </h2>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <MetricCard
              icon={<Heart className="h-5 w-5 text-pet-purple" />}
              title="Calories"
              value={dogMetrics.dailyCalories}
              unit="kcal"
              color="bg-pet-lightPurple"
            />
            <MetricCard
              icon={<Droplet className="h-5 w-5 text-pet-purple" />}
              title="Water"
              value={dogMetrics.waterIntake}
              unit="ml"
              color="bg-pet-lightPurple"
            />
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4">
            <h3 className="font-medium mb-2">Macronutrient Breakdown</h3>
            <div className="space-y-2">
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm font-medium">Protein</span>
                  <span className="text-sm text-gray-500">{dogMetrics.dietComposition.protein}%</span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full">
                  <div
                    className="h-2 bg-pet-purple rounded-full"
                    style={{width: `${dogMetrics.dietComposition.protein}%`}}
                  ></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm font-medium">Fat</span>
                  <span className="text-sm text-gray-500">{dogMetrics.dietComposition.fat}%</span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full">
                  <div
                    className="h-2 bg-pet-yellow rounded-full"
                    style={{width: `${dogMetrics.dietComposition.fat}%`}}
                  ></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm font-medium">Carbohydrates</span>
                  <span className="text-sm text-gray-500">{dogMetrics.dietComposition.carbs}%</span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full">
                  <div
                    className="h-2 bg-pet-green rounded-full"
                    style={{width: `${dogMetrics.dietComposition.carbs}%`}}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="font-medium mb-2">Recommended Foods</h3>
            <div className="grid grid-cols-1 gap-2">
              {dogMetrics.allFoods.map((food: PetFood, index: number) => {
                const isRecommended = dogMetrics.recommendedFoods.some((rec: any) => rec.name === food.name);
                return (
                  <div
                    key={index}
                    className={`flex items-center text-sm p-3 rounded-md ${isRecommended ? 'bg-green-50 border border-green-200' : ''}`}
                  >
                    <div className={`p-2 rounded-full ${isRecommended ? 'bg-green-500' : 'bg-gray-300'} mr-3`}>
                      {renderFoodIcon(food.icon)}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium">{food.name}</div>
                      <div className="flex items-center">
                        {food.logo && (
                          <img src={food.logo} alt={food.brand} className="h-4 mr-1" />
                        )}
                        <div className="text-xs text-gray-500">by {food.brand}</div>
                      </div>
                    </div>
                    {isRecommended &&
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-xs ml-auto">
                        Recommended
                      </span>
                    }
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Activity Recommendations */}
        <section>
          <h2 className="text-lg font-bold mb-4 flex items-center">
            <Dumbbell className="mr-2 text-pet-purple" />
            Daily Activity Plan
          </h2>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <MetricCard
              icon={<Calendar className="h-5 w-5 text-pet-purple" />}
              title="Activity Goal"
              value={dogMetrics.activityGoal}
              unit="min"
              color="bg-pet-green"
            />
            <MetricCard
              icon={<Activity className="h-5 w-5 text-pet-purple" />}
              title="Calorie Burn"
              value={dogMetrics.calorieExpenditure}
              unit="kcal"
              color="bg-pet-green"
            />
          </div>

          <div className="bg-gray-100 p-4 rounded-lg">
            <p className="text-sm">
              We recommend splitting {dogMetrics.activityGoal} minutes into {Math.ceil(dogMetrics.activityGoal / 15)} walks per day.
              Track each walk in the app to monitor progress and ensure {pet.name} stays active and healthy.
            </p>
          </div>
        </section>

        <Button
          onClick={() => navigate('/')}
          className="w-full"
        >
          Start Tracking
        </Button>
      </div>
    </div>
  );
};

export default ResultsPage;
