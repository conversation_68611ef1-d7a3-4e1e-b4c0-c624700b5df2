import React, {useState, useEffect} from 'react'
import {Link, useNavigate} from 'react-router-dom'
import {<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Dumbbell, Heart, Activity, Weight, Calendar, Dog, ChevronDown} from 'lucide-react'
import {Button} from '@/components/ui/button'
import {useToast} from '@/hooks/use-toast'
import {Card, CardContent} from '@/components/ui/card'
// import ProgressRing from '@/components/ProgressRing';
import MetricCard from '@/components/MetricCard'
import GoalProgressBar from '@/components/GoalProgressBar'
// import DogProfile from '@/components/DogProfile';
// import WalkTracker from '@/components/WalkTracker'
import DogRecommendationCard from '@/components/DogRecommendationCard'
import MealCard from '@/components/MealCard'
// import ActivityTypeSelector from '@/components/ActivityTypeSelector';
import TreatTracker from '@/components/TreatTracker'
import TodayDiet from '@/components/DailyDietCard'
import {useLocalData} from '@/hooks/useLocalData'
import {DogR<PERSON>ommendation} from '@/data/types'
import {PageHeaderProfile} from '@/components/PageHeader'

// Sample recommendations (in a real app these would come from the backend)
const sampleRecommendations: DogRecommendation[] = [
  {
    id: '1',
    text: 'Do not feed your dog before a walk. Wait at least 1 hour after feeding before any moderate to intensive exercise.',
    seen: false,
    category: 'diet'
  },
  {
    id: '2',
    text: 'Do not feed from the table. Human food can contain ingredients that are harmful to dogs and can lead to obesity.',
    seen: false,
    category: 'diet'
  },
  {
    id: '3',
    text: 'Provide constant access to fresh, clean water. This is especially important when increasing your dog\'s activity level.',
    seen: false,
    category: 'health'
  },
  {
    id: '4',
    text: 'Remember to choose food for your dog considering allergies. If you notice signs of food allergies (scratching, digestive issues), consult your vet.',
    seen: false,
    category: 'health'
  },
  {
    id: '5',
    text: 'Visit veterinary if your dog started to drink unusually little or, conversely, much more than usual. This could be a sign of underlying health issues.',
    seen: false,
    category: 'health'
  }
];

const HomePage = () => {
  const navigate = useNavigate();
  const {toast} = useToast();
  const {
    addTreat,
    adjustActivityBasedOnWalk,
    pets,
    getPetById,
    removeTreat,
    selectedPetId,
    selectPet,
  } = useLocalData(); // Get pets and new functions from useLocalData hook
  const [recommendations, setRecommendations] = useState<DogRecommendation[]>(sampleRecommendations);
  const [currentRecommendation, setCurrentRecommendation] = useState<DogRecommendation | null>(null);
  // const [localActivities, setLocalActivities] = useState(activities);
  // const [localMeals, setLocalMeals] = useState(meals);

  // Initialize the selected pet when pets are loaded - always called
  useEffect(() => {
    if(pets.length > 0 && !selectedPetId) {
      selectPet(pets[0].id);
    }
  }, [pets, selectedPetId]);

  // Always define selectedPet regardless of condition
  const selectedPet = getPetById(selectedPetId);

  // Always define these values regardless of condition
  // Default values for when no pet is selected
  const activityProgress = selectedPet ? Math.round((selectedPet.activityProgress / selectedPet.dailyActivityGoal) * 100) : 0;
  const calorieProgress = selectedPet ? Math.round((selectedPet.calorieProgress / selectedPet.dailyCalorieGoal) * 100) : 0;

  // Always define petMeals regardless of condition
  // const petMeals = selectedPet
  //   ? localMeals
  //     .filter(meal => meal.petId === selectedPetId)
  //     .sort((a, b) => a.time.localeCompare(b.time))
  //   : [];

  // Always define todayMeals regardless of condition
  // const todayMeals = selectedPet
  //   ? petMeals.filter(meal => {
  //     const today = new Date();
  //     const mealDate = new Date(meal.date);
  //     return (
  //       mealDate.getDate() === today.getDate() &&
  //       mealDate.getMonth() === today.getMonth() &&
  //       mealDate.getFullYear() === today.getFullYear()
  //     );
  //   })
  //   : [];

  // Show recommendations effect - always called
  useEffect(() => {
    // Only proceed if we have a selected pet
    if(!selectedPet) return;

    // Show a random recommendation
    const unseenRecommendations = recommendations.filter(rec => !rec.seen);
    if(unseenRecommendations.length > 0) {
      const randomIndex = Math.floor(Math.random() * unseenRecommendations.length);
      setCurrentRecommendation(unseenRecommendations[randomIndex]);
    }

    // Check if the user has completed onboarding, if not, show a notification to set up their pet profile
    // Use localStorage to track if we've shown the welcome notification before
    const hasCompletedOnboarding = localStorage.getItem('onboardingCompleted');
    const hasShownWelcomeNotification = localStorage.getItem('welcomeNotificationShown');

    if(!hasCompletedOnboarding && !hasShownWelcomeNotification) {
      toast({
        title: "Welcome to My Dog In Fit!",
        description: "Complete your dog's profile in the Settings section to get personalized recommendations.",
        duration: 6000,
      });

      // Mark that we've shown the welcome notification
      localStorage.setItem('welcomeNotificationShown', 'true');
    }
  }, [recommendations, toast, selectedPet]);

  const handleMarkRecommendationAsSeen = (id: string) => {
    setRecommendations(prev =>
      prev.map(rec =>
        rec.id === id ? {...rec, seen: true} : rec
      )
    );
    setCurrentRecommendation(null);

    // After a delay, show another recommendation if available
    setTimeout(() => {
      const unseenRecommendations = recommendations.filter(rec => !rec.seen && rec.id !== id);
      if(unseenRecommendations.length > 0) {
        const randomIndex = Math.floor(Math.random() * unseenRecommendations.length);
        setCurrentRecommendation(unseenRecommendations[randomIndex]);
      }
    }, 60000); // Show next recommendation after 1 minute
  };

  // const handleWalkComplete = (walkDetails: {duration: number; type: string; calories: number}) => {
  //   if(!selectedPet) return;

  //   // Add the new activity to the list
  //   const newActivity = {
  //     id: `act-${Date.now()}`,
  //     petId: selectedPetId,
  //     type: walkDetails.type.charAt(0).toUpperCase() + walkDetails.type.slice(1),
  //     duration: walkDetails.duration,
  //     calories: walkDetails.calories,
  //     date: new Date(),
  //     time: new Date().toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'})
  //   };

  //   setLocalActivities(prev => [newActivity, ...prev]);

  //   // Create a copy of the pet to avoid direct state mutation
  //   const updatedPet = {...selectedPet};
  //   updatedPet.activityProgress += walkDetails.duration;
  //   updatedPet.calorieProgress += walkDetails.calories;

  //   // Adjust tomorrow's recommendations based on today's activity
  //   adjustActivityBasedOnWalk(selectedPetId, walkDetails.duration, walkDetails.type);

  //   // Check if any achievements were unlocked
  //   if(updatedPet.activityProgress >= updatedPet.dailyActivityGoal) {
  //     toast({
  //       title: "Daily Goal Achieved! 🎉",
  //       description: `${updatedPet.name} has reached their activity goal for today!`,
  //     });
  //   }
  // };

  const handleAddTreat = () => {
    if(selectedPet) {
      addTreat(selectedPetId);
    }
  };

  const handleRemoveTreat = () => {
    if(selectedPet) {
      removeTreat(selectedPetId);
    }
  };

  const handleAddMeal = () => {
    navigate('/meals');
  };

  // Calculate diet impact text based on treats
  const getDietImpactText = () => {
    const adjustment = selectedPet?.dietAdjustment || 0;
    if(adjustment === 0) return "None";
    return `${adjustment > 0 ? '+' : ''}${adjustment}% calories`;
  };

  // Handle case when there are no pets
  if(!selectedPet) {
    return (
      <div className="flex flex-col items-center justify-center h-screen p-6">
        <h2 className="text-xl font-bold mb-4">Welcome to My Dog In Fit</h2>
        <p className="text-gray-500 mb-6 text-center">
          Track your dog's fitness, meals, and activities to keep them healthy and happy.
        </p>
        <Button
          onClick={() => navigate('/onboarding')}
          className="bg-pet-purple hover:bg-purple-700 w-full"
        >
          Add Your First Dog
        </Button>
      </div>
    );
  }

  const activeToday = selectedPet.activityProgress || 24;

  return (
    <div className="pb-20">
      <PageHeaderProfile />

      <div className="bg-background mb-4 pt-4 pb-1 px-6 rounded-3xl shadow-glow-sm">
        {/* Health Metrics */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4">Health Metrics</h3>
          <div className="grid grid-cols-2 gap-4">
            {/* {selectedPet.bcs && ( */}
            <MetricCard
              icon={<Heart className="h-5 w-5 text-white" />}
              title="BCS"
              value={(selectedPet.bcs || 7).toFixed(0)}
              unit=""
              color="orange"
            // color="blue"
            />
            {/* )} */}
            <Link to="/activities/history">
              <MetricCard
                icon={<Activity className="h-5 w-5 text-pet-purple" />}
                title="BMI"
                value={selectedPet.dailyActivityGoal}
                unit=""
                // color="purple"
                color="blue"
              />
            </Link>
            <Link to="/weight-history">
              <MetricCard
                icon={<Weight className="h-5 w-5 text-black/90" />}
                title="Weight"
                value={selectedPet.weight}
                unit="kg"
                color="pink"
              />
            </Link>
            <MetricCard
              icon={<CircleCheck className="h-5 w-5 text-pet-purple" />}
              title="Health Score"
              value="85"
              unit="%"
              color="yellow"
            />
          </div>
        </div>
        {/* </div>

      <div className="bg-background pt-4 pb-1 mb-20 rounded-t-3xl rounded-b-3xl shadow-glow-sm"> */}
        {/* Activity Summary */}
        <div className="mb-7">
          {/* <Card className="bg-pet-lightPurple border-none"> */}
          {/* <Card className="border-none"> */}
          {/* <CardContent className="p-5"> */}
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-bold">Today's Activity</h2>
            <Calendar className="h-5 w-5 text-primary" />
          </div>

          <div className="space-y-4">
            <GoalProgressBar
              title=""
              currentValue={activeToday}
              targetValue={selectedPet.dailyActivityGoal}
              unit="min"
            />

            {/* <div className="grid grid-cols-3 gap-2 mt-4">
                  <div className="text-center p-3 bg-white rounded-lg">
                    <p className="text-sm text-gray-500">Walked</p>
                    <p className="font-bold text-pet-purple">30 min</p>
                  </div>
                  <div className="text-center p-3 bg-white rounded-lg">
                    <p className="text-sm text-gray-500">Played</p>
                    <p className="font-bold text-pet-purple">15 min</p>
                  </div>
                  <div className="text-center p-3 bg-white rounded-lg">
                    <p className="text-sm text-gray-500">Trained</p>
                    <p className="font-bold text-pet-purple">20 min</p>
                  </div>
                </div> */}
          </div>
          <br />
          {/* </CardContent> */}
          {/* </Card> */}
        </div>

        {/* Daily Diet Card */}
        <div className="mb-7">
          <TodayDiet
            petName={selectedPet.name}
            dailyCalories={selectedPet.dailyCalorieGoal}
            preferredFoods={selectedPet.preferredFoods}
            dietAdjustment={selectedPet.dietAdjustment}
          />
        </div>

        {/* Treat Tracker */}
        <div className="my-2">
          <TreatTracker
            treatsCount={selectedPet.treatsToday || 0}
            onTreatAdded={handleAddTreat}
            onTreatRemoved={handleRemoveTreat}
            dietImpact={getDietImpactText()}
          />
        </div>

        {/* Weight and Target Info */}
        {/* <div className="mt-4">
          <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-xs">
            <div className="flex items-center">
              <Weight className="h-5 w-5 text-pet-purple mr-2" />
              <span className="font-medium">{selectedPet.weight} kg</span>
              {selectedPet.targetWeight && (
                <span className="ml-2 text-sm text-gray-500">
                  Target: {selectedPet.targetWeight} kg
                </span>
              )}
            </div>

            <Button
              variant="outline"
              size="sm"
              className="border-pet-purple text-pet-purple"
              onClick={handleViewDogProfile}
            >
              Update
            </Button>
          </div>
        </div> */}

        {/* Walk Tracker */}
        {/* <div className="px-5 mt-4 mb-6">
          <WalkTracker onWalkComplete={handleWalkComplete} />
        </div> */}

        {/* Nutrition Summary */}
        <div className="my-2">
          <Card className="border-none">
            <CardContent className="p-0">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-bold">Nutrition Summary</h2>
                <Calendar className="h-5 w-5 text-pet-purple" />
              </div>

              <div className="space-y-4">
                <GoalProgressBar
                  title="Daily Calories"
                  currentValue={selectedPet.calorieProgress}
                  targetValue={selectedPet.dailyCalorieGoal}
                  unit="kcal"
                />

                <div className="grid grid-cols-3 gap-2 mt-4">
                  <div className="text-center p-3 bg-white rounded-lg">
                    <p className="text-sm text-gray-500">Protein</p>
                    <p className="font-bold text-pet-purple">35%</p>
                  </div>
                  <div className="text-center p-3 bg-white rounded-lg">
                    <p className="text-sm text-gray-500">Fats</p>
                    <p className="font-bold text-pet-purple">25%</p>
                  </div>
                  <div className="text-center p-3 bg-white rounded-lg">
                    <p className="text-sm text-gray-500">Carbs</p>
                    <p className="font-bold text-pet-purple">40%</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Today's Meals */}
        {/* <div className="mb-7">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-bold">Today's Meals</h2>
            <Button
              variant="ghost"
              size="sm"
              className="text-pet-purple"
              onClick={handleAddMeal}
            >
              Add Meal
            </Button>
          </div>

          {todayMeals.length === 0 ? (
            <p className="text-gray-500 text-center py-4">No meals logged today</p>
          ) : (
            <div className="space-y-3">
              {todayMeals.slice(0, 3).map(meal => (
                <MealCard
                  key={meal.id}
                  icon={<Clock className="h-5 w-5 text-pet-purple" />}
                  title={meal.name}
                  time={meal.time}
                  food={meal.food}
                  portion={meal.portion}
                  calories={meal.calories}
                  color="bg-pet-yellow"
                />
              ))}
            </div>
          )}
        </div> */}
      </div>

      {/* Recommendation Card (if any) */}
      {currentRecommendation && (
        <div className="px-5 mb-6 fixed z-10 bottom-20">
          <DogRecommendationCard
            recommendation={currentRecommendation}
            onMarkAsSeen={handleMarkRecommendationAsSeen}
          />
        </div>
      )}
    </div>
  );
};

export default HomePage;
