
import React from 'react'
import {useNavigate} from 'react-router-dom'
import {useToast} from '@/hooks/use-toast'
import OnboardingSection from '@/components/onboarding/OnboardingSection'
import {useLocalData} from '@/hooks/useLocalData'
import {Pet} from '@/data/types'
import {PageHeader} from '@/components/PageHeader'

const AddDogPage = () => {
  const {toast} = useToast();
  const navigate = useNavigate();
  const {addPet} = useLocalData();

  const handleComplete = (petData: Omit<Pet, 'id'>) => {
    // Save the pet data using the addPet function from useLocalData hook
    // const newPet = addPet(petData);

    // toast({
    //   title: "Profile created!",
    //   description: `${petData.name}'s profile has been successfully created.`,
    // });

    // Navigate to results page with the new pet's ID
    // navigate(`/results?petId=${newPet.id}`);
  };

  return (
    <div className="pb-20">
      {/* Header */}
      <PageHeader
        title="Add New Pet"
        backPath="/"
      />

      <OnboardingSection onComplete={handleComplete} />
    </div>
  );
};

export default AddDogPage;
