
export interface Pet {
  id: string;
  name: string;
  type: string;
  breed?: string;
  birthday?: Date;
  weight: number;
  targetWeight?: number;
  image?: string;
  dailyCalorieGoal: number;
  dailyActivityGoal: number; // in minutes
  activityProgress: number; // in minutes
  calorieProgress: number;
  size?: 'small' | 'medium' | 'large' | string; // Updated to allow both types
  bodyFitState?: 1 | 2 | 3 | 4 | 5 | number; // Updated to allow both specific values and generic number
  bcs?: number;
  targetBmi?: number;
  typicalActivity?: {
    duration: number; // in minutes
    type: 'walking' | 'running' | 'mixed';
  };
  recommendedFoods?: string[];
  waterConsumption?: number; // in ml
  lastWeightUpdate?: Date | string;  // Updated to allow both Date and string
  lastPhotoUpdate?: Date | string;  // Updated to allow both Date and string
  achievements?: string[];
  order?: number; // Added for drag-and-drop ordering

  // Food-related properties
  preferredFoods?: PetFood[];
  foodHistory?: FoodHistoryEntry[];
  treatsToday?: number;
  dietAdjustment?: number; // percentage to adjust daily calories (positive or negative)
  activityAdjustment?: number; // percentage to adjust daily activity goal (positive or negative)
}

export interface PetFood {
  id: string;
  name: string;
  brand: string;
  caloriesPerCup: number;
  cupsPerDay?: number;
  icon: string;
  logo?: string;
  isPreferred: boolean;
  startDate?: Date | string;
}

export interface FoodHistoryEntry {
  foodId: string;
  name: string;
  brand: string;
  startDate: Date | string;
  endDate?: Date | string;
}

export interface Activity {
  id: string;
  petId: string;
  type: string;
  duration: number; // in minutes
  calories: number;
  date: Date;
  time?: string;
  notes?: string;
}

export interface Meal {
  id: string;
  petId: string;
  name: string;
  food: string;
  portion?: string;
  calories: number;
  date: Date;
  time: string;
  notes?: string;
}

export interface WeightLog {
  id: string;
  petId: string;
  weight: number;
  date: Date;
  notes?: string;
}

export interface DogRecommendation {
  id: string;
  text: string;
  seen: boolean;
  category: 'diet' | 'activity' | 'health' | 'general';
}

export interface UserProfile {
  email?: string;
  notificationsEnabled?: boolean;
  surveys?: boolean;
  newsletter?: boolean;
  unitSystem?: 'metric' | 'imperial'; // Added unitSystem property
}
