import { Pet, Activity, Meal } from "./types"

// Mock activities data
export const activities: Activity[] = [
  {
    id: "1",
    petId: "1",
    type: "Walk",
    duration: 30,
    calories: 150,
    date: new Date(),
    notes: "Morning walk in the park",
  },
  {
    id: "2",
    petId: "1",
    type: "Play",
    duration: 15,
    calories: 75,
    date: new Date(),
    notes: "Played fetch in the backyard",
  },
  {
    id: "3",
    petId: "2",
    type: "Play",
    duration: 10,
    calories: 40,
    date: new Date(),
    notes: "Played with toy mouse",
  },
  {
    id: "4",
    petId: "1",
    type: "Training",
    duration: 20,
    calories: 100,
    date: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
    notes: "Basic commands practice",
  },
];

// Mock meals data
export const meals: Meal[] = [
  {
    id: "1",
    petId: "1",
    name: "Breakfast",
    food: "Premium Dry Food",
    portion: "1 cup",
    calories: 350,
    date: new Date(),
    time: "08:00",
  },
  {
    id: "2",
    petId: "1",
    name: "Lunch",
    food: "Chicken & Rice",
    portion: "1.5 cups",
    calories: 450,
    date: new Date(),
    time: "13:00",
  },
  {
    id: "3",
    petId: "2",
    name: "Breakfast",
    food: "Salmon Cat Food",
    portion: "1/2 cup",
    calories: 120,
    date: new Date(),
    time: "07:30",
  },
  {
    id: "4",
    petId: "2",
    name: "Dinner",
    food: "Wet Cat Food",
    portion: "1/2 can",
    calories: 100,
    date: new Date(),
    time: "18:00",
  },
];
