use dioxus::prelude::*;
use crate::hooks::use_image_upload::{use_image_upload, ImageUploadState};

// Import base64 Engine trait for both platforms
use base64::Engine;

#[derive(Props, PartialEq, Clone)]
pub struct ImageUploadProps {
    #[props(optional)]
    pub label: Option<String>,
    #[props(optional)]  
    pub current_image: Option<String>,
    #[props(optional)]
    pub placeholder: Option<String>,
    pub on_image_selected: EventHandler<String>,
    #[props(optional)]
    pub dog_id: Option<String>,
}

pub fn ImageUpload(props: ImageUploadProps) -> Element {
    let image_upload = use_image_upload();
    
    let handle_file_change = move |evt: FormEvent| {
        // Handle web platform (WASM) - use the existing web file input approach
        #[cfg(target_family = "wasm")]
        {
            // For web platform, we need to implement proper FileReader API integration
            // This is a placeholder - the actual implementation would use web_sys FileReader
            tracing::warn!("Web file upload needs proper FileReader implementation");
            image_upload.state.set(ImageUploadState::Error("Web file upload not yet fully implemented".to_string()));
        }
        
        // Note: Non-WASM platforms use the mobile button handler instead
        #[cfg(not(target_family = "wasm"))]
        {
            tracing::debug!("File input change event on non-WASM platform - should use mobile handler");
        }
    };

    // Handle mobile file selection with native file picker
    let image_upload_for_mobile = image_upload.clone();
    let handle_mobile_file_select = move |_evt: MouseEvent| {
        #[cfg(not(target_family = "wasm"))]
        {
            let mut image_upload_clone = image_upload_for_mobile.clone();
            let dog_id_clone = props.dog_id.clone();
            
            spawn(async move {
                image_upload_clone.state.set(crate::hooks::use_image_upload::ImageUploadState::Uploading);
                
                match crate::hooks::use_image_upload::select_image_file().await {
                    Ok(Some((file_data, file_name))) => {
                        // Create base64 preview for mobile
                        let preview_data = base64::engine::general_purpose::STANDARD.encode(&file_data);
                        let data_url = format!("data:image/jpeg;base64,{preview_data}");
                        image_upload_clone.set_preview(data_url);
                        
                        // Upload file
                        image_upload_clone.upload_image(file_data, file_name, dog_id_clone);
                    }
                    Ok(None) => {
                        // User cancelled
                        image_upload_clone.state.set(crate::hooks::use_image_upload::ImageUploadState::Idle);
                    }
                    Err(error) => {
                        tracing::error!("Mobile file selection failed: {}", error);
                        image_upload_clone.state.set(crate::hooks::use_image_upload::ImageUploadState::Error(error));
                    }
                }
            });
        }
    };

    // Listen for successful uploads
    let on_image_selected = props.on_image_selected;
    use_effect(move || {
        let state = image_upload.state.read();
        if let ImageUploadState::Success(ref url) = *state {
            on_image_selected.call(url.clone());
        }
    });

    rsx! {
        div { class: "space-y-2",
            if let Some(label) = &props.label {
                label { class: "text-sm font-medium text-gray-700",
                    "{label}"
                }
            }
            
            div { class: "flex items-center space-x-4",
                // Preview area
                div { class: "w-24 h-24 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center overflow-hidden bg-gray-50",
                    if let Some(preview_url) = image_upload.preview_url.read().clone() {
                        img {
                            src: "{preview_url}",
                            alt: "Preview",
                            class: "w-full h-full object-cover"
                        }
                    } else if let Some(current_image) = &props.current_image {
                        img {
                            src: "{current_image}",
                            alt: "Current image",
                            class: "w-full h-full object-cover"
                        }
                    } else {
                        div { class: "text-center text-gray-500",
                            svg {
                                class: "w-8 h-8 mx-auto mb-1",
                                fill: "none",
                                stroke: "currentColor",
                                view_box: "0 0 24 24",
                                path {
                                    stroke_linecap: "round",
                                    stroke_linejoin: "round", 
                                    stroke_width: "2",
                                    d: "M12 6v6m0 0v6m0-6h6m-6 0H6"
                                }
                            }
                            p { class: "text-xs", "{props.placeholder.as_deref().unwrap_or(\"Add photo\")}" }
                        }
                    }
                }
                
                // Upload controls
                div { class: "flex-1",
                    // Platform-specific upload UI
                    {
                        #[cfg(target_family = "wasm")]
                        {
                            rsx! {
                                input {
                                    r#type: "file",
                                    accept: "image/*",
                                    class: "hidden",
                                    id: "image-upload-input",
                                    onchange: handle_file_change
                                }
                                
                                label {
                                    r#for: "image-upload-input",
                                    class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer",
                                    match image_upload.state.read().clone() {
                                        ImageUploadState::Uploading => rsx! {
                                            svg {
                                                class: "animate-spin -ml-1 mr-2 h-4 w-4",
                                                fill: "none",
                                                view_box: "0 0 24 24",
                                                circle {
                                                    class: "opacity-25",
                                                    cx: "12",
                                                    cy: "12",
                                                    r: "10",
                                                    stroke: "currentColor",
                                                    stroke_width: "4"
                                                }
                                                path {
                                                    class: "opacity-75",
                                                    fill: "currentColor",
                                                    d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                                }
                                            }
                                            "Uploading..."
                                        },
                                        ImageUploadState::Success(_) => rsx! { "Change Photo" },
                                        _ => rsx! { "Choose Photo" }
                                    }
                                }
                            }
                        }
                        
                        #[cfg(not(target_family = "wasm"))]
                        {
                            rsx! {
                                button {
                                    class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer",
                                    onclick: handle_mobile_file_select,
                                    match image_upload.state.read().clone() {
                                        ImageUploadState::Uploading => rsx! {
                                            svg {
                                                class: "animate-spin -ml-1 mr-2 h-4 w-4",
                                                fill: "none",
                                                view_box: "0 0 24 24",
                                                circle {
                                                    class: "opacity-25",
                                                    cx: "12",
                                                    cy: "12",
                                                    r: "10",
                                                    stroke: "currentColor",
                                                    stroke_width: "4"
                                                }
                                                path {
                                                    class: "opacity-75",
                                                    fill: "currentColor",
                                                    d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                                }
                                            }
                                            "Uploading..."
                                        },
                                        ImageUploadState::Success(_) => rsx! { "Change Photo" },
                                        _ => rsx! { "Choose Photo" }
                                    }
                                }
                            }
                        }
                    }
                    
                    if let ImageUploadState::Error(ref error) = image_upload.state.read().clone() {
                        p { class: "mt-1 text-sm text-red-600", "{error}" }
                    }
                }
            }
        }
    }
}

use crate::ui::components::base::Label;