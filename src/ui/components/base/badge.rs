use crate::ui::prelude::*;


#[derive(<PERSON><PERSON>, PartialEq, <PERSON>lone)]
pub struct BadgeProps {
    pub children: Element,
    #[props(default = "default".to_string())]
    pub variant:  String,
}

#[component]
pub fn Badge(props: BadgeProps) -> Element {
    let base_class = "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold \
                      transition-colors focus:outline-none focus:ring-2 focus:ring-ring \
                      focus:ring-offset-2";

    let variant_class = match props.variant.as_str() {
        "secondary" =>
            "border-transparent bg-secondary text-secondary-foreground-on-darker hover:bg-secondary/80",
        "destructive" =>
            "border-transparent bg-destructive text-destructive-foreground-on-darker \
             hover:bg-destructive/80",
        "outline" => "text-foreground-on-darker",
        _ => "border-transparent bg-primary text-primary-foreground-on-darker hover:bg-primary/80", /* default */
    };

    rsx! {
        div { class: "{base_class} {variant_class}", {props.children} }
    }
}
