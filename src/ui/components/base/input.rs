use crate::ui::prelude::*;


#[derive(<PERSON><PERSON>, PartialEq, Eq, <PERSON>lone)]
pub struct InputProps {
    #[props(into)]
    pub id:          String,
    #[props(into)]
    pub value:       Signal<String>,
    #[props(default = "text".to_string(), into)]
    pub r#type:      String,
    #[props(into)]
    pub placeholder: Option<String>,
    #[props(default)]
    pub class:       String,
    #[props(default = false)]
    pub error:       bool,
}

#[component]
pub fn Input(mut props: InputProps) -> Element {
    let base_class = "flex h-10 w-full rounded-md border bg-background px-3 py-2 text-sm \
                     ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium \
                     placeholder:text-muted-foreground-on-darker focus-visible:outline-none focus-visible:ring-2 \
                     focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50";
    
    let border_class = if props.error {
        "border-red-500 focus-visible:ring-red-500"
    } else {
        "border-input focus-visible:ring-ring"
    };
    
    let class = format!("{} {} {}", base_class, border_class, props.class);

    rsx! {
      input {
        id: "{props.id}",
        class: "{class}",
        r#type: "{props.r#type}",
        placeholder: props.placeholder.clone(),
        value: "{props.value}",
        oninput: move |evt| props.value.set(evt.value()),
      }
    }
}
