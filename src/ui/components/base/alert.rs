use crate::ui::prelude::*;


#[derive(Props, PartialEq, Clone)]
pub struct AlertProps {
    pub children: Element,
    #[props(default = "default".to_string())]
    pub variant:  String,
}

#[component]
pub fn Alert(props: AlertProps) -> Element {
    let base_class = "relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] \
                      [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground-on-darker";

    let variant_class = match props.variant.as_str() {
        "destructive" =>
            "border-destructive/50 text-destructive dark-theme:border-destructive [&>svg]:text-destructive",
        _ => "text-foreground-on-darker", // default
    };

    rsx! {
        div { role: "alert", class: "{base_class} {variant_class}", {props.children} }
    }
}

#[derive(<PERSON><PERSON>, PartialEq, Clone)]
pub struct AlertTitleProps {
    pub children: Element,
}

#[component]
pub fn AlertTitle(props: AlertTitleProps) -> Element {
    rsx! {
        h5 { class: "mb-1 font-medium leading-none tracking-tight", {props.children} }
    }
}

#[derive(Props, PartialEq, Clone)]
pub struct AlertDescriptionProps {
    pub children: Element,
}

#[component]
pub fn AlertDescription(props: AlertDescriptionProps) -> Element {
    rsx! {
        div { class: "text-sm [&_p]:leading-relaxed", {props.children} }
    }
}
