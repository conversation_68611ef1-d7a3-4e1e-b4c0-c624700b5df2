use crate::ui::prelude::*;


#[derive(Props, PartialEq, Clone)]
pub struct LabelProps {
    pub children: Element,
    #[props(into)]
    pub r#for:    Option<String>,
    #[props(default = false)]
    pub required: bool,
    #[props(default = false)]
    pub error:    bool,
}

#[component]
pub fn Label(props: LabelProps) -> Element {
    rsx! {
      label {
        class: "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
        r#for: props.r#for,
        {props.children}
        if props.required {
            span {
                class: if props.error {
                    "text-red-500 ml-1"
                } else {
                    "text-gray-400 ml-1"
                },
                "*"
            }
        }
      }
    }
}
