use dioxus::prelude::*;

// use dioxus_motion::prelude::*;
use crate::ui::{
    layout::BottomNavbar,
    pages::{
        // ActivityHistory,
        // AddDog,
        AiAssistantPage,
        DogProfile,
        Home,
        NotFound,
        Onboarding,
        // Results,
        Settings,
        WeightHistory,
    },
};

// #[derive(Routable, Clone, PartialEq, Eq, MotionTransitions)]
#[derive(Routable, Clone, PartialEq, Eq)]
#[rustfmt::skip]
pub enum Route {
    #[layout(WithNavbar)]
        #[route("/")]
        Home {},
        #[route("/dog")]
        DogProfile {},
        // #[route("/dog/:id")]
        // DogProfile { id: String },
        #[route("/ai")]
        AiAssistantPage {},
        #[route("/settings")]
        // #[transition(Fade)]
        Settings {},
    #[end_layout]

    // #[route("/activities/history")]
    // ActivityHistory {},
    // #[route("/add-dog")]
    // AddDog {},
    // #[route("/ai")]
    // AIChat {},
    #[route("/onboarding")]
    Onboarding {},
    // #[route("/results")]
    // ResultsPage {},
    #[route("/weight-history")]
    WeightHistory {},

    #[route("/*path")]
    NotFound { path: String },
}

#[component]
pub fn WithNavbar() -> Element {
    rsx! {
        Outlet::<Route> {}
        // AnimatedOutlet::<Route> {} // Crashes when switching between pages
        BottomNavbar {}
    }
}
