use crate::{
    models::weight::WeightLog,
    ui::prelude::*,
};


#[derive(<PERSON>ps, PartialEq, Eq, Clone)]
pub struct WeightHistoryTableProps {
    pub logs: Vec<WeightLog>,
}

#[component]
pub fn WeightHistoryTable(props: WeightHistoryTableProps) -> Element {
    rsx! {
      div { class: "overflow-x-auto",
        table { class: "min-w-full bg-white border border-gray-200",
          thead {
            tr {
              th { class: "px-4 py-2 border-b", "Date" }
              th { class: "px-4 py-2 border-b", "Weight" }
            }
          }
          tbody {
            for log in &props.logs {
              tr {
                td { class: "px-4 py-2 border-b", "{log.date}" }
                td { class: "px-4 py-2 border-b", "{log.weight_kg} kg" }
              }
            }
          }
        }
      }
    }
}
