use dioxus::prelude::*;
use dioxus_free_icons::icons::ld_icons::{
    LdActivity,
    LdBone,
    LdCalendar,
    LdCircle<PERSON>heck,
    LdEggFried,
    LdGauge,
    LdHeart,
    LdHourglass,
    LdTrendingUp,
    LdWeight,
};

use crate::{
    hooks::{
        use_activities,
        use_dogs,
        use_meals,
    },
    prelude::*,
    ui::components::advanced::{
        MetricCard,
        WalkStatusBar,
    },
    utils::calorie_calculator::CalorieCalculator,
};


mod daily_diet_card;
use daily_diet_card::DailyDietCard;
mod dog_recommendation_card;
use dog_recommendation_card::RecommendationCard;
mod treat_tracker;
use treat_tracker::TreatTracker;


#[component]
pub fn Home() -> Element {
    let dogs_hook = use_dogs();
    let activities_hook = use_activities();
    let meals_hook = use_meals();
    let navigator = use_navigator();

    // Read the current state
    let dogs_state = dogs_hook.state.read();
    let activities_state = activities_hook.state.read();
    let meals_state = meals_hook.state.read();

    // Handle empty state
    if dogs_state.dogs.is_empty() && !dogs_state.is_loading {
        return rsx! {
          Page {
            div { class: "text-center py-8",
              p { class: "text-gray-500 mb-4", "No dogs found" }
              Button {
                onclick: move |_| {
                    navigator.push("/onboarding");
                },
                "Add Your First Dog"
              }
            }
          }
        };
    }

    // Handle loading state
    if dogs_state.is_loading {
        return rsx! {
          Page {
            div { class: "text-center py-8",
              p { "Loading..." }
            }
          }
        };
    }

    // Handle error state
    if let Some(error) = &dogs_state.error {
        return rsx! {
          Page {
            div { class: "text-center py-8 text-red-500",
              p { "Error: {error}" }
              Button { onclick: move |_| (dogs_hook.load_dogs)(()), "Retry" }
            }
          }
        };
    }

    // Get the selected dog
    let dog = match dogs_state
        .selected_dog_id
        .as_ref()
        .and_then(|id| dogs_state.dogs.iter().find(|d| &d.id == id))
    {
        Some(dog) => dog.clone(),
        None => {
            return rsx! {
              Page {
                div { class: "text-center py-8",
                  p { class: "text-gray-500", "No dog selected" }
                }
              }
            };
        },
    };

    // Calculate daily calorie goal using CalorieCalculator
    let daily_calorie_goal = CalorieCalculator::calculate_daily_calories(&dog)
        .recommended_daily_calories
        .ceil() as u32;

    // Calculate today's calorie consumption from meals
    let calorie_progress = meals_state
        .daily_summary
        .as_ref()
        .map_or(0, |summary| summary.total_calories as u32);

    // Calculate BMI if possible
    let bmi = if let (Some(weight), Some(size)) = (dog.current_weight, Some(&dog.size)) {
        let ideal_weight = match size {
            DogSize::Small => 7.0,   // kg
            DogSize::Medium => 20.0, // kg
            DogSize::Large => 35.0,  // kg
        };
        ((weight / ideal_weight) * 100.0) as u32
    } else {
        100 // Default normal BMI
    };

    // Calculate today's activity duration (from all activities for now)
    let activity_duration = activities_state
        .activities
        .iter()
        .filter_map(|activity| activity.duration_minutes)
        .sum::<i32>() as u32;

    let handle_add_treat = move || {
        // TODO: Implement treat tracking with meal logging
    };

    let handle_remove_treat = move || {
        // TODO: Implement treat tracking with meal logging
    };

    let get_diet_impact_text = move || -> String { t!("diet.none") };

    rsx! {
      Page {
        PageHeaderProfile { dog: dog.clone(), dogs: dogs_state.dogs.clone() }

        Section { class: "px-0 text-foreground", colored: true,
          // * Health Metrics
          Section {
            title: t!("home-health-metrics"),
            icon: IconRef::new(&LdActivity),
            div { class: "grid grid-cols-2 gap-4",
              MetricCard {
                icon: IconRef::new(&LdGauge),
                title: "BCS",
                value: dog.body_condition_score.unwrap_or(5) as u32,
                unit: "",
                color: "teal",
              }
              Link { to: "/activities/history",
                MetricCard {
                  icon: IconRef::new(&LdTrendingUp),
                  title: "BMI",
                  value: bmi,
                  unit: "",
                  color: "blue",
                }
              }
              Link { to: "/weight-history",
                MetricCard {
                  icon: IconRef::new(&LdWeight),
                  title: "Weight",
                  value: dog.current_weight.unwrap_or(0.0),
                  unit: "kg",
                  color: "purple",
                }
              }
              MetricCard {
                icon: IconRef::new(&LdHeart),
                title: "Overall Score",
                value: if bmi <= 110 && dog.body_condition_score.unwrap_or(5) <= 6 { "Good" } else { "Fair" },
                unit: "",
                color: "pink",
              }
            }
          }

          // * Today's Activity
          Section {
            title: t!("home-todays-activity"),
            icon: IconRef::new(&LdHourglass),

            TodayActivitySummary {
              activity_duration,
              activity_goal: dog.daily_activity_goal.unwrap_or(30) as u32,
            }
          }

          // * Today's Diet Card
          Section {
            title: t!("diet-todays-diet"),
            icon: IconRef::new(&LdBone),
            DailyDietCard {
              daily_calories: daily_calorie_goal,
              preferred_foods: dog.preferred_foods,
              diet_adjustment: 0, // TODO: implement diet adjustments based on treats/activity
            }
            TreatTracker {
              treats_count: 0, // TODO: implement treat tracking with meals
              on_treat_added: handle_add_treat,
              on_treat_removed: handle_remove_treat,
              diet_impact: get_diet_impact_text(),
            }
          }

          // * Nutrition Summary
          Section {
            title: t!("home-nutrition-summary"),
            icon: IconRef::new(&LdEggFried),
            ProgressBar {
              title: t!("home-daily-calories"),
              current_value: calorie_progress,
              total_value: daily_calorie_goal,
              unit: "kcal".to_string(),
            }

            if let Some(summary) = &meals_state.daily_summary {
              div { class: "grid grid-cols-3 gap-2 mt-4",
                Card { class: "items-center bg-background-dark/15 border-background-dark/25",
                  p { class: "text-sm text-gray-700", {t!("home-protein")} }
                  p { class: "font-bold text-black",
                    "{summary.protein_percentage:.0}%"
                  }
                }
                Card { class: "items-center bg-background-dark/15 border-background-dark/25",
                  p { class: "text-sm text-gray-700", {t!("home-fats")} }
                  p { class: "font-bold text-black", "{summary.fat_percentage:.0}%" }
                }
                Card { class: "items-center bg-background-dark/15 border-background-dark/25",
                  p { class: "text-sm text-gray-700", {t!("home-carbs")} }
                  p { class: "font-bold text-black",
                    "{summary.carbs_percentage:.0}%"
                  }
                }
              }
            } else {
              div { class: "grid grid-cols-3 gap-2 mt-4",
                Card { class: "items-center bg-background-dark/15 border-background-dark/25",
                  p { class: "text-sm text-gray-700", {t!("home-protein")} }
                  p { class: "font-bold text-black", "0%" }
                }
                Card { class: "items-center bg-background-dark/15 border-background-dark/25",
                  p { class: "text-sm text-gray-700", {t!("home-fats")} }
                  p { class: "font-bold text-black", "0%" }
                }
                Card { class: "items-center bg-background-dark/15 border-background-dark/25",
                  p { class: "text-sm text-gray-700", {t!("home-carbs")} }
                  p { class: "font-bold text-black", "0%" }
                }
              }
            }
          }
        }

        // * Recommendation Card (if any)
        RecommendationCard {}
      }
    }
}


#[derive(Props, PartialEq, Eq, Clone)]
pub struct TodayActivitySummaryProps {
    pub activity_duration: u32,
    pub activity_goal:     u32,
}

#[component]
pub fn TodayActivitySummary(props: TodayActivitySummaryProps) -> Element {
    rsx! {
      ProgressBar {
        current_value: props.activity_duration,
        total_value: props.activity_goal,
        unit: "min".to_string(),
      }
    }
}
