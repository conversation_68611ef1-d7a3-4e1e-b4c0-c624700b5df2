use dioxus::{
    logger::tracing::info,
    prelude::*,
};

use crate::{
    models::DogRecommendation,
    ui::layout::Col,
};


#[component]
pub fn RecommendationCard() -> Element {
    let mut expanded = use_signal(|| false);
    let mut is_visible = use_signal(|| true);


    let mut recommendations = get_recommendations();
    let mut current_recommendation =
        use_signal(|| Some::<DogRecommendation>(recommendations.pop().unwrap()));

    if !is_visible() || current_recommendation().is_none() {
        return rsx! {
            div { class: "hidden" }
        };
    }
    let recommendation = current_recommendation().unwrap();

    // // Show recommendations effect
    // use_effect(move || {
    //     if recommendations
    //         .iter()
    //         .find(|rec| !rec.seen)
    //         .cloned()
    //         .is_some()
    //     {
    //         // let mut rng = rand::thread_rng();
    //         // if let Some(random_rec) = unseen_recommendations.choose(&mut rng) {
    //         //     current_recommendation.set(Some(random_rec.clone()));
    //         // }
    //     }

    //     // * Check onboarding status and show toast
    //     // let has_completed_onboarding = get_onboarding_completed();
    //     // let has_shown_welcome_notification = get_welcome_notification_shown();

    //     // if !has_completed_onboarding && !has_shown_welcome_notification {
    //     //     // Simulate toast
    //     //     info!(
    //     //         "Welcome to My Dog In Fit! Complete your dog's profile in the Settings section to get
    // \     //          personalized recommendations."
    //     //     );
    //     //     // set_welcome_notification_shown(true);
    //     // }
    // });

    let handle_mark_recommendation_as_seen = move |_| {
        current_recommendation.set(recommendations.pop());
        if let Some(rec) = current_recommendation() {
            info!("Recommendation: {}", rec.text);
        }

        // let updated_recs: Vec<DogRecommendation> = recommendations
        //     .iter()
        //     .map(|rec| {
        //         if rec.id == id {
        //             DogRecommendation {
        //                 seen: true,
        //                 ..rec.clone()
        //             }
        //         } else {
        //             rec.clone()
        //         }
        //     })
        //     .collect();
        // save_recommendations(&updated_recs);
        // recommendations.set(updated_recs);
        // current_recommendation.set(None);

        // * After a delay, show another recommendation if available
        // let recs_clone = recommendations.clone();
        // let current_rec_setter = current_recommendation.setter();
        // cx.spawn(async move {
        //     dioxus::tokio::time::sleep(Duration::from_secs(60)).await;
        //     let unseen_recommendations: Vec<DogRecommendation> = recs_clone
        //         .iter()
        //         .filter(|rec| !rec.seen && rec.id != id)
        //         .cloned()
        //         .collect();
        //     if !unseen_recommendations.is_empty() {
        //         let mut rng = rand::thread_rng();
        //         if let Some(random_rec) = unseen_recommendations.choose(&mut rng) {
        //             current_rec_setter(Some(random_rec.clone()));
        //         }
        //     }
        // });
    };


    let get_category_color = |category: &str| -> String {
        match category {
            "diet" => "bg-pet-yellow".to_string(),
            "activity" => "bg-pet-green".to_string(),
            "health" => "bg-pet-purple".to_string(),
            _ => "bg-pet-lightPurple".to_string(),
        }
    };
    let len = recommendation.text.len();

    rsx! {
        div { class: "fixed z-10 bottom-25 left-5 right-5 shadow-lg border border-primary/30 overflow-clip bg-background-dark/40 text-foreground-on-darker backdrop-blur-2xl rounded-2xl page-content pt-6 pb-2",
            div {
                class: "absolute top-1 right-3 cursor-pointer text-2xl font-extralight text-foreground-on-darker/70",
                onclick: move |_| {
                    is_visible.set(false);
                },
                "⨯"
            }
            Col {
                div {
                    class: "text-md font-light",
                    class: if len > 100 && !expanded() { "line-clamp-2" },
                    onclick: move |_| {
                        expanded.set(!expanded());
                    },
                    if len > 100 {
                        div {
                            class: "absolute left-5 bottom-4 text-foreground-on-darker/70",
                            class: if expanded() { "rotate-180" },
                            "⌵"
                        }
                    }
                    "{recommendation.text}"
                }

                // if !recommendation.seen {
                button {
                    class: "mt-1 text-md self-end text-foreground-on-darker/70", // Simulating Button variant="ghost" size="sm"
                    onclick: handle_mark_recommendation_as_seen,
                    "Next tip"
                }
                        // }
            }
        }
    }
}


// Sample recommendations (in a real app these would come from the backend)
fn get_recommendations() -> Vec<DogRecommendation> {
    vec![
        DogRecommendation {
            id:       "1".to_string(),
            text:     "Do not feed your dog before a walk. Wait at least 1 hour after feeding before any \
                       moderate to intensive exercise."
                .to_string(),
            seen:     false,
            category: "diet".to_string(),
        },
        DogRecommendation {
            id:       "2".to_string(),
            text:     "Do not feed from the table. Human food can contain ingredients that are harmful to \
                       dogs and can lead to obesity."
                .to_string(),
            seen:     false,
            category: "diet".to_string(),
        },
        DogRecommendation {
            id:       "3".to_string(),
            text:     "Provide constant access to fresh, clean water. This is especially important when \
                       increasing your dog's activity level."
                .to_string(),
            seen:     false,
            category: "health".to_string(),
        },
        DogRecommendation {
            id:       "4".to_string(),
            text:     "Remember to choose food for your dog considering allergies. If you notice signs of \
                       food allergies (scratching, digestive issues), consult your vet."
                .to_string(),
            seen:     false,
            category: "health".to_string(),
        },
        DogRecommendation {
            id:       "5".to_string(),
            text:     "Visit veterinary if your dog started to drink unusually little or, conversely, \
                       much more than usual. This could be a sign of underlying health issues."
                .to_string(),
            seen:     false,
            category: "health".to_string(),
        },
    ]
}
