use crate::ui::prelude::*;
use crate::hooks::use_onboarding;
use crate::ui::components::advanced::ImageUpload;

#[derive(Props, PartialEq, Clone)]
pub struct PetInfoFormProps {
    pub pet_name: Signal<String>,
    pub pet_gender: Signal<String>,
    pub pet_size: Signal<String>,
    pub pet_image_url: Signal<Option<String>>,
    #[props(default)]
    pub field_errors: std::collections::HashMap<String, bool>,
    #[props(default)]
    pub on_back: EventHandler<()>,
    #[props(default)]
    pub on_next: EventHandler<()>,
}

#[component]
pub fn PetInfoForm(mut props: PetInfoFormProps) -> Element {
    // No need for circular synchronization - signals are persistent now

    rsx! {
        div { class: "space-y-4",
            p { class: "text-gray-600 mb-4",
                "Now let's learn about your furry friend! This helps us provide personalized recommendations."
            }
            
            div {
                Label {
                    r#for: "pet_name",
                    required: true,
                    error: *props.field_errors.get("pet_name").unwrap_or(&false),
                    "Dog's Name"
                }
                Input {
                    id: "pet_name",
                    value: props.pet_name,
                    placeholder: "What's your dog's name?",
                    error: *props.field_errors.get("pet_name").unwrap_or(&false),
                }
                if *props.field_errors.get("pet_name").unwrap_or(&false) {
                    p { class: "text-sm text-red-500 mt-1",
                        "Dog's name is required"
                    }
                }
            }
            
            // Image upload field - add right after name field
            div {
                ImageUpload {
                    label: Some("Dog Photo (Optional)".to_string()),
                    on_image_selected: move |image_url: String| {
                        props.pet_image_url.set(Some(image_url));
                    },
                }
                if *props.field_errors.get("pet_image").unwrap_or(&false) {
                    p { class: "text-sm text-red-500 mt-1",
                        "Failed to upload image. Please try again."
                    }
                }
            }
            
            div {
                Label {
                    required: true,
                    error: *props.field_errors.get("pet_gender").unwrap_or(&false),
                    "Gender"
                }
                div {
                    class: if *props.field_errors.get("pet_gender").unwrap_or(&false) {
                        "grid grid-cols-2 gap-2 mt-2 p-2 border-2 border-red-500 rounded-lg animate-pulse"
                    } else {
                        "grid grid-cols-2 gap-2 mt-2"
                    },
                    Button {
                        variant: if props.pet_gender.read().as_str() == "male" { 
                            ButtonVariant::Default
                        } else { 
                            ButtonVariant::Outline 
                        },
                        onclick: move |_| props.pet_gender.set("male".to_string()),
                        class: "flex-1",
                        "Male"
                    }
                    Button {
                        variant: if props.pet_gender.read().as_str() == "female" { 
                            ButtonVariant::Default
                        } else { 
                            ButtonVariant::Outline 
                        },
                        onclick: move |_| props.pet_gender.set("female".to_string()),
                        class: "flex-1",
                        "Female"
                    }
                }
                if *props.field_errors.get("pet_gender").unwrap_or(&false) {
                    p { class: "text-sm text-red-500 mt-1",
                        "Please select your dog's gender"
                    }
                }
            }
            
            div {
                Label {
                    required: true,
                    error: *props.field_errors.get("pet_size").unwrap_or(&false),
                    "Size"
                }
                div {
                    class: if *props.field_errors.get("pet_size").unwrap_or(&false) {
                        "grid grid-cols-3 gap-2 mt-2 p-2 border-2 border-red-500 rounded-lg animate-pulse"
                    } else {
                        "grid grid-cols-3 gap-2 mt-2"
                    },
                    Button {
                        variant: if props.pet_size.read().as_str() == "small" { 
                            ButtonVariant::Default
                        } else { 
                            ButtonVariant::Outline 
                        },
                        onclick: move |_| props.pet_size.set("small".to_string()),
                        class: "flex-1 text-sm",
                        div { class: "text-center",
                            div { "Small" }
                            div { class: "text-xs text-gray-500", "< 25 lbs" }
                        }
                    }
                    Button {
                        variant: if props.pet_size.read().as_str() == "medium" { 
                            ButtonVariant::Default
                        } else { 
                            ButtonVariant::Outline 
                        },
                        onclick: move |_| props.pet_size.set("medium".to_string()),
                        class: "flex-1 text-sm",
                        div { class: "text-center",
                            div { "Medium" }
                            div { class: "text-xs text-gray-500", "25-60 lbs" }
                        }
                    }
                    Button {
                        variant: if props.pet_size.read().as_str() == "large" { 
                            ButtonVariant::Default
                        } else { 
                            ButtonVariant::Outline 
                        },
                        onclick: move |_| props.pet_size.set("large".to_string()),
                        class: "flex-1 text-sm",
                        div { class: "text-center",
                            div { "Large" }
                            div { class: "text-xs text-gray-500", "> 60 lbs" }
                        }
                    }
                }
                if *props.field_errors.get("pet_size").unwrap_or(&false) {
                    p { class: "text-sm text-red-500 mt-1",
                        "Please select your dog's size"
                    }
                }
            }
            
            div { class: "flex justify-between pt-4",
                Button {
                    variant: ButtonVariant::Outline,
                    onclick: move |_| props.on_back.call(()),
                    "Back"
                }
                Button {
                    onclick: move |_| {
                        props.on_next.call(());
                    },
                    "Next"
                }
            }
        }
    }
}