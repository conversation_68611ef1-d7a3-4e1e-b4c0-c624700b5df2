use crate::ui::prelude::*;

#[derive(Props, PartialEq, Clone)]
pub struct HealthMetricsFormProps {
    pub weight: Signal<Option<f32>>,
    pub birthday: Signal<String>,
    pub target_weight: Signal<Option<f32>>,
    pub validation_error: Option<String>,
    #[props(default)]
    pub field_errors: std::collections::HashMap<String, bool>,
    #[props(default)]
    pub on_back: EventHandler<()>,
    #[props(default)]
    pub on_next: EventHandler<()>,
}

#[component]
pub fn HealthMetricsForm(mut props: HealthMetricsFormProps) -> Element {
    // Create string signals for numeric inputs with proper synchronization
    let weight_input = use_signal(|| props.weight.read().map_or_else(String::new, |w| w.to_string()));
    let target_weight_input = use_signal(|| props.target_weight.read().map_or_else(String::new, |w| w.to_string()));
    
    // Set up synchronization effects to update the main signals when input changes
    use_effect(move || {
        let weight_str = weight_input.read();
        if weight_str.trim().is_empty() {
            *props.weight.write() = None;
        } else if let Ok(weight) = weight_str.parse::<f32>() {
            *props.weight.write() = Some(weight);
        }
    });
    
    use_effect(move || {
        let target_weight_str = target_weight_input.read();
        if target_weight_str.trim().is_empty() {
            *props.target_weight.write() = None;
        } else if let Ok(weight) = target_weight_str.parse::<f32>() {
            *props.target_weight.write() = Some(weight);
        }
    });
    
    let weight_error = *props.field_errors.get("dog_weight").unwrap_or(&false);
    let birthday_error = *props.field_errors.get("dog_birthday").unwrap_or(&false);
    let target_weight_error = *props.field_errors.get("dog_target_weight").unwrap_or(&false);

    rsx! {
        div { class: "space-y-4",
            p { class: "text-gray-600 mb-4",
                "Let's gather some health information to provide accurate recommendations."
            }
            
            div {
                Label {
                    r#for: "weight",
                    required: true,
                    error: weight_error,
                    "Current Weight (lbs)"
                }
                Input {
                    id: "weight",
                    r#type: "number",
                    value: weight_input,
                    placeholder: "Enter current weight",
                    error: weight_error,
                }
                div { class: "flex items-center mt-2 text-sm text-blue-600 cursor-pointer",
                    "ℹ️ How to weigh your dog at home"
                }
                if weight_error {
                    p { class: "text-sm text-red-500 mt-1",
                        "Current weight is required"
                    }
                }
            }
            
            div {
                Label {
                    r#for: "birthday",
                    required: true,
                    error: birthday_error,
                    "Birthday"
                }
                Input {
                    id: "birthday",
                    r#type: "date",
                    value: props.birthday,
                    error: birthday_error,
                }
                p { class: "text-sm text-gray-500 mt-1",
                    "This helps us calculate age-appropriate calorie needs."
                }
                if birthday_error {
                    p { class: "text-sm text-red-500 mt-1",
                        "Birthday is required"
                    }
                }
            }
            
            div {
                Label {
                    r#for: "target_weight",
                    error: target_weight_error,
                    "Target Weight (lbs, Optional)"
                }
                Input {
                    id: "target_weight",
                    r#type: "number",
                    value: target_weight_input,
                    placeholder: "Enter target weight",
                    error: target_weight_error,
                }
                p { class: "text-sm text-gray-500 mt-1",
                    "Leave blank if your dog is at their ideal weight."
                }
                if target_weight_error {
                    p { class: "text-sm text-red-500 mt-1",
                        "Please enter a valid target weight"
                    }
                }
            }
            
            if let Some(error) = &props.validation_error {
                div { class: "p-3 bg-red-100 border border-red-400 text-red-700 rounded mb-4",
                    "{error}"
                }
            }
            
            div { class: "flex justify-between pt-4",
                Button {
                    variant: ButtonVariant::Outline,
                    onclick: move |_| props.on_back.call(()),
                    "Back"
                }
                Button {
                    onclick: move |_| {
                        props.on_next.call(());
                    },
                    "Next"
                }
            }
        }
    }
}