use dioxus::prelude::*;
use dioxus_router::hooks::use_navigator;

use crate::hooks::{use_onboarding, use_dogs};

mod pet_info_form;
use pet_info_form::PetInfoForm;
mod health_metrics_form;
use health_metrics_form::HealthMetricsForm;
mod activity_bcs_form;
use activity_bcs_form::ActivityBcsForm;
mod recommendations_display;
use recommendations_display::RecommendationsDisplay;
mod onboarding_section;
use onboarding_section::OnboardingSection;

#[component]
pub fn Onboarding() -> Element {
    let navigator = use_navigator();
    let onboarding = use_onboarding();
    let dogs_hook = use_dogs();

    let current_step = onboarding.current_step();

    rsx! {
        div { class: "p-4 min-h-screen bg-gray-50",
            div { class: "max-w-md mx-auto",
                // Progress indicator
                div { class: "mb-6",
                    div { class: "flex justify-between items-center mb-2",
                        h1 { class: "text-2xl font-bold text-center", "Welcome to MyDogInFit!" }
                        if current_step <= 3 {
                            span { class: "text-sm text-gray-600", "Step {current_step} of 3" }
                        } else {
                            span { class: "text-sm text-gray-600", "Complete!" }
                        }
                    }
                    div { class: "w-full bg-gray-200 rounded-full h-2",
                        {
                            let progress_width = if current_step >= 4 {
                                100.0
                            } else {
                                current_step as f32 / 3.0 * 100.0
                            };
                            rsx! {
                                div {
                                    class: "bg-blue-500 h-2 rounded-full transition-all duration-300",
                                    style: "width: {progress_width}%",
                                }
                            }
                        }
                    }
                }

                // Step content
                match current_step {
                    1 => {
                        let mut onboarding_clone1 = onboarding.clone();
                        let mut onboarding_clone2 = onboarding.clone();
                        let field_errors = onboarding.get_field_errors(1);
                        rsx! {
                            OnboardingSection { title: "About your dog",
                                PetInfoForm {
                                    pet_name: onboarding.dog_name(),
                                    pet_gender: onboarding.dog_gender(),
                                    pet_size: onboarding.dog_size(),
                                    pet_image_url: onboarding.dog_image_url(),
                                    on_back: move |()| {
                                        onboarding_clone1.previous_step();
                                    },
                                    on_next: move |()| {
                                        onboarding_clone2.next_step();
                                    },
                                    field_errors,
                                }
                            }
                        }
                    }
                    2 => {
                        let mut onboarding_clone1 = onboarding.clone();
                        let mut onboarding_clone2 = onboarding.clone();
                        let field_errors = onboarding.get_field_errors(2);
                        rsx! {
                            OnboardingSection { title: "Health metrics",
                                HealthMetricsForm {
                                    weight: onboarding.dog_weight(),
                                    birthday: onboarding.dog_birthday(),
                                    target_weight: onboarding.dog_target_weight(),
                                    on_back: move |()| {
                                        onboarding_clone1.previous_step();
                                    },
                                    on_next: move |()| {
                                        onboarding_clone2.next_step();
                                    },
                                    field_errors,
                                }
                            }
                        }
                    }
                    3 => {
                        let mut onboarding_clone1 = onboarding.clone();
                        let onboarding_clone2 = onboarding.clone();
                        let field_errors = onboarding.get_field_errors(3);
                        rsx! {
                            OnboardingSection { title: "Activity & body condition",
                                ActivityBcsForm {
                                    typical_activity: onboarding.dog_typical_activity(),
                                    body_condition_score: onboarding.dog_bcs(),
                                    on_back: move |()| {
                                        onboarding_clone1.previous_step();
                                    },
                                    on_finish: move |()| {
                                        spawn({
                                            let mut onboarding = onboarding_clone2.clone();
                                            let dogs_hook_clone = dogs_hook.clone();
                                            async move {
                                                if onboarding.finish_onboarding(dogs_hook_clone).await == Ok(()) {
                                                    navigator.push("/");
                                                }
                                            }
                                        });
                                    },
                                    field_errors,
                                    is_loading: onboarding.is_loading(),
                                }
                            }
                        }
                    }
                    4 => {
                        let navigator_clone = navigator;
                        rsx! {
                            RecommendationsDisplay {
                                calorie_recommendations: onboarding.calorie_recommendations(),
                                on_continue: move |()| {
                                    navigator_clone.push("/");
                                },
                            }
                        }
                    }
                    _ => {
                        let navigator_clone = navigator;
                        rsx! {
                            div { class: "text-center p-4",
                                h2 { class: "text-xl font-bold text-red-600 mb-4", "Invalid Step" }
                                p { class: "text-gray-600 mb-4", "Something went wrong with the onboarding flow." }
                                button {
                                    class: "px-4 py-2 bg-blue-500 text-white rounded",
                                    onclick: move |_| {
                                        navigator_clone.push("/");
                                    },
                                    "Go to Home"
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
