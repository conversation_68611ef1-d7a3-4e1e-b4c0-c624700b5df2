use crate::ui::prelude::*;

#[derive(<PERSON><PERSON>, PartialEq, <PERSON>lone)]
pub struct BasicInfoFormProps {
    pub name: Signal<String>,
    pub email: Signal<String>,
    pub validation_error: Option<String>,
    #[props(default)]
    pub field_errors: std::collections::HashMap<String, bool>,
    #[props(default)]
    pub on_next: EventHandler<()>,
}

#[component]
pub fn BasicInfoForm(props: BasicInfoFormProps) -> Element {
    let name_error = *props.field_errors.get("name").unwrap_or(&false);
    let email_error = *props.field_errors.get("email").unwrap_or(&false);

    rsx! {
        div { class: "space-y-4",
            p { class: "text-gray-600 mb-4",
                "Let's start by getting to know you better. This helps us personalize your experience."
            }
            
            div {
                Label {
                    r#for: "name",
                    required: true,
                    error: name_error,
                    "Your Name"
                }
                Input {
                    id: "name",
                    value: props.name,
                    placeholder: "Enter your name",
                    error: name_error,
                }
                if name_error {
                    p { class: "text-sm text-red-500 mt-1",
                        "Name is required"
                    }
                }
            }
            
            div {
                Label {
                    r#for: "email",
                    error: email_error,
                    "Email Address (Optional)"
                }
                Input {
                    id: "email",
                    r#type: "email",
                    value: props.email,
                    placeholder: "<EMAIL>",
                    error: email_error,
                }
                p { class: "text-sm text-gray-500 mt-1",
                    "We'll use this to send you helpful tips and reminders."
                }
                if email_error {
                    p { class: "text-sm text-red-500 mt-1",
                        "Please enter a valid email address"
                    }
                }
            }
            
            if let Some(error) = &props.validation_error {
                div { class: "p-3 bg-red-100 border border-red-400 text-red-700 rounded",
                    "{error}"
                }
            }
            
            Button {
                onclick: move |_| {
                    if !props.name.read().trim().is_empty() {
                        props.on_next.call(());
                    }
                },
                disabled: props.name.read().trim().is_empty(),
                class: "w-full",
                "Next"
            }
        }
    }
}