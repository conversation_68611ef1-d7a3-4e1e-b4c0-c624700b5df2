use crate::ui::prelude::*;
use crate::utils::calorie_calculator::CalorieCalculationResult;

#[derive(Props, PartialEq, Clone)]
pub struct RecommendationsDisplayProps {
    pub calorie_recommendations: Option<CalorieCalculationResult>,
    #[props(default)]
    pub on_continue: EventHandler<()>,
}

#[component]
pub fn RecommendationsDisplay(props: RecommendationsDisplayProps) -> Element {
    rsx! {
        div { class: "space-y-6",
            div { class: "text-center mb-6",
                div { class: "text-6xl mb-4", "🎉" }
                h2 { class: "text-2xl font-bold text-green-600 mb-2", "Profile Created!" }
                p { class: "text-gray-600",
                    "Here are your personalized recommendations based on your dog's profile."
                }
            }
            
            if let Some(recommendations) = &props.calorie_recommendations {
                div { class: "space-y-4",
                    // Daily Calories Card
                    div { class: "bg-blue-50 border border-blue-200 rounded-lg p-4",
                        div { class: "flex items-center mb-2",
                            span { class: "text-2xl mr-2", "🍽️" }
                            h3 { class: "text-lg font-semibold text-blue-800", "Daily Calories" }
                        }
                        div { class: "text-center",
                            div { class: "text-3xl font-bold text-blue-600",
                                "{recommendations.recommended_daily_calories:.0}"
                            }
                            div { class: "text-sm text-blue-700", "calories per day" }
                        }
                        p { class: "text-xs text-blue-600 mt-2",
                            "Calculated using veterinary-standard {recommendations.rer_method}"
                        }
                    }
                    
                    // RER/MER Details Card
                    div { class: "bg-gray-50 border border-gray-200 rounded-lg p-4",
                        div { class: "flex items-center mb-3",
                            span { class: "text-2xl mr-2", "📊" }
                            h3 { class: "text-lg font-semibold text-gray-800", "Calculation Details" }
                        }
                        div { class: "grid grid-cols-2 gap-4",
                            div { class: "text-center",
                                div { class: "text-2xl font-bold text-gray-600",
                                    "{recommendations.rer_calories:.0}"
                                }
                                div { class: "text-xs text-gray-500", "RER (Base)" }
                            }
                            div { class: "text-center",
                                div { class: "text-2xl font-bold text-gray-600",
                                    "{recommendations.mer_calories:.0}"
                                }
                                div { class: "text-xs text-gray-500", "MER (Maintenance)" }
                            }
                        }
                        p { class: "text-xs text-gray-500 mt-2",
                            "RER = Resting Energy Requirement, MER = Maintenance Energy Requirement"
                        }
                    }
                    
                    // BCS Adjustment (if applicable)
                    if recommendations.bcs_adjustment_percentage != 0.0 {
                        div { class: if recommendations.bcs_adjustment_percentage < 0.0 {
                            "bg-orange-50 border border-orange-200 rounded-lg p-4"
                        } else {
                            "bg-green-50 border border-green-200 rounded-lg p-4"
                        },
                            div { class: "flex items-center mb-2",
                                span { class: "text-2xl mr-2",
                                    if recommendations.bcs_adjustment_percentage < 0.0 { "⚠️" } else { "💪" }
                                }
                                h3 { class: if recommendations.bcs_adjustment_percentage < 0.0 {
                                    "text-lg font-semibold text-orange-800"
                                } else {
                                    "text-lg font-semibold text-green-800"
                                },
                                    "BCS Adjustment"
                                }
                            }
                            p { class: if recommendations.bcs_adjustment_percentage < 0.0 {
                                "text-sm text-orange-700"
                            } else {
                                "text-sm text-green-700"
                            },
                                if recommendations.bcs_adjustment_percentage < 0.0 {
                                    "Calories reduced to support weight management."
                                } else {
                                    "Calories increased due to underweight condition."
                                }
                            }
                            div { class: "text-center mt-2",
                                span { class: if recommendations.bcs_adjustment_percentage < 0.0 {
                                    "text-xl font-bold text-orange-600"
                                } else {
                                    "text-xl font-bold text-green-600"
                                },
                                    "{recommendations.bcs_adjustment_percentage:+.0}%"
                                }
                            }
                        }
                    }
                    
                    // Next Steps
                    div { class: "bg-purple-50 border border-purple-200 rounded-lg p-4",
                        div { class: "flex items-center mb-2",
                            span { class: "text-2xl mr-2", "🚀" }
                            h3 { class: "text-lg font-semibold text-purple-800", "Next Steps" }
                        }
                        ul { class: "text-sm text-purple-700 space-y-1",
                            li { "• Start logging your dog's daily meals" }
                            li { "• Track activity with our built-in timer" }
                            li { "• Monitor weight changes weekly" }
                            li { "• Chat with our AI assistant for tips" }
                        }
                    }
                }
            } else {
                div { class: "text-center text-gray-500",
                    "Calculating recommendations..."
                }
            }
            
            Button {
                onclick: move |_| props.on_continue.call(()),
                class: "w-full mt-6",
                "Get Started!"
            }
        }
    }
}