use crate::ui::prelude::*;

#[derive(<PERSON><PERSON>, PartialEq, Clone)]
pub struct ActivityBcsFormProps {
    pub typical_activity: Signal<String>,
    pub body_condition_score: Signal<Option<i32>>,
    pub validation_error: Option<String>,
    #[props(default)]
    pub field_errors: std::collections::HashMap<String, bool>,
    pub is_loading: bool,
    #[props(default)]
    pub on_back: EventHandler<()>,
    #[props(default)]
    pub on_finish: EventHandler<()>,
}

#[component]
pub fn ActivityBcsForm(mut props: ActivityBcsFormProps) -> Element {
    rsx! {
        div { class: "space-y-6",
            p { class: "text-gray-600 mb-4",
                "Finally, let's understand your dog's activity level and body condition."
            }
            
            // Activity Type Selection
            div {
                Label {
                    required: true,
                    error: *props.field_errors.get("typical_activity").unwrap_or(&false),
                    "What type of activity does your dog typically do?"
                }
                div {
                    class: if *props.field_errors.get("typical_activity").unwrap_or(&false) {
                        "space-y-3 p-3 border-2 border-red-500 rounded-lg animate-pulse"
                    } else {
                        "space-y-3"
                    },
                    button {
                        r#type: "button",
                        class: {
                            let base_class = "w-full p-4 border rounded-lg text-left transition-all duration-200 flex items-center space-x-3";
                            if props.typical_activity.read().as_str() == "walking" {
                                format!("{base_class} border-blue-500 bg-blue-50")
                            } else {
                                format!("{base_class} border-gray-300 hover:border-gray-400")
                            }
                        },
                        onclick: move |_| {
                            props.typical_activity.set("walking".to_string());
                        },
                        div { class: "text-2xl", "🚶" }
                        div {
                            div { class: "font-medium", "Walking" }
                            div { class: "text-sm text-gray-500", "Leisurely walks, light exercise" }
                        }
                    }
                    button {
                        r#type: "button",
                        class: {
                            let base_class = "w-full p-4 border rounded-lg text-left transition-all duration-200 flex items-center space-x-3";
                            if props.typical_activity.read().as_str() == "running" {
                                format!("{base_class} border-blue-500 bg-blue-50")
                            } else {
                                format!("{base_class} border-gray-300 hover:border-gray-400")
                            }
                        },
                        onclick: move |_| {
                            props.typical_activity.set("running".to_string());
                        },
                        div { class: "text-2xl", "🏃" }
                        div {
                            div { class: "font-medium", "Running" }
                            div { class: "text-sm text-gray-500", "Jogging, high-energy activities" }
                        }
                    }
                    button {
                        r#type: "button",
                        class: {
                            let base_class = "w-full p-4 border rounded-lg text-left transition-all duration-200 flex items-center space-x-3";
                            if props.typical_activity.read().as_str() == "mixed" {
                                format!("{base_class} border-blue-500 bg-blue-50")
                            } else {
                                format!("{base_class} border-gray-300 hover:border-gray-400")
                            }
                        },
                        onclick: move |_| {
                            props.typical_activity.set("mixed".to_string());
                        },
                        div { class: "text-2xl", "🎾" }
                        div {
                            div { class: "font-medium", "Mixed Activities" }
                            div { class: "text-sm text-gray-500", "Combination of walking, running, and play" }
                        }
                    }
                    if *props.field_errors.get("typical_activity").unwrap_or(&false) {
                        p { class: "text-sm text-red-500 mt-1",
                            "Please select your dog's typical activity"
                        }
                    }
                }
            }
            
            // Body Condition Score
            div {
                Label {
                    required: true,
                    error: *props.field_errors.get("body_condition_score").unwrap_or(&false),
                    "Body Condition Score (1-9 scale)"
                }
                p { class: "text-sm text-gray-600 mb-4",
                    "Rate your dog's body condition where 1 is severely underweight, 5 is ideal, and 9 is severely overweight."
                }
                div {
                    class: if *props.field_errors.get("body_condition_score").unwrap_or(&false) {
                        "p-3 border-2 border-red-500 rounded-lg animate-pulse"
                    } else {
                        ""
                    },
                    div { class: "flex gap-1 overflow-x-auto",
                        for score in 1..=9 {
                            button {
                                r#type: "button",
                                class: {
                                    let base_class = "min-w-10 p-2 border rounded text-center transition-all duration-200 flex-shrink-0";
                                    if *props.body_condition_score.read() == Some(score) {
                                        format!("{base_class} border-blue-500 bg-blue-50 text-blue-700")
                                    } else {
                                        format!("{base_class} border-gray-300 hover:border-gray-400")
                                    }
                                },
                                onclick: move |_| {
                                    props.body_condition_score.set(Some(score));
                                },
                                "{score}"
                            }
                        }
                    }
                    div { class: "mt-2 flex gap-1 text-xs text-gray-500 text-center",
                        div { class: "flex-1", "Underweight (1-3)" }
                        div { class: "flex-1", "Ideal (4-6)" }
                        div { class: "flex-1", "Overweight (7-9)" }
                    }
                }
                if *props.field_errors.get("body_condition_score").unwrap_or(&false) {
                    p { class: "text-sm text-red-500 mt-1",
                        "Please select your dog's body condition score"
                    }
                }
            }
            
            if let Some(error) = &props.validation_error {
                div { class: "p-3 bg-red-100 border border-red-400 text-red-700 rounded mb-4",
                    "{error}"
                }
            }
            
            div { class: "flex justify-between pt-4",
                Button {
                    variant: ButtonVariant::Outline,
                    onclick: move |_| props.on_back.call(()),
                    disabled: props.is_loading,
                    "Back"
                }
                Button {
                    onclick: move |_| {
                        props.on_finish.call(());
                    },
                    disabled: props.is_loading,
                    class: if props.is_loading { "opacity-50" } else { "" },
                    if props.is_loading {
                        "Creating Profile..."
                    } else {
                        "Create Profile"
                    }
                }
            }
        }
    }
}