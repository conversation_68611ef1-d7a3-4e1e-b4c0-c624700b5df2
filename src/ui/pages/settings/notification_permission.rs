// use dioxus::prelude::*;

// use crate::i18n::*;

// #[derive(<PERSON><PERSON>, PartialEq, Clone)]
// pub struct NotificationPermissionProps {}

// #[component]
// pub fn NotificationPermission(props: NotificationPermissionProps) -> Element {
//     let i18n = I18n::new();

//     let permission_status = use_signal(|| "default".to_string());
//     let platform = use_signal(|| "other".to_string()); // "ios", "android", "other"
//     let is_standalone = use_signal(|| false);

//     // Simulate useEffect for platform detection and initial permission status
//     use_effect(move || {
//         // In a real web environment, you'd use web_sys::window().navigator()
//         // For now, we'll mock the platform and permission status.
//         // Example:
//         // let user_agent = web_sys::window().unwrap().navigator().user_agent().unwrap_or_default();
//         // if user_agent.contains("iPhone") || user_agent.contains("iPad") {
//         //     platform.set("ios".to_string());
//         //     // Simulate isIOSStandalone
//         //     is_standalone.set(false);
//         // } else if user_agent.contains("Android") {
//         //     platform.set("android".to_string());
//         // }

//         // Simulate Notification.permission
//         // In a real app, you'd use web_sys::Notification::permission()
//         permission_status.set("default".to_string()); // Or "granted", "denied"
//         async {}
//     });

//     let request_permission = move |_| {
//         // Simulate Notification.requestPermission()
//         // In a real app, this would involve a browser API call.
//         // For now, we'll just set it to "granted" or "denied" for demonstration.
//         let current_platform = platform.get().clone();
//         let current_is_standalone = *is_standalone.get();

//         if current_platform == "ios" && !current_is_standalone {
//             // Simulate toast
//             // log::info!("Install as App First: For notifications on iOS, add this app to your Home
//             // Screen.");
//             return;
//         }

//         // Simulate permission request result
//         let granted = true; // Change to false to simulate denied
//         if granted {
//             permission_status.set("granted".to_string());
//             // Simulate toast
//             // log::info!("Notifications enabled: You'll now receive notifications.");
//             // Simulate sending message to service worker for test notification
//         } else {
//             permission_status.set("denied".to_string());
//             // Simulate platform-specific toast guidance
//             // log::warn!("Notifications blocked: Please enable notifications in settings.");
//         }
//     };

//     if permission_status.get() == "granted" {
//         return None;
//     }

//     rsx!(
//       div { class: "flex flex-col gap-4 pt-1 mb-10",
//         h3 { class: "font-medium", {t!("notifications-push-n")}otifications")} }

//         p { class: "text-md text-muted-foreground-on-darker",
//           {t!("notifications-push-n")}otificationsDescription")}
//         }

//         if platform.get() == "ios" && !*is_standalone.get() {
//           div { class: "flex flex-col gap-2 bg-amber-50 p-3 rounded-md border border-amber-200",
//             div { class: "flex items-center gap-2 text-amber-700",
//               // Placeholder for Download icon
//               div { class: "h-4 w-4", "⬇️" }
//               span { class: "font-medium", {t!("notifications-install-a")}sAppFirst")} }
//             }
//             p { class: "text-xs text-amber-700",
//               {t!("notifications-ios-i")}nstallGuidance")}
//             }
//           }
//         }

//         if permission_status.get() == "denied" {
//           div { class: "flex flex-col mb-2",
//             if platform.get() == "ios" {
//               div { class: "flex items-start gap-2 text-sm bg-primary/15 p-4 rounded-md",
//                 // Placeholder for Info icon
//                 div { class: "h-4 w-4 mt-0.5 shrink-0", "ℹ️" }
//                 span { {t!("notifications-ios-e")}nableGuidance")} }
//               }
//             } else if platform.get() == "android" {
//               div { class: "flex items-start gap-2 text-sm bg-gray-100 p-2 rounded-xs",
//                 // Placeholder for Info icon
//                 div { class: "h-4 w-4 mt-0.5 shrink-0", "ℹ️" }
//                 span { {t!("notifications-android-e")}nableGuidance")} }
//               }
//             } else {
//               div { class: "flex items-start gap-2 text-sm bg-gray-100 p-2 rounded-xs",
//                 // Placeholder for Info icon
//                 div { class: "h-4 w-4 mt-0.5 shrink-0", "ℹ️" }
//                 span { {t!("notifications-browser-e")}nableGuidance")} }
//               }
//             }
//           }
//         }
//         button {
//           class: "h-12 px-4 py-2 rounded-md bg-blue-500 text-white hover:bg-blue-600",
//           onclick: request_permission,
//           {t!("notifications-enable-n")}otifications")}
//         }
//       }
//     )
// }
