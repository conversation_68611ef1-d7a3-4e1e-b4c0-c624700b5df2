use dioxus_free_icons::icons::ld_icons::LdPencil;

use crate::prelude::*;


#[derive(Props, PartialEq, Clone)]
pub struct EditableFieldProps<V: Clone + PartialEq + 'static> {
    pub label:          String,
    pub value:          Option<V>,
    pub on_change:      EventHandler<V>,
    #[props(optional)]
    pub on_focus:       EventHandler<FocusEvent>,
    // pub on_click:       EventHandler<MouseEvent>,
    // #[props(optional)]
    // pub on_clear:       EventHandler<MouseEvent>,
    #[props(default)]
    pub placeholder:    String,
    #[props(default = false)]
    pub disabled:       bool,
    #[props(default = false)]
    pub is_recognizing: bool,
}

#[component]
pub fn EditableField<V: Clone + PartialEq + TryFrom<String> + Display>(
    props: EditableFieldProps<V>,
) -> Element {
    let mut is_editing = use_signal(|| false);
    let value_formatted = use_signal(|| {
        props
            .value
            .as_ref()
            .map_or_else(String::new, ToString::to_string)
    });
    let mut input_value = value_formatted;

    let handle_edit = move |_| {
        is_editing.set(true);
    };

    // let handle_cancel = move |_| {
    //     is_editing.set(false);
    //     input_value.set(value_formatted());
    // };

    let handle_save = move |_| {
        is_editing.set(false);
        let typed_value: V = match V::try_from(input_value()) {
            Ok(v) => v,
            Err(_) => return,
        };
        if props.value.as_ref().is_some() && !typed_value.eq(props.value.as_ref().unwrap()) {
            props.on_change.call(typed_value);
        }
    };


    rsx! {
      Row {
        span {
          {props.label}
          ":"
        }
        if is_editing() {
          div { class: "flex items-center",
            input {
              value: "{value_formatted}",
              // onfocusout: handle_cancel,
              oninput: move |evt| {
                  input_value.set(evt.value());
              },
              // oninput: move |evt| props.on_change.call(evt.value()),
              // onclick: move |evt| props.on_click.call(evt),
              // onfocus: move |evt| props.on_focus.call(evt),
              // onchange: move |evt| props.on_change.call(evt.value()),
              class: "border rounded px-2 py-1 mr-2",
            }
            button {
              class: "text-secondary rounded-full",
              onclick: handle_save,
              {t!("common-save")}
            }
          }
        } else {
          div { class: "flex items-center",
            span { class: "text-sm font-medium mr-4", "{value_formatted}" }
            button {
              class: "text-secondary rounded-full",
              onclick: handle_edit,
              Icon { size: Size::Xs, icon: IconRef::new(&LdPencil) }
            }
          }
        }
        ""
      }
    }
}
