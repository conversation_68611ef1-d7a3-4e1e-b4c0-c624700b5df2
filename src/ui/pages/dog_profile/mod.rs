use chrono::prelude::*;
use dioxus::prelude::*;
use dioxus_free_icons::icons::ld_icons::LdCamera;

use crate::{
    hooks::{use_dogs, use_image_upload},
    models::dogs::{
        <PERSON>,
        DogSize,
        <PERSON>Weight,
    },
    prelude::*,
    ui::components::advanced::ImageUpload,
};

mod editable_field;
use editable_field::EditableField;
mod delete_dog_dialog;
use delete_dog_dialog::DeleteDogDialog;

#[component]
pub fn DogProfile() -> Element {
    debug!("DogProfile()");
    let dogs_hook = use_dogs();
    let navigator = use_navigator();

    // Read the current state
    let dogs_state = dogs_hook.state.read();

    // Handle empty state
    if dogs_state.dogs.is_empty() && !dogs_state.is_loading {
        return rsx! {
          Page {
            div { class: "text-center py-8",
              p { class: "text-gray-500 mb-4", "No dogs found" }
              Button {
                onclick: move |_| {
                    navigator.push("/onboarding");
                },
                "Add Your First Dog"
              }
            }
          }
        };
    }

    // Handle loading state
    if dogs_state.is_loading {
        return rsx! {
          Page {
            div { class: "text-center py-8",
              p { "Loading dog profile..." }
            }
          }
        };
    }

    // Handle error state
    if let Some(error) = &dogs_state.error {
        return rsx! {
          Page {
            div { class: "text-center py-8 text-red-500",
              p { "Error: {error}" }
              Button { onclick: move |_| (dogs_hook.load_dogs)(()), "Retry" }
            }
          }
        };
    }

    // Get the selected dog
    let dog = match dogs_state
        .selected_dog_id
        .as_ref()
        .and_then(|id| dogs_state.dogs.iter().find(|d| &d.id == id))
    {
        Some(dog) => dog.clone(),
        None => {
            return rsx! {
              Page {
                div { class: "text-center py-8",
                  p { class: "text-gray-500", "No dog selected" }
                }
              }
            };
        },
    };

    let mut show_delete_dialog = use_signal(|| false);

    let handle_delete = {
        let dogs_hook = dogs_hook.clone();
        let dog_id = dog.id.clone();
        move |()| {
            (dogs_hook.delete_dog)(dog_id.clone());
            show_delete_dialog.set(false);
            // Navigate to home after deletion
            navigator.push("/");
        }
    };

    let handle_name_change = {
        let dogs_hook = dogs_hook.clone();
        let mut dog = dog.clone();
        move |new_name: String| {
            dog.name = new_name;
            (dogs_hook.update_dog)(dog.clone());
        }
    };

    let handle_birthday_change = {
        let dogs_hook = dogs_hook.clone();
        let mut dog = dog.clone();
        move |new_birthday: Option<DateTime<Utc>>| {
            debug!("on_change(): birthday {}", new_birthday.unwrap_or_default());
            dog.birthday = new_birthday.map(|dt| dt.date_naive());
            (dogs_hook.update_dog)(dog.clone());
        }
    };

    let handle_breed_change = {
        let dogs_hook = dogs_hook.clone();
        let mut dog = dog.clone();
        move |new_breed: String| {
            debug!("on_change() breed: {new_breed}");
            dog.breed_id = if new_breed.is_empty() { None } else { Some(new_breed) };
            (dogs_hook.update_dog)(dog.clone());
        }
    };

    let handle_weight_change = {
        let dogs_hook = dogs_hook.clone();
        let mut dog = dog.clone();
        move |new_weight: DogWeight| {
            dog.current_weight = Some(*new_weight);
            (dogs_hook.update_dog)(dog.clone());
        }
    };

    let handle_size_change = {
        let dogs_hook = dogs_hook.clone();
        let mut dog = dog.clone();
        move |new_size: DogSize| {
            debug!("on_change() size: {new_size}");
            dog.size = new_size;
            (dogs_hook.update_dog)(dog.clone());
        }
    };

    let handle_image_upload = {
        let dogs_hook = dogs_hook.clone();
        let mut dog = dog.clone();
        move |image_url: String| {
            dog.photo_url = Some(image_url);
            (dogs_hook.update_dog)(dog.clone());
        }
    };

    rsx! {
      Page {
        PageHeaderProfile { dog: dog.clone(), dogs: dogs_state.dogs.clone() }

        Section { colored: true,
          // * Photo Upload
          div { class: "mb-6",
            ImageUpload {
                label: "Photo".to_string(),
                current_image: dog.photo_url.clone(),
                dog_id: Some(dog.id.clone()),
                on_image_selected: handle_image_upload,
            }
          }

          div { class: "space-y-5 my-4 text-sm",
            EditableField {
              label: t!("dog-profile-name"),
              value: dog.name.clone(),
              // on_change: move |new_name: String| handle_name_change(*dog.clone(), new_name),
              on_change: handle_name_change,
            }
            // EditableField {
            //   label: t!("dog-profile-birthday"),
            //   value: dog.birthday.map_or_else(|| "Not set".to_string(), |d| d.to_string()),
            //   on_change: handle_birthday_change,
            // }
            EditableField {
              label: t!("dog-profile-breed"),
              value: dog.breed(),
              on_change: handle_breed_change,
            }
            EditableField {
              label: t!("dog-profile-weight"),
              value: dog.weight(),
              on_change: handle_weight_change,
            }
            EditableField {
              label: t!("dog-profile-size"),
              value: dog.size,
              on_change: handle_size_change,
            }
          }

          Button {
            variant: ButtonVariant::Destructive,
            class: "w-full mt-16",
            onclick: move |_| show_delete_dialog.set(true),
            {t!("dog-profile-delete-dog")}
          }
        }

        DeleteDogDialog {
          is_open: show_delete_dialog(),
          on_close: move || show_delete_dialog.set(false),
          on_delete: handle_delete,
          dog_name: dog.name.clone(),
          dog_id: Some(dog.id),
        }
        ""

        // FoodManagement {
        //   pet_foods: dog.preferred_foods.clone(),
        //   food_history: dog.food_history.clone(),
        //   on_food_added: handle_add_pet_food,
        //   on_food_removed: handle_remove_pet_food,
        //   on_food_preferred: handle_food_preferred,
        // }
        ""
      }
    }
}
