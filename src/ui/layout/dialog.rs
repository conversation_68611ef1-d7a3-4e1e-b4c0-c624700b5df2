use crate::ui::prelude::*;


#[derive(<PERSON>ps, PartialEq, Clone)]
pub struct DialogProps {
    pub children: Element,
    #[props(default)]
    pub open:     bool,
}

#[component]
pub fn Dialog(props: DialogProps) -> Element {
    rsx! {
      if props.open {
        {props.children}
      }
    }
}

#[derive(Props, PartialEq, Clone)]
pub struct DialogContentProps {
    pub children: Element,
    #[props(default)]
    pub class:    String,
}

#[component]
pub fn DialogContent(props: DialogContentProps) -> Element {
    let class = format!(
        "fixed left-1/2 top-1/2 z-50 grid w-full max-w-lg -translate-x-1/2 -translate-y-1/2 gap-4 border \
         bg-background p-6 shadow-lg duration-200 sm:rounded-lg {}",
        props.class
    );
    rsx! {
      div { class: "{class}", {props.children} }
    }
}

#[derive(Props, PartialEq, Clone)]
pub struct DialogHeaderProps {
    pub children: Element,
}

#[component]
pub fn DialogHeader(props: DialogHeaderProps) -> Element {
    rsx! {
      div { class: "flex flex-col space-y-1.5 text-center sm:text-left", {props.children} }
    }
}

#[derive(Props, PartialEq, Clone)]
pub struct DialogFooterProps {
    pub children: Element,
}

#[component]
pub fn DialogFooter(props: DialogFooterProps) -> Element {
    rsx! {
      div { class: "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
        {props.children}
      }
    }
}

#[derive(Props, PartialEq, Clone)]
pub struct DialogTitleProps {
    pub children: Element,
}

#[component]
pub fn DialogTitle(props: DialogTitleProps) -> Element {
    rsx! {
      h2 { class: "text-lg font-semibold leading-none tracking-tight", {props.children} }
    }
}

#[derive(Props, PartialEq, Clone)]
pub struct DialogDescriptionProps {
    pub children: Element,
}

#[component]
pub fn DialogDescription(props: DialogDescriptionProps) -> Element {
    rsx! {
      p { class: "text-sm text-muted-foreground-on-darker", {props.children} }
    }
}
