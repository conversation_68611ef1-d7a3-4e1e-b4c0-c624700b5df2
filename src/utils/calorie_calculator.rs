//! Calorie calculation engine for dogs with veterinary-standard formulas
//!
//! This module implements RER (Resting Energy Requirement) and MER (Maintenance Energy Requirement)
//! calculations with activity multipliers and BCS (Body Condition Score) adjustments.
//!
//! Target accuracy: ≤5% variance vs. veterinary reference

use chrono::{
    DateTime,
    NaiveDate,
    Utc,
};
use serde::Serialize;

use crate::models::{
    activity::ActivityType,
    dogs::{
        Dog,
        DogSize,
        Gender,
    },
};

/// Activity level classifications for energy requirement calculations
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ActivityLevel {
    /// Sedentary lifestyle, minimal exercise
    Sedentary,
    /// Light activity, short walks
    Light,
    /// Moderate activity, regular walks and play
    Moderate,
    /// Active lifestyle, longer exercise sessions
    Active,
    /// Very active, working dogs, extensive exercise
    VeryActive,
    /// Working dogs with heavy physical demands
    Working,
}

/// Life stage classifications affecting energy requirements
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum LifeStage {
    /// Puppies 4-12 months
    Puppy,
    /// Young adults 1-2 years
    YoungAdult,
    /// Adult dogs 2-7 years
    Adult,
    /// Senior dogs 7+ years
    Senior,
    /// Geriatric dogs 10+ years
    Geriatric,
}

/// Neuter status affecting metabolism
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum NeuterStatus {
    Intact,
    Neutered,
}

/// Comprehensive calorie calculation result
#[derive(Debug, Clone, PartialEq, Serialize)]
pub struct CalorieCalculationResult {
    /// Resting Energy Requirement in calories/day
    pub rer_calories:               f32,
    /// Maintenance Energy Requirement in calories/day
    pub mer_calories:               f32,
    /// Activity-adjusted calories in calories/day
    pub activity_adjusted_calories: f32,
    /// BCS-adjusted calories in calories/day
    pub bcs_adjusted_calories:      f32,
    /// Final recommended daily calories
    pub recommended_daily_calories: f32,
    /// Activity multiplier used
    pub activity_multiplier:        f32,
    /// BCS adjustment percentage
    pub bcs_adjustment_percentage:  f32,
    /// Calculation method used for RER
    pub rer_method:                 String,
}

/// Main calorie calculator struct
pub struct CalorieCalculator;

impl CalorieCalculator {
    /// Create a new `CalorieCalculator` instance
    pub const fn new() -> Self { Self }

    /// Calculate comprehensive calorie requirements for a dog
    pub fn calculate_daily_calories(dog: &Dog) -> CalorieCalculationResult {
        let weight_kg = dog.current_weight.unwrap_or(25.0);
        let age = Self::calculate_age_in_months(dog.birthday);
        let life_stage = Self::determine_life_stage(age);
        let activity_level = Self::determine_activity_level(&dog.typical_activity);
        let neuter_status = NeuterStatus::Neutered; // Default assumption, could be added to Dog model

        // Step 1: Calculate RER (Resting Energy Requirement)
        let (rer_calories, rer_method) = Self::calculate_rer(weight_kg);

        // Step 2: Calculate MER (Maintenance Energy Requirement)
        let mer_calories = Self::calculate_mer(rer_calories, life_stage, neuter_status);

        // Step 3: Apply activity multiplier
        let activity_multiplier = Self::get_activity_multiplier(activity_level, life_stage);
        let activity_adjusted_calories = mer_calories * activity_multiplier;

        // Step 4: Apply BCS adjustment
        let bcs_score = dog.body_condition_score.unwrap_or(5);
        let (bcs_adjustment_percentage, bcs_adjusted_calories) =
            Self::apply_bcs_adjustment(activity_adjusted_calories, bcs_score);

        CalorieCalculationResult {
            rer_calories,
            mer_calories,
            activity_adjusted_calories,
            bcs_adjusted_calories,
            recommended_daily_calories: bcs_adjusted_calories,
            activity_multiplier,
            bcs_adjustment_percentage,
            rer_method,
        }
    }

    /// Calculate RER using veterinary-standard formulas
    /// Formula 1: RER = 70 × (body weight in kg)^0.75 (standard)
    /// Formula 2: RER = 30 × (body weight) + 70 (for dogs <2kg or >45kg)
    fn calculate_rer(weight_kg: f32) -> (f32, String) {
        if (2.0..=45.0).contains(&weight_kg) {
            // Standard allometric formula
            let rer = 70.0 * weight_kg.powf(0.75);
            (rer, format!("Allometric formula (70 × {weight_kg}^0.75)"))
        } else {
            // Linear formula for very small or very large dogs
            let rer = 30.0f32.mul_add(weight_kg, 70.0);
            (rer, format!("Linear formula (30 × {weight_kg} + 70)"))
        }
    }

    /// Calculate MER based on life stage and neuter status
    fn calculate_mer(rer: f32, life_stage: LifeStage, neuter_status: NeuterStatus) -> f32 {
        let multiplier = match (life_stage, neuter_status) {
            (LifeStage::Puppy, _) => 2.5, // Puppies need significantly more energy
            (LifeStage::YoungAdult, NeuterStatus::Intact) => 1.8,
            (LifeStage::YoungAdult, NeuterStatus::Neutered) => 1.6,
            (LifeStage::Adult, NeuterStatus::Intact) => 1.8,
            (LifeStage::Adult, NeuterStatus::Neutered) => 1.6,
            (LifeStage::Senior, NeuterStatus::Intact) => 1.4,
            (LifeStage::Senior, NeuterStatus::Neutered) => 1.2,
            (LifeStage::Geriatric, _) => 1.1,
        };

        rer * multiplier
    }

    /// Get activity multiplier based on activity level and life stage
    const fn get_activity_multiplier(activity_level: ActivityLevel, life_stage: LifeStage) -> f32 {
        match activity_level {
            ActivityLevel::Sedentary => 1.0,
            ActivityLevel::Light => match life_stage {
                LifeStage::Puppy => 1.1,
                LifeStage::Senior | LifeStage::Geriatric => 1.05,
                _ => 1.1,
            },
            ActivityLevel::Moderate => match life_stage {
                LifeStage::Puppy => 1.2,
                LifeStage::Senior | LifeStage::Geriatric => 1.1,
                _ => 1.15,
            },
            ActivityLevel::Active => match life_stage {
                LifeStage::Puppy => 1.3,
                LifeStage::Senior | LifeStage::Geriatric => 1.15,
                _ => 1.25,
            },
            ActivityLevel::VeryActive => match life_stage {
                LifeStage::Puppy => 1.4,
                LifeStage::Senior | LifeStage::Geriatric => 1.2,
                _ => 1.4,
            },
            ActivityLevel::Working => match life_stage {
                LifeStage::Puppy => 1.5,
                LifeStage::Senior | LifeStage::Geriatric => 1.25,
                _ => 1.8,
            },
        }
    }

    /// Apply BCS (Body Condition Score) adjustment to calorie requirements
    /// BCS Scale: 1-9 (1=emaciated, 5=ideal, 9=obese)
    fn apply_bcs_adjustment(base_calories: f32, bcs_score: i32) -> (f32, f32) {
        let adjustment_percentage = match bcs_score {
            1 => 40.0,  // Emaciated - increase by 40%
            2 => 30.0,  // Very thin - increase by 30%
            3 => 20.0,  // Thin - increase by 20%
            4 => 10.0,  // Underweight - increase by 10%
            5 => 0.0,   // Ideal - no adjustment
            6 => -10.0, // Overweight - decrease by 10%
            7 => -20.0, // Heavy - decrease by 20%
            8 => -30.0, // Obese - decrease by 30%
            9 => -40.0, // Extremely obese - decrease by 40%
            _ => 0.0,   // Default to no adjustment for invalid scores
        };

        let adjusted_calories = base_calories * (1.0 + adjustment_percentage / 100.0);
        (adjustment_percentage, adjusted_calories)
    }

    /// Determine activity level from dog's typical activity string
    fn determine_activity_level(typical_activity: &Option<String>) -> ActivityLevel {
        let activity_str = typical_activity.as_ref().map(|s| s.to_lowercase());
        match activity_str.as_deref() {
            Some("walking") => ActivityLevel::Light,
            Some("running") => ActivityLevel::Active,
            Some("mixed") => ActivityLevel::Moderate,
            Some("working") => ActivityLevel::Working,
            Some("sedentary") => ActivityLevel::Sedentary,
            Some("very_active") => ActivityLevel::VeryActive,
            _ => ActivityLevel::Moderate, // Default
        }
    }

    /// Determine life stage based on age in months
    const fn determine_life_stage(age_months: Option<i32>) -> LifeStage {
        match age_months {
            Some(months) if months < 12 => LifeStage::Puppy,
            Some(months) if months < 24 => LifeStage::YoungAdult,
            Some(months) if months < 84 => LifeStage::Adult, // 7 years
            Some(months) if months < 120 => LifeStage::Senior, // 10 years
            Some(_) => LifeStage::Geriatric,
            None => LifeStage::Adult, // Default assumption
        }
    }

    /// Calculate age in months from birthday
    fn calculate_age_in_months(birthday: Option<NaiveDate>) -> Option<i32> {
        birthday.map(|birth_date| {
            let now = Utc::now().date_naive();
            let duration = now.signed_duration_since(birth_date);
            (duration.num_days() / 30) as i32 // Approximate months
        })
    }

    /// Calculate calorie requirements for weight management
    /// Returns (`calories_for_current_weight`, `calories_for_target_weight`)
    pub fn calculate_weight_management_calories(
        dog: &Dog,
    ) -> (CalorieCalculationResult, Option<CalorieCalculationResult>) {
        let current_calculation = Self::calculate_daily_calories(dog);

        let target_calculation = if let Some(target_weight) = dog.target_weight {
            let mut target_dog = dog.clone();
            target_dog.current_weight = Some(target_weight);
            Some(Self::calculate_daily_calories(&target_dog))
        } else {
            None
        };

        (current_calculation, target_calculation)
    }

    /// Calculate calories burned during activity
    pub fn calculate_activity_calories(
        dog: &Dog,
        activity_type: ActivityType,
        duration_minutes: u32,
    ) -> f32 {
        let weight_kg = dog.current_weight.unwrap_or(25.0);
        let base_rate = Self::calculate_daily_calories(dog).rer_calories;

        // METs (Metabolic Equivalent of Task) for different activities
        let met_value = match activity_type {
            ActivityType::Walking => 3.0,
            ActivityType::Running => 8.0,
            ActivityType::Mixed => 5.0,
        };

        // Calories per minute = (MET × weight_kg × 3.5) / 200
        let calories_per_minute = (met_value * weight_kg * 3.5) / 200.0;
        calories_per_minute * duration_minutes as f32
    }

    /// Get feeding recommendations based on calorie calculation
    pub fn get_feeding_recommendations(calculation: &CalorieCalculationResult) -> FeedingRecommendations {
        let daily_calories = calculation.recommended_daily_calories;

        FeedingRecommendations {
            total_daily_calories:     daily_calories,
            meals_per_day:            if daily_calories > 1000.0 { 2 } else { 1 },
            calories_per_meal:        daily_calories / if daily_calories > 1000.0 { 2.0 } else { 1.0 },
            treat_allowance_calories: daily_calories * 0.1, // 10% of daily calories for treats
            main_meal_calories:       daily_calories * 0.9, // 90% for main meals
        }
    }
}

/// Feeding recommendations structure
#[derive(Debug, Clone)]
pub struct FeedingRecommendations {
    pub total_daily_calories:     f32,
    pub meals_per_day:            u32,
    pub calories_per_meal:        f32,
    pub treat_allowance_calories: f32,
    pub main_meal_calories:       f32,
}

#[cfg(test)]
mod tests {
    use chrono::NaiveDate;

    use super::*;
    use crate::models::dogs::{
        Dog,
        DogSize,
        Gender,
    };

    #[test]
    fn test_rer_calculation_standard_formula() {
        let (rer, method) = CalorieCalculator::calculate_rer(20.0);
        // Expected: 70 × 20^0.75 = 70 × 11.49 ≈ 804.7
        assert!((rer - 804.7).abs() < 1.0);
        assert!(method.contains("Allometric"));
    }

    #[test]
    fn test_rer_calculation_linear_formula_small_dog() {
        let (rer, method) = CalorieCalculator::calculate_rer(1.5);
        // Expected: 30 × 1.5 + 70 = 115
        assert_eq!(rer, 115.0);
        assert!(method.contains("Linear"));
    }

    #[test]
    fn test_rer_calculation_linear_formula_large_dog() {
        let (rer, method) = CalorieCalculator::calculate_rer(50.0);
        // Expected: 30 × 50 + 70 = 1570
        assert_eq!(rer, 1570.0);
        assert!(method.contains("Linear"));
    }

    #[test]
    fn test_bcs_adjustment() {
        let base_calories = 1000.0;

        // Test ideal weight (BCS 5)
        let (percentage, adjusted) = CalorieCalculator::apply_bcs_adjustment(base_calories, 5);
        assert_eq!(percentage, 0.0);
        assert_eq!(adjusted, 1000.0);

        // Test overweight (BCS 7)
        let (percentage, adjusted) = CalorieCalculator::apply_bcs_adjustment(base_calories, 7);
        assert_eq!(percentage, -20.0);
        assert_eq!(adjusted, 800.0);

        // Test underweight (BCS 3)
        let (percentage, adjusted) = CalorieCalculator::apply_bcs_adjustment(base_calories, 3);
        assert_eq!(percentage, 20.0);
        assert_eq!(adjusted, 1200.0);
    }

    #[test]
    fn test_activity_level_determination() {
        assert_eq!(
            CalorieCalculator::determine_activity_level(&Some("walking".to_string())),
            ActivityLevel::Light
        );
        assert_eq!(
            CalorieCalculator::determine_activity_level(&Some("running".to_string())),
            ActivityLevel::Active
        );
        assert_eq!(
            CalorieCalculator::determine_activity_level(&Some("mixed".to_string())),
            ActivityLevel::Moderate
        );
        assert_eq!(CalorieCalculator::determine_activity_level(&None), ActivityLevel::Moderate);
    }

    #[test]
    fn test_life_stage_determination() {
        assert_eq!(CalorieCalculator::determine_life_stage(Some(6)), LifeStage::Puppy);
        assert_eq!(CalorieCalculator::determine_life_stage(Some(18)), LifeStage::YoungAdult);
        assert_eq!(CalorieCalculator::determine_life_stage(Some(48)), LifeStage::Adult);
        assert_eq!(CalorieCalculator::determine_life_stage(Some(96)), LifeStage::Senior);
        assert_eq!(CalorieCalculator::determine_life_stage(Some(150)), LifeStage::Geriatric);
        assert_eq!(CalorieCalculator::determine_life_stage(None), LifeStage::Adult);
    }

    #[test]
    fn test_full_calorie_calculation() {
        let dog = Dog {
            id:                   "test".to_string(),
            user_id:              "test_user".to_string(),
            name:                 "Test Dog".to_string(),
            breed_id:             None,
            gender:               Gender::Male,
            birthday:             Some(NaiveDate::from_ymd_opt(2020, 1, 1).unwrap()),
            size:                 DogSize::Medium,
            current_weight:       Some(20.0),
            target_weight:        Some(18.0),
            body_condition_score: Some(6), // Slightly overweight
            typical_activity:     Some("walking".to_string()),
            photo_url:            None,
            created_at:           None,
            updated_at:           None,
            
            // Additional fields for compatibility
            activity_level:       Some(crate::models::dogs::ActivityLevel::Moderate),
            is_neutered:          Some(true),
            
            activities:           vec![],
            preferred_foods:      vec![],
            food_history:         vec![],
            daily_calorie_goal:   None,
            daily_activity_goal:  None,
            calorie_progress:     None,
        };

        let result = CalorieCalculator::calculate_daily_calories(&dog);

        // Should be a reasonable calorie count for a 20kg adult dog
        assert!(result.recommended_daily_calories > 500.0);
        assert!(result.recommended_daily_calories < 2000.0);

        // BCS adjustment should reduce calories (BCS 6 = overweight)
        assert!(result.bcs_adjustment_percentage < 0.0);
        assert!(result.bcs_adjusted_calories < result.activity_adjusted_calories);
    }
}
