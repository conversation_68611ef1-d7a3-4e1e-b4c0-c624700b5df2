use dioxus::logger::tracing::{
    error,
    info,
};
use serde::{
    Deserialize,
    Serialize,
};
use std::{
    collections::HashMap,
    fs,
    io::{
        Read,
        Write,
    },
    path::PathBuf,
    sync::{
        LazyLock,
        Mutex,
    },
};

// Simple cross-platform storage using files as fallback
// TODO: Replace with proper dioxus_storage when API is stable
static STORAGE_DIR: LazyLock<PathBuf> = LazyLock::new(|| {
    let mut dir = std::env::temp_dir();
    dir.push("mydoginfit_storage");
    if let Err(e) = fs::create_dir_all(&dir) {
        error!("Failed to create storage directory: {:?}", e);
    }
    dir
});

static MEMORY_CACHE: LazyLock<Mutex<HashMap<String, String>>> = LazyLock::new(|| Mutex::new(HashMap::new()));

fn get_file_path(key: &str) -> PathBuf {
    let mut path = STORAGE_DIR.clone();
    // Sanitize key for filename
    let filename = key.replace(['/', '\\', ':', '*', '?', '"', '<', '>', '|'], "_");
    path.push(format!("{filename}.json"));
    path
}

pub fn save_item<T: Serialize>(key: &str, item: &T) {
    match serde_json::to_string(item) {
        Ok(serialized_item) => {
            // Save to memory cache first for immediate access
            if let Ok(mut cache) = MEMORY_CACHE.lock() {
                cache.insert(key.to_string(), serialized_item.clone());
            }
            
            // Also save to file for persistence
            let file_path = get_file_path(key);
            match fs::File::create(&file_path) {
                Ok(mut file) => {
                    if let Err(e) = file.write_all(serialized_item.as_bytes()) {
                        error!("Error writing item '{}' to file: {:?}", key, e);
                    } else {
                        info!("Successfully saved item '{}' to local storage.", key);
                    }
                },
                Err(e) => {
                    error!("Error creating file for item '{}': {:?}", key, e);
                }
            }
        },
        Err(e) => error!("Error serializing item: {:?}", e),
    }
}

pub fn get_item<T: for<'de> Deserialize<'de>>(key: &str) -> Option<T> {
    // Try memory cache first
    if let Ok(cache) = MEMORY_CACHE.lock()
        && let Some(serialized_item) = cache.get(key) {
            match serde_json::from_str::<T>(serialized_item) {
                Ok(item) => return Some(item),
                Err(e) => {
                    error!("Error deserializing cached item '{}': {:?}", key, e);
                }
            }
        }
    
    // Fallback to file
    let file_path = get_file_path(key);
    if let Ok(mut file) = fs::File::open(&file_path) {
        let mut contents = String::new();
        match file.read_to_string(&mut contents) {
            Ok(_) => {
                // Update cache
                if let Ok(mut cache) = MEMORY_CACHE.lock() {
                    cache.insert(key.to_string(), contents.clone());
                }
                
                match serde_json::from_str::<T>(&contents) {
                    Ok(item) => Some(item),
                    Err(e) => {
                        error!("Error deserializing item '{}': {:?}", key, e);
                        None
                    }
                }
            },
            Err(e) => {
                error!("Error reading file for item '{}': {:?}", key, e);
                None
            }
        }
    } else {
        info!("Item '{}' not found in local storage.", key);
        None
    }
}

pub fn remove_item(key: &str) {
    // Remove from memory cache
    if let Ok(mut cache) = MEMORY_CACHE.lock() {
        cache.remove(key);
    }
    
    // Remove file
    let file_path = get_file_path(key);
    match fs::remove_file(&file_path) {
        Ok(()) => {
            info!("Successfully removed item '{}' from local storage.", key);
        },
        Err(e) => {
            error!("Error removing item '{}' from local storage: {:?}", key, e);
        }
    }
}

// Specific helpers for Pet, Activity, Meal, WeightLog, DogRecommendation, UserProfile
// use crate::models::{
//     Activity,
//     Dog,
//     DogRecommendation,
//     Meal,
//     UserProfile,
//     WeightLog,
// };

pub const APP_STATE_KEY: &str = "app-state";
const PETS_KEY: &str = "pets";
const ACTIVITIES_KEY: &str = "activities";
const MEALS_KEY: &str = "meals";
const WEIGHT_LOGS_KEY: &str = "weight-logs";
const RECOMMENDATIONS_KEY: &str = "recommendations";
const SELECTED_PET_ID_KEY: &str = "selected-pet-id";
const USER_PROFILE_KEY: &str = "user-profile";
const ONBOARDING_COMPLETED_KEY: &str = "onboarding-completed";
const WELCOME_NOTIFICATION_SHOWN_KEY: &str = "welcome-notification-shown";

// pub fn get_dogs() -> Vec<Dog> { get_item(PETS_KEY).unwrap_or_else(Vec::new) }

// pub fn save_dogs(dogs: &[Dog]) { save_item(PETS_KEY, dogs); }

// pub fn get_activities() -> Vec<Activity> { get_item(ACTIVITIES_KEY).unwrap_or_else(Vec::new) }

// pub fn save_activities(activities: &[Activity]) { save_item(ACTIVITIES_KEY, activities); }

// pub fn get_meals() -> Vec<Meal> { get_item(MEALS_KEY).unwrap_or_else(Vec::new) }

// pub fn save_meals(meals: &[Meal]) { save_item(MEALS_KEY, meals); }

// pub fn get_weight_logs() -> Vec<WeightLog> { get_item(WEIGHT_LOGS_KEY).unwrap_or_else(Vec::new) }

// pub fn save_weight_logs(weight_logs: &[WeightLog]) { save_item(WEIGHT_LOGS_KEY, weight_logs); }

// pub fn get_recommendations() -> Vec<DogRecommendation> {
//     get_item(RECOMMENDATIONS_KEY).unwrap_or_else(Vec::new)
// }

// pub fn save_recommendations(recommendations: &[DogRecommendation]) {
//     save_item(RECOMMENDATIONS_KEY, recommendations);
// }

// pub fn get_selected_pet_id() -> Option<String> { get_item(SELECTED_PET_ID_KEY) }

// pub fn save_selected_pet_id(id: &str) { save_item(SELECTED_PET_ID_KEY, &id.to_string()); }

// pub fn remove_selected_pet_id() { remove_item(SELECTED_PET_ID_KEY); }

// pub fn get_user_profile() -> Option<UserProfile> { get_item(USER_PROFILE_KEY) }

// pub fn save_user_profile(profile: &UserProfile) { save_item(USER_PROFILE_KEY, profile); }

// pub fn get_onboarding_completed() -> bool { get_item(ONBOARDING_COMPLETED_KEY).unwrap_or(false) }

// pub fn set_onboarding_completed(completed: bool) { save_item(ONBOARDING_COMPLETED_KEY, &completed); }

// pub fn get_welcome_notification_shown() -> bool {
//     get_item(WELCOME_NOTIFICATION_SHOWN_KEY).unwrap_or(false)
// }

// pub fn set_welcome_notification_shown(shown: bool) { save_item(WELCOME_NOTIFICATION_SHOWN_KEY, &shown); }
