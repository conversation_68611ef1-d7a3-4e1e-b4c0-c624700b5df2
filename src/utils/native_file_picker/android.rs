//! Android native file picker implementation using JNI
//!
//! This module provides a native Android file picker implementation that uses
//! JNI calls via dioxus::mobile::wry to launch native Android Intents for image selection.

use std::{
    collections::HashMap,
    sync::{
        Arc,
        LazyLock,
        Mutex,
    },
};

use tokio::sync::oneshot;

use super::{
    FilePickerError,
    NativeFilePicker,
    SelectedFile,
};

/// Global storage for pending file picker requests
static FILE_PICKER_CALLBACKS: LazyLock<
    Mutex<HashMap<u32, oneshot::Sender<Result<Option<SelectedFile>, FilePickerError>>>>,
> = LazyLock::new(|| Mutex::new(HashMap::new()));

/// Counter for generating unique request IDs
static REQUEST_ID_COUNTER: Mutex<u32> = Mutex::new(1);

pub struct AndroidFilePicker;

impl NativeFilePicker for AndroidFilePicker {
    async fn pick_image() -> Result<Option<SelectedFile>, FilePickerError> {
        tracing::info!("Starting Android native file picker via JNI...");

        // Check and request permissions first
        if !check_storage_permissions() {
            match request_storage_permissions().await {
                Ok(true) => tracing::info!("Storage permissions granted"),
                Ok(false) =>
                    return Err(FilePickerError::AccessDenied("Storage permissions denied".to_string())),
                Err(e) => return Err(e),
            }
        }

        // Launch native image picker using JNI dispatch
        launch_native_image_picker().await
    }
}

/// Launch native Android image picker using JNI dispatch
async fn launch_native_image_picker() -> Result<Option<SelectedFile>, FilePickerError> {
    use dioxus::mobile::wry::prelude::dispatch;

    // Generate unique request ID
    let request_id = {
        let mut counter = REQUEST_ID_COUNTER.lock().unwrap();
        let id = *counter;
        *counter += 1;
        id
    };

    // Create a oneshot channel for the result
    let (sender, receiver) = oneshot::channel();

    // Store the sender in the global callbacks map
    {
        let mut callbacks = FILE_PICKER_CALLBACKS.lock().unwrap();
        callbacks.insert(request_id, sender);
    }

    // Launch the native image picker using JNI dispatch
    let dispatch_result = dispatch(move |env, activity, _webview| {
        match launch_image_picker_intent(env, activity, request_id) {
            Ok(()) => {
                tracing::info!("Image picker intent launched successfully");
            },
            Err(e) => {
                tracing::error!("Failed to launch image picker intent: {}", e);
                // Clean up the callback and send error
                let mut callbacks = FILE_PICKER_CALLBACKS.lock().unwrap();
                if let Some(sender) = callbacks.remove(&request_id) {
                    let _ = sender.send(Err(FilePickerError::NativeError(format!(
                        "Failed to launch image picker: {}",
                        e
                    ))));
                }
            },
        }
    });

    // dispatch returns (), so no error checking needed
    let _ = dispatch_result;

    // Wait for the result from the callback
    match tokio::time::timeout(std::time::Duration::from_secs(60), receiver).await {
        Ok(Ok(result)) => result,
        Ok(Err(_)) => Err(FilePickerError::NativeError("Result channel closed".to_string())),
        Err(_) => {
            // Timeout - clean up the callback
            let mut callbacks = FILE_PICKER_CALLBACKS.lock().unwrap();
            callbacks.remove(&request_id);
            Err(FilePickerError::NativeError("Image picker timeout".to_string()))
        },
    }
}

/// Launch image picker intent using JNI
fn launch_image_picker_intent(
    env: &mut jni::JNIEnv,
    activity: &jni::objects::JObject,
    request_id: u32,
) -> Result<(), jni::errors::Error> {
    // Get Intent class and create new Intent
    let intent_class = env.find_class("android/content/Intent")?;

    // Create Intent with ACTION_GET_CONTENT
    let action_get_content = env.new_string("android.intent.action.GET_CONTENT")?;
    let intent =
        env.new_object(&intent_class, "(Ljava/lang/String;)V", &[jni::objects::JValue::Object(
            &action_get_content,
        )])?;

    // Set type to "image/*"
    let image_type = env.new_string("image/*")?;
    let _ = env.call_method(&intent, "setType", "(Ljava/lang/String;)Landroid/content/Intent;", &[
        jni::objects::JValue::Object(&image_type),
    ])?;

    // Add category OPENABLE to ensure we can read the file
    let category_openable = env.new_string("android.intent.category.OPENABLE")?;
    let _ = env.call_method(&intent, "addCategory", "(Ljava/lang/String;)Landroid/content/Intent;", &[
        jni::objects::JValue::Object(&category_openable),
    ])?;

    // Create chooser intent
    let chooser_title = env.new_string("Select Image")?;
    let chooser_intent = env.call_static_method(
        &intent_class,
        "createChooser",
        "(Landroid/content/Intent;Ljava/lang/CharSequence;)Landroid/content/Intent;",
        &[jni::objects::JValue::Object(&intent), jni::objects::JValue::Object(&chooser_title)],
    )?;

    // Start activity for result
    let _ = env.call_method(activity, "startActivityForResult", "(Landroid/content/Intent;I)V", &[
        jni::objects::JValue::Object(&chooser_intent.l()?),
        jni::objects::JValue::Int(request_id as i32),
    ])?;

    Ok(())
}

/// Handle activity result callback from Android
/// This function should be called from the Android activity's onActivityResult method
#[unsafe(no_mangle)]
pub extern "C" fn Java_com_mydoginfit_MainActivity_onImagePickerResult(
    mut env: jni::JNIEnv,
    _class: jni::objects::JClass,
    request_id: jni::sys::jint,
    result_code: jni::sys::jint,
    data_intent: jni::objects::JObject,
) {
    let request_id = request_id as u32;

    // Get the callback sender
    let sender = {
        let mut callbacks = FILE_PICKER_CALLBACKS.lock().unwrap();
        callbacks.remove(&request_id)
    };

    let sender = match sender {
        Some(s) => s,
        None => {
            tracing::error!("No callback found for request ID: {}", request_id);
            return;
        },
    };

    // Check result code
    const RESULT_OK: i32 = -1; // Activity.RESULT_OK
    const RESULT_CANCELED: i32 = 0; // Activity.RESULT_CANCELED

    let result = match result_code {
        RESULT_CANCELED => Ok(None), // User cancelled
        RESULT_OK =>
            if data_intent.is_null() {
                Err(FilePickerError::NativeError("No data in result intent".to_string()))
            } else {
                match process_image_picker_result(&mut env, &data_intent) {
                    Ok(file) => Ok(Some(file)),
                    Err(e) => Err(e),
                }
            },
        _ => Err(FilePickerError::NativeError(format!("Unknown result code: {}", result_code))),
    };

    // Send the result
    if let Err(_) = sender.send(result) {
        tracing::error!("Failed to send image picker result");
    }
}

/// Process the result intent from the image picker
fn process_image_picker_result(
    env: &mut jni::JNIEnv,
    data_intent: &jni::objects::JObject,
) -> Result<SelectedFile, FilePickerError> {
    // Get the URI from the intent data
    let uri = env
        .call_method(data_intent, "getData", "()Landroid/net/Uri;", &[])
        .map_err(|e| FilePickerError::NativeError(format!("Failed to call getData: {}", e)))?
        .l()
        .map_err(|e| FilePickerError::NativeError(format!("Failed to get URI: {}", e)))?;

    if uri.is_null() {
        return Err(FilePickerError::NoFileSelected);
    }

    // Get application context and content resolver
    let context = get_application_context(env)?;
    let content_resolver = env
        .call_method(&context, "getContentResolver", "()Landroid/content/ContentResolver;", &[])
        .map_err(|e| FilePickerError::NativeError(format!("Failed to call getContentResolver: {}", e)))?
        .l()
        .map_err(|e| FilePickerError::NativeError(format!("Failed to get content resolver: {}", e)))?;

    // Get file name from URI
    let name = get_file_name_from_uri(env, &content_resolver, &uri)?;

    // Get MIME type
    let mime_type_result = env
        .call_method(&content_resolver, "getType", "(Landroid/net/Uri;)Ljava/lang/String;", &[
            jni::objects::JValue::Object(&uri),
        ])
        .map_err(|e| FilePickerError::NativeError(format!("Failed to call getType: {}", e)))?
        .l()
        .map_err(|e| FilePickerError::NativeError(format!("Failed to get MIME type: {}", e)))?;

    let mime_type = if mime_type_result.is_null() {
        "image/jpeg".to_string() // Default fallback
    } else {
        env.get_string(&jni::objects::JString::from(mime_type_result))
            .map_err(|e| FilePickerError::NativeError(format!("Failed to get MIME type string: {}", e)))?
            .to_string_lossy()
            .to_string()
    };

    // Read file data
    let (data, size) = read_file_data_from_uri(env, &content_resolver, &uri)?;

    Ok(SelectedFile {
        name,
        data,
        mime_type,
        size,
    })
}

/// Get application context
fn get_application_context<'a>(
    env: &mut jni::JNIEnv<'a>,
) -> Result<jni::objects::JObject<'a>, FilePickerError> {
    // This is a simplified implementation - in practice, you'd need to store
    // the application context or get it from the activity
    // For now, we'll try to get it via the current activity
    let activity_thread_class = env
        .find_class("android/app/ActivityThread")
        .map_err(|e| FilePickerError::NativeError(format!("Failed to find ActivityThread class: {}", e)))?;

    let current_application = env
        .call_static_method(
            &activity_thread_class,
            "currentApplication",
            "()Landroid/app/Application;",
            &[],
        )?
        .l()
        .map_err(|e| FilePickerError::NativeError(format!("Failed to get current application: {}", e)))?;

    Ok(current_application)
}

/// Get file name from URI
fn get_file_name_from_uri(
    env: &mut jni::JNIEnv,
    content_resolver: &jni::objects::JObject,
    uri: &jni::objects::JObject,
) -> Result<String, FilePickerError> {
    // Query the content resolver for the display name
    let projection = env.new_object_array(1, "java/lang/String", jni::objects::JObject::null())?;
    let display_name_column = env.new_string("_display_name")?;
    env.set_object_array_element(&projection, 0, &display_name_column)?;

    let cursor = env
        .call_method(
            content_resolver,
            "query",
            "(Landroid/net/Uri;[Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;Ljava/lang/String;\
             )Landroid/database/Cursor;",
            &[
                jni::objects::JValue::Object(uri),
                jni::objects::JValue::Object(&projection),
                jni::objects::JValue::Object(&jni::objects::JObject::null()),
                jni::objects::JValue::Object(&jni::objects::JObject::null()),
                jni::objects::JValue::Object(&jni::objects::JObject::null()),
            ],
        )?
        .l()
        .map_err(|e| FilePickerError::NativeError(format!("Failed to query cursor: {}", e)))?;

    if cursor.is_null() {
        return Ok("selected_image.jpg".to_string()); // Fallback name
    }

    let move_to_first: bool = env
        .call_method(&cursor, "moveToFirst", "()Z", &[])?
        .z()
        .map_err(|e| FilePickerError::NativeError(format!("Failed to move cursor: {}", e)))?;

    if !move_to_first {
        let _ = env.call_method(&cursor, "close", "()V", &[]);
        return Ok("selected_image.jpg".to_string()); // Fallback name
    }

    let column_index: i32 = env
        .call_method(&cursor, "getColumnIndex", "(Ljava/lang/String;)I", &[
            jni::objects::JValue::Object(&display_name_column),
        ])?
        .i()
        .map_err(|e| FilePickerError::NativeError(format!("Failed to get column index: {}", e)))?;

    let file_name = if column_index != -1 {
        let name_obj = env
            .call_method(&cursor, "getString", "(I)Ljava/lang/String;", &[jni::objects::JValue::Int(
                column_index,
            )])?
            .l()
            .map_err(|e| {
                FilePickerError::NativeError(format!("Failed to get string from cursor: {}", e))
            })?;

        if name_obj.is_null() {
            "selected_image.jpg".to_string()
        } else {
            env.get_string(&jni::objects::JString::from(name_obj))?
                .to_string_lossy()
                .to_string()
        }
    } else {
        "selected_image.jpg".to_string()
    };

    let _ = env.call_method(&cursor, "close", "()V", &[]);
    Ok(file_name)
}

/// Read file data from URI
fn read_file_data_from_uri(
    env: &mut jni::JNIEnv,
    content_resolver: &jni::objects::JObject,
    uri: &jni::objects::JObject,
) -> Result<(Vec<u8>, u64), FilePickerError> {
    // Open input stream
    let input_stream = env
        .call_method(
            content_resolver,
            "openInputStream",
            "(Landroid/net/Uri;)Ljava/io/InputStream;",
            &[jni::objects::JValue::Object(uri)],
        )?
        .l()
        .map_err(|e| FilePickerError::NativeError(format!("Failed to open input stream: {}", e)))?;

    if input_stream.is_null() {
        return Err(FilePickerError::NativeError("Failed to open input stream".to_string()));
    }

    // Read all bytes from the stream
    let mut data = Vec::new();
    let buffer_size = 8192;
    let buffer = env.new_byte_array(buffer_size)?;

    loop {
        let bytes_read: i32 = env
            .call_method(&input_stream, "read", "([B)I", &[jni::objects::JValue::Object(&buffer)])
            .map_err(|e| FilePickerError::NativeError(format!("Failed to call read method: {}", e)))?
            .i()
            .map_err(|e| FilePickerError::NativeError(format!("Failed to read from stream: {}", e)))?;

        if bytes_read <= 0 {
            break;
        }

        // Ensure we don't read more than the buffer size
        let actual_bytes_read = std::cmp::min(bytes_read as usize, buffer_size as usize);

        // Get the bytes from the buffer
        let mut chunk = vec![0i8; actual_bytes_read];
        env.get_byte_array_region(&buffer, 0, &mut chunk[..])
            .map_err(|e| FilePickerError::NativeError(format!("Failed to get byte array region: {}", e)))?;

        // Convert i8 to u8
        let u8_chunk: Vec<u8> = chunk.iter().map(|&b| b as u8).collect();
        data.extend_from_slice(&u8_chunk);
    }

    // Close the input stream
    let _ = env.call_method(&input_stream, "close", "()V", &[]);

    let size = data.len() as u64;
    Ok((data, size))
}

/// Check if storage permissions are granted
pub fn check_storage_permissions() -> bool {
    use std::sync::{
        Arc,
        Condvar,
        Mutex,
    };

    use dioxus::mobile::wry::prelude::dispatch;

    // Use JNI dispatch to check permissions with proper synchronization
    let result = Arc::new((Mutex::new(false), Condvar::new()));
    let result_clone = result.clone();

    let dispatch_result = dispatch(move |env, activity, _webview| {
        let (lock, cvar) = &*result_clone;
        match check_permission_via_jni(env, activity, "android.permission.READ_EXTERNAL_STORAGE") {
            Ok(granted) => {
                let mut result = lock.lock().unwrap();
                *result = granted;
                cvar.notify_one();
            },
            Err(e) => {
                tracing::error!("Failed to check storage permissions: {}", e);
                let mut result = lock.lock().unwrap();
                *result = false;
                cvar.notify_one();
            },
        }
    });

    // Wait for the result with a timeout
    let (lock, cvar) = &*result;
    let result = lock.lock().unwrap();
    let (result, _) = cvar
        .wait_timeout(result, std::time::Duration::from_secs(5))
        .unwrap();
    *result
}

/// Request storage permissions if not already granted
pub async fn request_storage_permissions() -> Result<bool, FilePickerError> {
    if check_storage_permissions() {
        return Ok(true);
    }

    use dioxus::mobile::wry::prelude::dispatch;

    // Generate unique request ID for permission request
    let request_id = {
        let mut counter = REQUEST_ID_COUNTER.lock().unwrap();
        let id = *counter;
        *counter += 1;
        id
    };

    // Create a oneshot channel for the result
    let (sender, receiver) = oneshot::channel();

    // Store the sender in the global callbacks map (reusing the same map)
    {
        let mut callbacks = FILE_PICKER_CALLBACKS.lock().unwrap();
        callbacks.insert(request_id, sender);
    }

    // Request permissions using JNI dispatch
    let dispatch_result = dispatch(move |env, activity, _webview| {
        match request_permission_via_jni(
            env,
            activity,
            "android.permission.READ_EXTERNAL_STORAGE",
            request_id,
        ) {
            Ok(()) => {
                tracing::info!("Permission request launched successfully");
            },
            Err(e) => {
                tracing::error!("Failed to request permissions: {}", e);
                // Clean up the callback and send error
                let mut callbacks = FILE_PICKER_CALLBACKS.lock().unwrap();
                if let Some(sender) = callbacks.remove(&request_id) {
                    let _ = sender.send(Err(FilePickerError::AccessDenied(format!(
                        "Failed to request permissions: {}",
                        e
                    ))));
                }
            },
        }
    });

    // dispatch returns (), so no error checking needed
    let _ = dispatch_result;

    // Wait for the result from the callback
    match tokio::time::timeout(std::time::Duration::from_secs(30), receiver).await {
        Ok(Ok(Ok(None))) => Ok(true), // Permission granted (None indicates success for permissions)
        Ok(Ok(Err(e))) => Err(e),     // Permission denied or error
        Ok(Ok(Ok(Some(_)))) => {
            // This shouldn't happen for permission requests, but treat as success
            Ok(true)
        },
        Ok(Err(_)) => Err(FilePickerError::NativeError("Result channel closed".to_string())),
        Err(_) => {
            // Timeout - clean up the callback
            let mut callbacks = FILE_PICKER_CALLBACKS.lock().unwrap();
            callbacks.remove(&request_id);
            Err(FilePickerError::AccessDenied("Permission request timeout".to_string()))
        },
    }
}

/// Check if a specific permission is granted via JNI
fn check_permission_via_jni(
    env: &mut jni::JNIEnv,
    activity: &jni::objects::JObject,
    permission: &str,
) -> Result<bool, jni::errors::Error> {
    // Get the permission string
    let permission_string = env.new_string(permission)?;

    // Check permission using ContextCompat.checkSelfPermission
    let context_compat_class = env.find_class("androidx/core/content/ContextCompat")?;
    let permission_result = env.call_static_method(
        &context_compat_class,
        "checkSelfPermission",
        "(Landroid/content/Context;Ljava/lang/String;)I",
        &[jni::objects::JValue::Object(activity), jni::objects::JValue::Object(&permission_string)],
    )?;

    // PackageManager.PERMISSION_GRANTED = 0
    Ok(permission_result.i()? == 0)
}

/// Request a specific permission via JNI
fn request_permission_via_jni(
    env: &mut jni::JNIEnv,
    activity: &jni::objects::JObject,
    permission: &str,
    request_id: u32,
) -> Result<(), jni::errors::Error> {
    // Create permissions array
    let permissions_array = env.new_object_array(1, "java/lang/String", jni::objects::JObject::null())?;
    let permission_string = env.new_string(permission)?;
    env.set_object_array_element(&permissions_array, 0, &permission_string)?;

    // Request permissions using ActivityCompat.requestPermissions
    let activity_compat_class = env.find_class("androidx/core/app/ActivityCompat")?;
    let _ = env.call_static_method(
        &activity_compat_class,
        "requestPermissions",
        "(Landroid/app/Activity;[Ljava/lang/String;I)V",
        &[
            jni::objects::JValue::Object(activity),
            jni::objects::JValue::Object(&permissions_array),
            jni::objects::JValue::Int(request_id as i32),
        ],
    )?;

    Ok(())
}

/// Handle permission request result callback from Android
/// This function should be called from the Android activity's onRequestPermissionsResult method
#[unsafe(no_mangle)]
pub extern "C" fn Java_com_mydoginfit_MainActivity_onPermissionResult(
    mut env: jni::JNIEnv,
    _class: jni::objects::JClass,
    request_id: jni::sys::jint,
    _permissions: jni::objects::JObjectArray,
    grant_results: jni::objects::JIntArray,
) {
    let request_id = request_id as u32;

    // Get the callback sender
    let sender = {
        let mut callbacks = FILE_PICKER_CALLBACKS.lock().unwrap();
        callbacks.remove(&request_id)
    };

    let sender = match sender {
        Some(s) => s,
        None => {
            tracing::error!("No callback found for permission request ID: {}", request_id);
            return;
        },
    };

    // Check if permission was granted
    let result = if grant_results.is_null() {
        Err(FilePickerError::AccessDenied("No grant results".to_string()))
    } else {
        // Get the grant results array
        match unsafe { env.get_array_elements(&grant_results, jni::objects::ReleaseMode::NoCopyBack) } {
            Ok(results) => {
                if results.len() > 0 && results[0] == 0 {
                    // PackageManager.PERMISSION_GRANTED = 0
                    // For permission results, we return None to indicate success without a file
                    Ok(None)
                } else {
                    Err(FilePickerError::AccessDenied("Storage permission denied".to_string()))
                }
            },
            Err(e) => {
                tracing::error!("Failed to get grant results: {}", e);
                Err(FilePickerError::AccessDenied("Failed to check permission results".to_string()))
            },
        }
    };

    // Send the result
    if let Err(_) = sender.send(result) {
        tracing::error!("Failed to send permission result");
    }
}
