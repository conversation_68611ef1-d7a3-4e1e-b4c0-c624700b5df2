//! Web implementation of file picker using HTML input element and Web APIs

use crate::utils::native_file_picker::{FilePickerError, NativeFilePicker, SelectedFile};
use wasm_bindgen::JsCast;
use wasm_bindgen_futures::JsFuture;
use web_sys::{window, HtmlInputElement, FileReader};
use js_sys::Uint8Array;

pub struct WebFilePicker;

impl NativeFilePicker for WebFilePicker {
    async fn pick_image() -> Result<Option<SelectedFile>, FilePickerError> {
        // For web, this should be handled by the HTML input element
        // This is a fallback implementation
        Err(FilePickerError::PlatformNotSupported)
    }
}

/// Read file contents from HTML File using FileReader API
pub async fn read_file_contents(file: &web_sys::File) -> Result<Vec<u8>, FilePickerError> {
    let file_reader = FileReader::new()
        .map_err(|_| FilePickerError::NativeError("Failed to create FileReader".to_string()))?;
    
    file_reader.read_as_array_buffer(file)
        .map_err(|_| FilePickerError::NativeError("Failed to read file as array buffer".to_string()))?;

    // Wait for the file to be read
    let promise = js_sys::Promise::resolve(&wasm_bindgen::JsValue::from(&file_reader));
    JsFuture::from(promise).await
        .map_err(|_| FilePickerError::NativeError("FileReader promise failed".to_string()))?;

    // Get the result as ArrayBuffer and convert to Vec<u8>
    let array_buffer = file_reader.result()
        .map_err(|_| FilePickerError::NativeError("Failed to get FileReader result".to_string()))?;
    
    let uint8_array = Uint8Array::new(&array_buffer);
    let mut contents = vec![0; uint8_array.length() as usize];
    uint8_array.copy_to(&mut contents);
    
    Ok(contents)
}