//! Native file picker implementations for mobile platforms
//! 
//! This module provides platform-specific file picking functionality:
//! - Android: Uses JNI to call native Android file picker APIs
//! - iOS: Uses Objective-C bindings to call native iOS file picker APIs
//! - Web: Falls back to web-sys file input handling

#[cfg(target_os = "android")]
pub mod android;

#[cfg(target_os = "ios")]
pub mod ios;

#[cfg(target_family = "wasm")]
pub mod web;

use crate::hooks::use_image_upload::validate_image_file;

/// Represents a selected file from the native file picker
#[derive(Debug, Clone)]
pub struct SelectedFile {
    pub name: String,
    pub data: Vec<u8>,
    pub mime_type: String,
    pub size: u64,
}

/// Errors that can occur during file picking
#[derive(Debug, thiserror::Error)]
pub enum FilePickerError {
    #[error("File picker cancelled by user")]
    Cancelled,
    #[error("No file selected")]
    NoFileSelected,
    #[error("File access denied: {0}")]
    AccessDenied(String),
    #[error("File too large: {size} bytes (max: {max_size} bytes)")]
    FileTooLarge { size: u64, max_size: u64 },
    #[error("Unsupported file type: {0}")]
    UnsupportedFileType(String),
    #[error("Platform not supported")]
    PlatformNotSupported,
    #[error("Native error: {0}")]
    NativeError(String),
}

impl From<jni::errors::Error> for FilePickerError {
    fn from(error: jni::errors::Error) -> Self {
        FilePickerError::NativeError(error.to_string())
    }
}

/// Native file picker interface
pub trait NativeFilePicker {
    async fn pick_image() -> Result<Option<SelectedFile>, FilePickerError>;
}

/// Pick an image file using the platform-specific implementation
pub async fn pick_image_file() -> Result<Option<SelectedFile>, FilePickerError> {
    #[cfg(target_os = "android")]
    {
        android::AndroidFilePicker::pick_image().await
    }

    #[cfg(target_os = "ios")]
    {
        ios::IOSFilePicker::pick_image().await
    }

    #[cfg(target_family = "wasm")]
    {
        web::WebFilePicker::pick_image().await
    }

    #[cfg(not(any(target_os = "android", target_os = "ios", target_family = "wasm")))]
    {
        Err(FilePickerError::PlatformNotSupported)
    }
}

/// Validate and process a selected file
pub fn validate_selected_file(file: &SelectedFile) -> Result<(), FilePickerError> {
    // Validate file type and size
    validate_image_file(&file.mime_type, file.size)
        .map_err(FilePickerError::UnsupportedFileType)?;

    Ok(())
}