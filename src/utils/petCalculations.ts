import { Pet, PetFood } from "@/data/types"

// Fixed list of sample foods with brand information
const sampleFoods: PetFood[] = [
  {
    id: "food-1",
    name: "Adult Dry Dog Food",
    brand: "Royal Canin",
    caloriesPerCup: 350,
    icon: "beef",
    logo: "https://placehold.co/100x40?text=Royal+Canin",
    isPreferred: false,
  },
  {
    id: "food-2",
    name: "Grain-Free Recipe",
    brand: "Blue Buffalo",
    caloriesPerCup: 370,
    icon: "fish",
    logo: "https://placehold.co/100x40?text=Blue+Buffalo",
    isPreferred: false,
  },
  {
    id: "food-3",
    name: "Weight Management Formula",
    brand: "Hill's",
    caloriesPerCup: 280,
    icon: "wheat",
    logo: "https://placehold.co/100x40?text=Hills",
    isPreferred: false,
  },
  {
    id: "food-4",
    name: "Puppy Formula",
    brand: "Purina",
    caloriesPerCup: 420,
    icon: "milk",
    logo: "https://placehold.co/100x40?text=Purina",
    isPreferred: false,
  },
  {
    id: "food-5",
    name: "Senior Recipe",
    brand: "Pedigree",
    caloriesPerCup: 310,
    icon: "apple",
    logo: "https://placehold.co/100x40?text=Pedigree",
    isPreferred: false,
  },
  {
    id: "food-6",
    name: "Joint Health Formula",
    brand: "Wellness",
    caloriesPerCup: 345,
    icon: "carrot",
    logo: "https://placehold.co/100x40?text=Wellness",
    isPreferred: false,
  },
];

export const calculateDogMetrics = (pet: Pet) => {
  // Calculate BMI (simplified version)
  // A real BMI calculation would be more complex based on breed
  const height = pet.size === "small" ? 30 : pet.size === "medium" ? 45 : 60; // Estimated height in cm
  const bmi = (pet.weight / (height * height)) * 10000;

  // Target BMI based on ideal body condition
  const targetBmi = pet.size === "small" ? 11 : pet.size === "medium" ? 15 : 18;

  // Daily calorie needs (basic RER * activity factor)
  // RER = 70 × (body weight in kg)^0.75
  const rer = 70 * Math.pow(pet.weight, 0.75);

  // Activity factor based on activity level and body condition
  let activityFactor = 1.6; // Default for average active pet

  if (pet.bodyFitState) {
    if (pet.bodyFitState > 3) {
      // Overweight - lower calories
      activityFactor = 1.2;
    } else if (pet.bodyFitState < 3) {
      // Underweight - higher calories
      activityFactor = 1.8;
    }
  }

  // If pet has a dietAdjustment property, apply it
  const adjustmentFactor = pet.dietAdjustment
    ? 1 + pet.dietAdjustment / 100
    : 1;

  const dailyCalories = Math.round(rer * activityFactor * adjustmentFactor);

  // Water intake (ml) = 60ml per kg of body weight
  const waterIntake = Math.round(60 * pet.weight);

  // Activity goal (in minutes) - based on size and age
  const baseActivityGoal = pet.dailyActivityGoal || 60;

  // If pet has an activityAdjustment property, apply it
  const activityAdjustmentFactor = pet.activityAdjustment
    ? 1 + pet.activityAdjustment / 100
    : 1;

  const activityGoal = Math.round(baseActivityGoal * activityAdjustmentFactor);

  // Calories to burn through exercise
  const calorieExpenditure = Math.round(dailyCalories * 0.3);

  // Recommended foods based on pet's characteristics
  // In a real app, this would be a more sophisticated algorithm
  // Get all foods, including the pet's own foods if they've added any
  const allFoods = [
    ...(pet.preferredFoods || []),
    ...sampleFoods.filter(
      (food) =>
        !pet.preferredFoods?.some(
          (pf) => pf.brand === food.brand && pf.name === food.name
        )
    ),
  ];

  // Determine which foods to recommend
  const recommendedFoods = [];

  // First, always recommend the pet's preferred food if one exists
  const preferredFood = pet.preferredFoods?.find((food) => food.isPreferred);
  if (preferredFood) {
    recommendedFoods.push(preferredFood);
  }

  // Add other recommendations based on pet's condition
  if (pet.bodyFitState && pet.bodyFitState > 3) {
    // If overweight, recommend weight management food
    recommendedFoods.push(
      ...allFoods.filter(
        (food) =>
          food.name.toLowerCase().includes("weight") ||
          food.caloriesPerCup < 320
      )
    );
  } else {
    const birthDate = pet.birthday ? new Date(pet.birthday) : null;
    const currentDate = new Date();
    const ageInYears = birthDate
      ? currentDate.getFullYear() - birthDate.getFullYear()
      : 0;

    if (birthDate && ageInYears < 1) {
      // If puppy, recommend puppy food
      recommendedFoods.push(
        ...allFoods.filter((food) => food.name.toLowerCase().includes("puppy"))
      );
    } else if (birthDate && ageInYears > 7) {
      // If senior, recommend senior food
      recommendedFoods.push(
        ...allFoods.filter((food) => food.name.toLowerCase().includes("senior"))
      );
    } else {
      // Otherwise recommend average adult foods
      recommendedFoods.push(
        ...allFoods.filter(
          (food) =>
            !food.name.toLowerCase().includes("puppy") &&
            !food.name.toLowerCase().includes("senior") &&
            !food.name.toLowerCase().includes("weight")
        )
      );
    }
  }

  // Diet composition (macronutrients)
  const dietComposition = {
    protein: 30,
    fat: 20,
    carbs: 50,
  };

  // If the dog is overweight, adjust macros
  if (pet.bodyFitState && pet.bodyFitState > 3) {
    dietComposition.protein = 35;
    dietComposition.fat = 15;
    dietComposition.carbs = 50;
  }

  return {
    bmi,
    targetBmi,
    dailyCalories,
    waterIntake,
    activityGoal,
    calorieExpenditure,
    dietComposition,
    allFoods, // All possible foods
    recommendedFoods: [...new Set(recommendedFoods)], // Unique recommended foods
  };
};
