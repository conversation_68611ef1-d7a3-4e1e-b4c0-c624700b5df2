
/**
 * Main breed recognition module
 * Integrates all utilities for image processing and breed detection
 */

import { isFile, isBlob, fileToBase64, imageUrlToCanvas } from './fileUtils';
import { extractImageFeatures } from './imageAnalysis';
import { matchBreedByFeatures } from './breedMatcher';
import type { BreedRecognitionResult } from './breedData';

/**
 * Advanced breed recognition using image analysis
 */
export const recognizeDogBreed = async (
  image: string | File | Blob
): Promise<BreedRecognitionResult | null> => {
  try {
    console.log('Recognizing dog breed using advanced approach...');
    
    // Process the image to get a URL we can use
    let imageUrl = '';
    
    if (isFile(image) || isBlob(image)) {
      imageUrl = await fileToBase64(image);
    } else if (typeof image === 'string') {
      if (image.startsWith('data:') || image.startsWith('blob:')) {
        imageUrl = image;
      } else {
        // For regular URLs, we'll load it into a canvas first to handle CORS
        const canvas = await imageUrlToCanvas(image);
        if (canvas) {
          imageUrl = canvas.toDataURL('image/jpeg');
        } else {
          imageUrl = image; // Try direct URL as fallback
        }
      }
    }
    
    if (!imageUrl) {
      throw new Error('Could not process image');
    }
    
    // Extract image features
    const features = await extractImageFeatures(imageUrl);
    console.log('Advanced image features:', features);
    
    // Match the features to a breed
    const result = matchBreedByFeatures(features);
    
    return result;
  } catch (error) {
    console.error('Error recognizing dog breed:', error);
    return null;
  }
};
