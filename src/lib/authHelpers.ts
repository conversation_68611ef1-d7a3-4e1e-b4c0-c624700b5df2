
import { supabase } from '@/integrations/supabase/client'
import {
  getPets, savePets,
  getActivities, saveActivities,
  getMeals, saveMeals,
  getWeightLogs, saveWeightLogs,
  getRecommendations, saveRecommendations,
  getUserProfile, saveUserProfile,
  setSessionExpiry, clearSessionData,
  isSessionValid
} from './localStorageHelpers';
import { Pet, Activity, Meal, WeightLog, DogRecommendation, UserProfile } from '@/data/types'
import { toast } from '@/hooks/use-toast'

// Function to sync local data with Supabase after login
export async function syncDataWithSupabase(userId: string): Promise<void> {
  try {
    // Get local data
    const localPets = getPets();
    const localActivities = getActivities();
    const localMeals = getMeals();
    const localWeightLogs = getWeightLogs();

    // Sync profile data
    const { data: profileData } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (profileData) {
      saveUserProfile({
        email: profileData.email,
        notificationsEnabled: true,
        surveys: false,
        newsletter: false
      });
    }

    // Sync pets data
    if (localPets.length > 0) {
      // For each local pet, upsert to Supabase
      for (const pet of localPets) {
        // Convert Date objects to ISO strings for Supabase
        const lastWeightUpdateStr = pet.lastWeightUpdate instanceof Date
          ? pet.lastWeightUpdate.toISOString()
          : pet.lastWeightUpdate;

        const lastPhotoUpdateStr = pet.lastPhotoUpdate instanceof Date
          ? pet.lastPhotoUpdate.toISOString()
          : pet.lastPhotoUpdate;

        const petData = {
          id: pet.id,
          owner_id: userId,
          name: pet.name,
          type: pet.type,
          breed: pet.breed,
          birthday: pet.birthday,
          weight: pet.weight,
          target_weight: pet.targetWeight,
          image: pet.image,
          daily_calorie_goal: pet.dailyCalorieGoal,
          daily_activity_goal: pet.dailyActivityGoal,
          size: pet.size,
          body_fit_state: pet.bodyFitState,
          bmi: pet.bcs,
          target_bmi: pet.targetBmi,
          water_consumption: pet.waterConsumption,
          last_weight_update: lastWeightUpdateStr,
          last_photo_update: lastPhotoUpdateStr
        };

        const { error } = await supabase
          .from('pets')
          .upsert(petData);

        if (error) {
          console.error('Error syncing pet data:', error);
        }
      }
    }

    // Get pets from Supabase
    const { data: supabasePets, error: petsError } = await supabase
      .from('pets')
      .select('*')
      .eq('owner_id', userId);

    if (petsError) {
      console.error('Error fetching pets:', petsError);
    } else if (supabasePets) {
      // Convert Supabase pets to local format and merge with existing
      const convertedPets: Pet[] = supabasePets.map(dbPet => {
        // Make sure bodyFitState is of the correct type
        let bodyFitStateVal: 1 | 2 | 3 | 4 | 5 | number | undefined = dbPet.body_fit_state;

        // If it's outside the 1-5 range, default to 3
        if (bodyFitStateVal && (bodyFitStateVal < 1 || bodyFitStateVal > 5)) {
          bodyFitStateVal = 3;
        }

        return {
          id: dbPet.id,
          name: dbPet.name,
          type: dbPet.type,
          breed: dbPet.breed,
          age: dbPet.age,
          weight: dbPet.weight,
          targetWeight: dbPet.target_weight,
          image: dbPet.image,
          dailyCalorieGoal: dbPet.daily_calorie_goal,
          dailyActivityGoal: dbPet.daily_activity_goal,
          activityProgress: 0, // We'll calculate this from activities
          calorieProgress: 0, // We'll calculate this from meals
          size: dbPet.size as Pet['size'] || 'medium', // Cast to the proper type
          bodyFitState: bodyFitStateVal,
          bmi: dbPet.bmi,
          targetBmi: dbPet.target_bmi,
          waterConsumption: dbPet.water_consumption,
          lastWeightUpdate: dbPet.last_weight_update,
          lastPhotoUpdate: dbPet.last_photo_update
        };
      });

      // Merge with local pets (prefer remote)
      savePets(convertedPets);
    }

    // Similar sync for activities, meals, weight logs, etc.
    // For brevity, we'll focus on the main implementation

    // Set session expiry
    setSessionExpiry();

    toast({
      title: "Account synced",
      description: "Your data has been synced with your account."
    });
  } catch (error) {
    console.error('Error syncing data:', error);
    toast({
      title: "Sync failed",
      description: "Failed to sync your data. Please try again later.",
      variant: "destructive"
    });
  }
}

// Function to handle login
export async function handleLogin(email: string, password: string): Promise<boolean> {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      toast({
        title: "Login failed",
        description: error.message,
        variant: "destructive"
      });
      return false;
    }

    if (data.user) {
      // Sync local data with Supabase
      await syncDataWithSupabase(data.user.id);
      return true;
    }

    return false;
  } catch (error) {
    console.error('Login error:', error);
    return false;
  }
}

// Function to handle signup
export async function handleSignup(email: string, password: string): Promise<boolean> {
  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password
    });

    if (error) {
      toast({
        title: "Signup failed",
        description: error.message,
        variant: "destructive"
      });
      return false;
    }

    toast({
      title: "Account created",
      description: "Please check your email for verification."
    });

    return true;
  } catch (error) {
    console.error('Signup error:', error);
    return false;
  }
}

// Function to handle Google login
export async function handleGoogleLogin(): Promise<boolean> {
  try {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/settings`
      }
    });

    if (error) {
      toast({
        title: "Login failed",
        description: error.message,
        variant: "destructive"
      });
      return false;
    }

    return true;
  } catch (error) {
    console.error('Google login error:', error);
    return false;
  }
}

// Function to handle Apple login
export async function handleAppleLogin(): Promise<boolean> {
  // Check if on iOS device
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;

  if (!isIOS) {
    toast({
      title: "Not supported",
      description: "Apple login is only available on iOS devices.",
      variant: "destructive"
    });
    return false;
  }

  try {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'apple',
      options: {
        redirectTo: `${window.location.origin}/settings`
      }
    });

    if (error) {
      toast({
        title: "Login failed",
        description: error.message,
        variant: "destructive"
      });
      return false;
    }

    return true;
  } catch (error) {
    console.error('Apple login error:', error);
    return false;
  }
}

// Function to handle logout
export async function handleLogout(): Promise<boolean> {
  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      toast({
        title: "Logout failed",
        description: error.message,
        variant: "destructive"
      });
      return false;
    }

    // Clear session data but keep local data
    clearSessionData();

    toast({
      title: "Logged out",
      description: "You have been logged out successfully."
    });

    return true;
  } catch (error) {
    console.error('Logout error:', error);
    return false;
  }
}

export function initializeAuth(callback: (user: any) => void): () => void {
  const { data: { subscription } } = supabase.auth.onAuthStateChange(
    (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        // Set session expiry
        setSessionExpiry();
        // Sync data with Supabase
        syncDataWithSupabase(session.user.id);
        // Call callback with user
        callback(session.user);
      } else if (event === 'SIGNED_OUT') {
        // Clear session data
        clearSessionData();
        // Call callback with null
        callback(null);
      }
    }
  );

  // Also check for existing session on load
  supabase.auth.getSession().then(({ data: { session } }) => {
    if (session?.user) {
      if (!isSessionValid()) {
        // If session is expired, sign out
        handleLogout();
      } else {
        // Call callback with user
        callback(session.user);
      }
    }
  });

  return () => {
    subscription.unsubscribe();
  };
}
