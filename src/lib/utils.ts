import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import {format as dateFormat} from 'date-fns'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

  // Helper function to format dates safely
export const formatDate = (date: Date | string): string => {
  if(typeof date === 'string') {
    return date;
  }
  return dateFormat(date, 'PPP');
};
