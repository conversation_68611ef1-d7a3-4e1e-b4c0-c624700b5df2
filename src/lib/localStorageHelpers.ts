
import { Pet, Activity, Meal, WeightLog, DogRecommendation, UserProfile } from '@/data/types'

// Storage keys
const STORAGE_KEYS = {
  ACTIVITIES: 'activities',
  LOGIN_PROMPT_DATE: 'loginPromptDate',
  MEALS: 'meals',
  PETS: 'pets',
  RECOMMENDATIONS: 'recommendations',
  SELECTED_PET_ID: 'selectedPetId',
  SESSION_EXPIRY: 'sessionExpiry',
  USER_PROFILE: 'userProfile',
  WEIGHT_LOGS: 'weightLogs',
};

// Generic storage functions
export function getFromStorage<T>(key: string, defaultValue: T): T {
  try {
    const stored = localStorage.getItem(key);
    return stored ? JSON.parse(stored) : defaultValue;
  } catch (error) {
    console.error(`Error reading from storage (${key}):`, error);
    return defaultValue;
  }
}

export function saveToStorage<T>(key: string, data: T): void {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error(`Error saving to storage (${key}):`, error);
  }
}

// Specific storage functions for pets
export function getPets(): Pet[] {
  return getFromStorage<Pet[]>(STORAGE_KEYS.PETS, []);
}

export function savePets(pets: Pet[]): void {
  saveToStorage(STORAGE_KEYS.PETS, pets);
}

export function getPetById(id: string): Pet | undefined {
  return getPets().find(pet => pet.id === id);
}

export function getSelectedPetId(): string {
  return getFromStorage<string>(STORAGE_KEYS.SELECTED_PET_ID, '');
}

export function saveSelectedPetId(id: string): void {
  saveToStorage(STORAGE_KEYS.SELECTED_PET_ID, id);
}

export function savePet(pet: Pet): void {
  const pets = getPets();
  const existingIndex = pets.findIndex(p => p.id === pet.id);

  if (existingIndex >= 0) {
    pets[existingIndex] = pet;
  } else {
    pets.push(pet);
  }

  savePets(pets);
}

// Specific storage functions for activities
export function getActivities(): Activity[] {
  return getFromStorage<Activity[]>(STORAGE_KEYS.ACTIVITIES, []);
}

export function saveActivities(activities: Activity[]): void {
  saveToStorage(STORAGE_KEYS.ACTIVITIES, activities);
}

export function getActivitiesByPetId(petId: string): Activity[] {
  return getActivities().filter(activity => activity.petId === petId);
}

export function saveActivity(activity: Activity): void {
  const activities = getActivities();
  const existingIndex = activities.findIndex(a => a.id === activity.id);

  if (existingIndex >= 0) {
    activities[existingIndex] = activity;
  } else {
    activities.push(activity);
  }

  saveActivities(activities);
}

// Specific storage functions for meals
export function getMeals(): Meal[] {
  return getFromStorage<Meal[]>(STORAGE_KEYS.MEALS, []);
}

export function saveMeals(meals: Meal[]): void {
  saveToStorage(STORAGE_KEYS.MEALS, meals);
}

export function getMealsByPetId(petId: string): Meal[] {
  return getMeals().filter(meal => meal.petId === petId);
}

export function saveMeal(meal: Meal): void {
  const meals = getMeals();
  const existingIndex = meals.findIndex(m => m.id === meal.id);

  if (existingIndex >= 0) {
    meals[existingIndex] = meal;
  } else {
    meals.push(meal);
  }

  saveMeals(meals);
}

// Specific storage functions for weight logs
export function getWeightLogs(): WeightLog[] {
  return getFromStorage<WeightLog[]>(STORAGE_KEYS.WEIGHT_LOGS, []);
}

export function saveWeightLogs(weightLogs: WeightLog[]): void {
  saveToStorage(STORAGE_KEYS.WEIGHT_LOGS, weightLogs);
}

export function getWeightLogsByPetId(petId: string): WeightLog[] {
  return getWeightLogs().filter(log => log.petId === petId);
}

export function saveWeightLog(weightLog: WeightLog): void {
  const weightLogs = getWeightLogs();
  const existingIndex = weightLogs.findIndex(w => w.id === weightLog.id);

  if (existingIndex >= 0) {
    weightLogs[existingIndex] = weightLog;
  } else {
    weightLogs.push(weightLog);
  }

  saveWeightLogs(weightLogs);
}

// Specific storage functions for recommendations
export function getRecommendations(): DogRecommendation[] {
  return getFromStorage<DogRecommendation[]>(STORAGE_KEYS.RECOMMENDATIONS, []);
}

export function saveRecommendations(recommendations: DogRecommendation[]): void {
  saveToStorage(STORAGE_KEYS.RECOMMENDATIONS, recommendations);
}

// User profile and session management
export function getUserProfile(): UserProfile | null {
  return getFromStorage<UserProfile | null>(STORAGE_KEYS.USER_PROFILE, null);
}

export function saveUserProfile(profile: UserProfile | null): void {
  saveToStorage(STORAGE_KEYS.USER_PROFILE, profile);
}

// Login prompt management
export function shouldShowLoginPrompt(): boolean {
  const lastPromptDate = getFromStorage<string | null>(STORAGE_KEYS.LOGIN_PROMPT_DATE, null);
  if (!lastPromptDate) return true;

  const lastDate = new Date(lastPromptDate);
  const now = new Date();
  const diffDays = (now.getTime() - lastDate.getTime()) / (1000 * 3600 * 24);

  return diffDays >= 3;
}

export function setLoginPromptShown(): void {
  saveToStorage(STORAGE_KEYS.LOGIN_PROMPT_DATE, new Date().toISOString());
}

// Session management
export function setSessionExpiry(): void {
  // Set session expiry to 1 year from now
  const expiryDate = new Date();
  expiryDate.setFullYear(expiryDate.getFullYear() + 1);
  saveToStorage(STORAGE_KEYS.SESSION_EXPIRY, expiryDate.toISOString());
}

export function getSessionExpiry(): Date | null {
  const expiry = getFromStorage<string | null>(STORAGE_KEYS.SESSION_EXPIRY, null);
  return expiry ? new Date(expiry) : null;
}

export function isSessionValid(): boolean {
  const expiry = getSessionExpiry();
  return expiry !== null && new Date() < expiry;
}

export function clearSessionData(): void {
  localStorage.removeItem(STORAGE_KEYS.SESSION_EXPIRY);
  localStorage.removeItem(STORAGE_KEYS.USER_PROFILE);
}
