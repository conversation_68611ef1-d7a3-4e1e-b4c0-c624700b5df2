
import {useState, useEffect} from 'react'
import {DogR<PERSON>ommendation} from '@/data/types'
import {getRecommendations, saveRecommendations} from '@/lib/localStorageHelpers'

export const useRecommendationData = () => {
  const [recommendations, setRecommendations] = useState<DogRecommendation[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load data from localStorage on component mount
  useEffect(() => {
    setRecommendations(getRecommendations());
    setIsLoaded(true);
  }, []);

  return {
    recommendations,
    isLoaded
  };
};
