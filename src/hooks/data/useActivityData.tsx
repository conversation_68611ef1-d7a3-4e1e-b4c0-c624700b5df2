
import {useState, useEffect} from 'react'
import {Activity} from '@/data/types'
import {getActivities, saveActivities} from '@/lib/localStorageHelpers'
import {v4 as uuidv4} from 'uuid'

export const useActivityData = () => {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load data from localStorage on component mount
  useEffect(() => {
    const loadedActivities = getActivities();
    setActivities(loadedActivities);
    setIsLoaded(true);
  }, []);

  // Activity CRUD operations
  const addActivity = (activityData: Omit<Activity, 'id'>): Activity => {
    const newActivity: Activity = {
      ...activityData,
      id: uuidv4()
    };
    const updatedActivities = [...activities, newActivity];
    setActivities(updatedActivities);
    saveActivities(updatedActivities);
    return newActivity;
  };

  const updateActivity = (updatedActivity: Activity): void => {
    const updatedActivities = activities.map(activity =>
      activity.id === updatedActivity.id ? updatedActivity : activity
    );
    setActivities(updatedActivities);
    saveActivities(updatedActivities);
  };

  const deleteActivity = (id: string): void => {
    const updatedActivities = activities.filter(activity => activity.id !== id);
    setActivities(updatedActivities);
    saveActivities(updatedActivities);
  };

  const getActivitiesByPetId = (petId: string): Activity[] => {
    return activities.filter(activity => activity.petId === petId);
  };

  return {
    activities,
    addActivity,
    updateActivity,
    deleteActivity,
    getActivitiesByPetId,
    isLoaded,
  };
};
