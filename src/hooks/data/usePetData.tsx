import {useState, useEffect} from 'react'
import {Pet, PetFood} from '@/data/types'
import {getPets, savePets, getSelectedPetId, saveSelectedPetId} from '@/lib/localStorageHelpers'
import {v4 as uuidv4} from 'uuid'

export const usePetData = () => {
  const [pets, setPets] = useState<Pet[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);
  const [selectedPetId, setSelectedPetId] = useState('');

  // Load data from localStorage on component mount
  useEffect(() => {
    if(isLoaded) return;

    const pets = getPets()
    pets.forEach(pet => {
      if(typeof pet.birthday === 'string') {
        pet.birthday = new Date(pet.birthday);
      }
      if(typeof pet.lastWeightUpdate === 'string') {
        pet.lastWeightUpdate = new Date(pet.lastWeightUpdate);
      }
      if(typeof pet.lastPhotoUpdate === 'string') {
        pet.lastPhotoUpdate = new Date(pet.lastPhotoUpdate);
      }
    });

    let selectedPetId = getSelectedPetId();
    if(!getPetById(selectedPetId)) {
      selectedPetId = pets.length > 0 ? pets[0].id : '';
    }

    setPets(pets);
    setSelectedPetId(selectedPetId);
    setIsLoaded(true);
  }, []);

  // Pet CRUD operations
  const addPet = (petData: Omit<Pet, 'id'>): Pet => {
    const newPet: Pet = {
      ...petData,
      id: uuidv4(),
      preferredFoods: [],
      treatsToday: 0,
      dietAdjustment: 0,
      activityAdjustment: 0
    };
    const updatedPets = [...pets, newPet];
    setPets(updatedPets);
    savePets(updatedPets);
    selectPet(newPet.id);

    return newPet;
  };

  const updatePet = (updatedPet: Pet): void => {
    const updatedPets = pets.map(pet =>
      pet.id === updatedPet.id ? updatedPet : pet
    );
    setPets(updatedPets);
    savePets(updatedPets);
  };

  const deletePet = (id: string): void => {
    const updatedPets = pets.filter(pet => pet.id !== id);
    setPets(updatedPets);
    savePets(updatedPets);
    if(selectedPetId === id) {
      setSelectedPetId(updatedPets[0]?.id || '');
      saveSelectedPetId(updatedPets[0]?.id || '');
    }
  };

  const getPetById = (id: string): Pet | undefined => {
    return pets.find(pet => pet.id === id);
  };

  const selectPet = (id: string): void => {
    setSelectedPetId(id);
    saveSelectedPetId(id);
  };

  // Food management
  const addPetFood = (petId: string, food: PetFood): void => {
    const pet = getPetById(petId);
    if(!pet) return;

    const updatedPet = {
      ...pet,
      preferredFoods: [...(pet.preferredFoods || []), food]
    };

    updatePet(updatedPet);
  };

  const removePetFood = (petId: string, foodId: string): void => {
    const pet = getPetById(petId);
    if(!pet || !pet.preferredFoods) return;

    const updatedPet = {
      ...pet,
      preferredFoods: pet.preferredFoods.filter(food => food.id !== foodId)
    };

    updatePet(updatedPet);
  };

  const setFoodPreferred = (petId: string, foodId: string, isPreferred: boolean): void => {
    const pet = getPetById(petId);
    if(!pet || !pet.preferredFoods) return;

    const updatedPet = {
      ...pet,
      preferredFoods: pet.preferredFoods.map(food =>
        food.id === foodId ? {...food, isPreferred} : food
      )
    };

    updatePet(updatedPet);
  };

  // Treat tracking
  const addTreat = (petId: string): void => {
    const pet = getPetById(petId);
    if(!pet) return;

    const treatsToday = (pet.treatsToday || 0) + 1;

    // Calculate diet adjustment based on treats
    // Each treat adds 5% to daily calorie adjustment
    const dietAdjustment = Math.min(treatsToday * 5, 30); // Cap at 30%

    const updatedPet = {
      ...pet,
      treatsToday,
      dietAdjustment
    };

    updatePet(updatedPet);
  };

  const removeTreat = (petId: string): void => {
    const pet = getPetById(petId);
    if(!pet || !(pet.treatsToday && pet.treatsToday > 0)) return;

    const treatsToday = pet.treatsToday - 1;

    // Recalculate diet adjustment
    const dietAdjustment = Math.max(treatsToday * 5, 0);

    const updatedPet = {
      ...pet,
      treatsToday,
      dietAdjustment
    };

    updatePet(updatedPet);
  };

  // Activity adjustment
  const adjustActivityBasedOnWalk = (petId: string, duration: number, intensity: string): void => {
    const pet = getPetById(petId);
    if(!pet) return;

    // Calculate adjustment based on activity duration and intensity
    // For high intensity activities, we might want to increase tomorrow's calories
    const intensityFactor = intensity === 'running' ? 2 : intensity === 'walking' ? 1 : 1.5;
    const activityImpact = (duration / pet.dailyActivityGoal) * 100 * intensityFactor;

    // If activity exceeds goal by a significant amount, adjust tomorrow's recommendations
    let activityAdjustment = 0;
    let dietAdjustment = pet.dietAdjustment || 0;

    if(activityImpact > 120) { // If activity exceeds 120% of daily goal
      activityAdjustment = 10; // Increase tomorrow's activity goal by 10%
      dietAdjustment += 5; // Increase tomorrow's calories by 5%
    }

    const updatedPet = {
      ...pet,
      activityAdjustment,
      dietAdjustment
    };

    updatePet(updatedPet);
  };

  const resetDailyTracking = (): void => {
    // This function would be called at midnight to reset daily tracking
    const updatedPets = pets.map(pet => ({
      ...pet,
      treatsToday: 0,
      // Keep the dietAdjustment from the previous day's activity,
      // but reset any adjustment from treats
      dietAdjustment: pet.activityAdjustment || 0,
    }));

    setPets(updatedPets);
    savePets(updatedPets);
  };

  return {
    addPet,
    addPetFood,
    addTreat,
    adjustActivityBasedOnWalk,
    deletePet,
    getPetById,
    isLoaded,
    pets,
    removePetFood,
    removeTreat,
    resetDailyTracking,
    setFoodPreferred,
    selectedPetId,
    selectPet,
    updatePet,
  };
};
