
import {useState, useEffect} from 'react'
import {WeightLog} from '@/data/types'
import {getWeightLogs, saveWeightLogs} from '@/lib/localStorageHelpers'
import {v4 as uuidv4} from 'uuid'

export const useWeightLogData = () => {
  const [weightLogs, setWeightLogs] = useState<WeightLog[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load data from localStorage on component mount
  useEffect(() => {
    setWeightLogs(getWeightLogs());
    setIsLoaded(true);
  }, []);

  // Weight Log CRUD operations
  const addWeightLog = (weightLogData: Omit<WeightLog, 'id'>): WeightLog => {
    const newWeightLog: WeightLog = {
      ...weightLogData,
      id: uuidv4()
    };
    const updatedWeightLogs = [...weightLogs, newWeightLog];
    setWeightLogs(updatedWeightLogs);
    saveWeightLogs(updatedWeightLogs);
    return newWeightLog;
  };

  const updateWeightLog = (updatedWeightLog: WeightLog): void => {
    const updatedWeightLogs = weightLogs.map(log =>
      log.id === updatedWeightLog.id ? updatedWeightLog : log
    );
    setWeightLogs(updatedWeightLogs);
    saveWeightLogs(updatedWeightLogs);
  };

  const deleteWeightLog = (id: string): void => {
    const updatedWeightLogs = weightLogs.filter(log => log.id !== id);
    setWeightLogs(updatedWeightLogs);
    saveWeightLogs(updatedWeightLogs);
  };

  const getWeightLogsByPetId = (petId: string): WeightLog[] => {
    return weightLogs.filter(log => log.petId === petId);
  };

  return {
    weightLogs,
    addWeightLog,
    updateWeightLog,
    deleteWeightLog,
    getWeightLogsByPetId,
    isLoaded
  };
};
