
import {useState, useEffect} from 'react'
import {Meal} from '@/data/types'
import {getMeals, saveMeals} from '@/lib/localStorageHelpers'
import {v4 as uuidv4} from 'uuid'

export const useMealData = () => {
  const [meals, setMeals] = useState<Meal[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load data from localStorage on component mount
  useEffect(() => {
    setMeals(getMeals());
    setIsLoaded(true);
  }, []);

  // Meal CRUD operations
  const addMeal = (mealData: Omit<Meal, 'id'>): Meal => {
    const newMeal: Meal = {
      ...mealData,
      id: uuidv4()
    };
    const updatedMeals = [...meals, newMeal];
    setMeals(updatedMeals);
    saveMeals(updatedMeals);
    return newMeal;
  };

  const updateMeal = (updatedMeal: Meal): void => {
    const updatedMeals = meals.map(meal =>
      meal.id === updatedMeal.id ? updatedMeal : meal
    );
    setMeals(updatedMeals);
    saveMeals(updatedMeals);
  };

  const deleteMeal = (id: string): void => {
    const updatedMeals = meals.filter(meal => meal.id !== id);
    setMeals(updatedMeals);
    saveMeals(updatedMeals);
  };

  const getMealsByPetId = (petId: string): Meal[] => {
    return meals.filter(meal => meal.petId === petId);
  };

  return {
    meals,
    addMeal,
    updateMeal,
    deleteMeal,
    getMealsByPetId,
    isLoaded
  };
};
