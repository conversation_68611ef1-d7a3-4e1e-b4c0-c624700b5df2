
import React, {createContext, useState, useContext, useEffect, ReactNode} from 'react'
import {Language, Translations, translations, formatString} from '@/i18n'

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: Translations;
  format: (key: string, params?: Record<string, string | number>) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider = ({children}: {children: ReactNode}) => {
  const [language, setLanguage] = useState<Language>(() => {
    // Try to get saved language from localStorage, fallback to 'en'
    const savedLanguage = localStorage.getItem('language') as Language | null;
    return savedLanguage && ['en', 'es', 'ru'].includes(savedLanguage) ? savedLanguage : 'en';
  });

  // Current translations
  const t = translations[language];

  useEffect(() => {
    // Save language preference to localStorage when it changes
    localStorage.setItem('language', language);
  }, [language]);

  // Format a translation string with parameters
  const format = (key: string, params?: Record<string, string | number>): string => {
    if(!key || typeof key !== 'string') {
      return String(key || '');
    }

    // Traverse the nested object using the dot notation
    const keys = key.split('.');
    let value: any = t;

    for(const k of keys) {
      value = value?.[k];
      if(value === undefined) return key; // Return the key if translation not found
    }

    if(typeof value !== 'string') return key;
    return formatString(value, params);
  };

  return (
    <LanguageContext.Provider value={{language, setLanguage, t, format}}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if(context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }

  return context;
};
