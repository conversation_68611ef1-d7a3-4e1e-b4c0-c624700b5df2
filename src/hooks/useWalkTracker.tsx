import React, {createContext, useContext, useState, useEffect} from 'react'
import {useToast} from '@/hooks/use-toast'
import {useLocalData} from '@/hooks/useLocalData'

interface WalkTrackerContextType {
  isWalking: boolean;
  duration: number;
  startTime: Date | null;
  startWalk: () => void;
  pauseWalk: () => void;
  continueWalk: () => void;
  endWalk: () => void;
  showActivityModal: boolean;
  setShowActivityModal: (show: boolean) => void;
  completeWalk: (type: string) => void;
  getCurrentDuration: () => number;
  debugMode: boolean;
  toggleDebugMode: () => void;
}

interface WalkState {
  isWalking: boolean;
  startTime: string | null;
  duration: number;
  statusChangeTime: number; // Timestamp when the status last changed
}

const WalkTrackerContext = createContext<WalkTrackerContextType | undefined>(undefined);

export const WalkTrackerProvider: React.FC<{children: React.ReactNode}> = ({children}) => {
  const [isWalking, setIsWalking] = useState(false);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [duration, setDuration] = useState(0);
  const [statusChangeTime, setStatusChangeTime] = useState<number | null>(null);
  const [showActivityModal, setShowActivityModal] = useState(false);
  const [debugMode, setDebugMode] = useState(false);
  const {toast} = useToast();
  const {adjustActivityBasedOnWalk} = useLocalData();
  const {pets} = useLocalData();
  const selectedPetId = pets[0]?.id || '';

  // Enhanced toggle debug mode function
  const toggleDebugMode = () => {
    const newDebugMode = !debugMode;
    setDebugMode(newDebugMode);

    if(newDebugMode) {
      // Log useful debugging information when enabled
      console.log('=== DEBUG MODE ENABLED ===');
      console.log('Device Information:');
      console.log('- User Agent:', navigator.userAgent);
      console.log('- Platform:', /iPad|iPhone|iPod/.test(navigator.userAgent) ? 'iOS' : /Android/.test(navigator.userAgent) ? 'Android' : 'Other');

      console.log('\nPWA Status:');
      console.log('- Display Mode (matchMedia):', window.matchMedia('(display-mode: standalone)').matches ? 'Standalone' : 'Browser');
      console.log('- Navigator Standalone:', (window.navigator as any).standalone ? 'Yes' : 'No');
      console.log('- isIOSStandalone() result:', isIOSStandalone() ? 'Yes' : 'No');

      console.log('\nNotification Status:');
      console.log('- Permission:', Notification.permission);
      console.log('- API Available:', 'Notification' in window ? 'Yes' : 'No');

      console.log('\nService Worker Status:');
      console.log('- API Available:', 'serviceWorker' in navigator ? 'Yes' : 'No');
      if('serviceWorker' in navigator) {
        console.log('- Controller:', navigator.serviceWorker.controller ? 'Active' : 'Not active');
        navigator.serviceWorker.getRegistrations().then(registrations => {
          console.log('- Registrations:', registrations.length);
          registrations.forEach((reg, i) => {
            console.log(`  Registration ${i + 1}:`, {
              scope: reg.scope,
              updateViaCache: reg.updateViaCache,
              active: reg.active ? 'Yes' : 'No'
            });
          });
        });
      }

      console.log('\nWalk Tracker State:');
      console.log('- isWalking:', isWalking);
      console.log('- duration:', duration);
      console.log('- startTime:', startTime);
      console.log('- statusChangeTime:', statusChangeTime);
      console.log('- localStorage walkTrackerState:', localStorage.getItem('walkTrackerState'));

      // Show debug toast
      toast({
        title: "Debug mode enabled",
        description: "Check browser console for detailed information",
        duration: 5000,
      });

      // If service worker is active, request its debug info
      if('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: 'DEBUG_INFO_REQUEST'
        });
      }
    } else {
      // Log when debug mode is disabled
      console.log('Debug mode disabled');
      toast({
        title: "Debug mode disabled",
        duration: 3000,
      });
    }
  };

  // Calculate current duration based on status change time
  const getCurrentDuration = (): number => {
    if(!isWalking || !statusChangeTime) return duration;

    const now = Date.now();
    const elapsedSeconds = Math.floor((now - statusChangeTime) / 1000);
    return duration + elapsedSeconds;
  };


  // Save walk state whenever relevant state changes
  useEffect(() => {
    // Save walk state to localStorage
    if(isWalking || duration > 0) {
      const walkState: WalkState = {
        isWalking,
        startTime: startTime?.toISOString() || null,
        duration,
        statusChangeTime: statusChangeTime || Date.now()
      };
      localStorage.setItem('walkTrackerState', JSON.stringify(walkState));
    } else {
      localStorage.removeItem('walkTrackerState');
    }
  }, [isWalking, duration, startTime, statusChangeTime]);

  // Register service worker
  useEffect(() => {
    if('serviceWorker' in navigator) {
      console.log('Registering service worker (useWalkTracker)...');
      navigator.serviceWorker.register('/service-worker.js')
        .then(registration => {
          console.log('Service Worker registered with scope:', registration.scope);
          navigator.serviceWorker.ready.then(function(reg) {
            // console.log('Service Worker is ready:', reg);
            reg.active.postMessage({
              type: 'GET_WALK_STATE'
            });
            // console.log('navigator.serviceWorker', navigator.serviceWorker);
            // if('serviceWorker' in navigator && navigator.serviceWorker.controller) {
            //   console.log('Requesting walk state from service worker...');
            //   navigator.serviceWorker.controller.postMessage({
            //     type: 'GET_WALK_STATE'
            //   });
            // }
          });
        })
        .catch(error => {
          console.error('Service Worker registration failed:', error);
        });
    }
  }, []);

  // Listen for messages from service worker
  useEffect(() => {
    if('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        console.log('Message received from service worker:', event.data);
        if(event.data && event.data.type === 'WALK_UPDATE') {
          // Update base duration and status change time from service worker
          if(event.data.duration !== undefined) {
            setDuration(event.data.duration);
          }
          if(event.data.statusChangeTime !== undefined) {
            setStatusChangeTime(event.data.statusChangeTime);
          }
          if(event.data.isWalking !== undefined) {
            setIsWalking(event.data.isWalking);
          }
        }
        else if(event.data && event.data.type === 'WALK_PAUSED') {
          setIsWalking(false);
          if(event.data.duration !== undefined) {
            setDuration(event.data.duration);
          }
          if(event.data.statusChangeTime !== undefined) {
            setStatusChangeTime(event.data.statusChangeTime);
          }
        }
        else if(event.data && event.data.type === 'WALK_CONTINUED') {
          setIsWalking(true);
          if(event.data.duration !== undefined) {
            setDuration(event.data.duration);
          }
          if(event.data.statusChangeTime !== undefined) {
            setStatusChangeTime(event.data.statusChangeTime);
          }
        }
        else if(event.data && event.data.type === 'WALK_ENDED' || event.data && event.data.type === 'WALK_COMPLETED') {
          setIsWalking(false);
          if(event.data.duration !== undefined) {
            setDuration(event.data.duration);
          }
          setStatusChangeTime(null);

          if(event.data.duration && event.data.duration >= 60) { // Only show modal if walk was at least 1 minute
            setShowActivityModal(true);
          } else {
            // Reset walk tracker for short walks
            setDuration(0);
            setStartTime(null);
            localStorage.removeItem('walkTrackerState');
          }
        }
        else if(event.data && event.data.type === 'WALK_STATE') {
          if(event.data.walkData) {
            setIsWalking(event.data.walkData.isWalking);

            // If currentDuration is provided, use it
            if(event.data.walkData.currentDuration !== undefined) {
              setDuration(event.data.walkData.currentDuration);
            } else {
              setDuration(event.data.walkData.duration);
            }

            setStartTime(new Date(event.data.walkData.startTime));
            setStatusChangeTime(event.data.walkData.statusChangeTime);
          }
        }
      });
    }
  }, []);

  // When the app starts, check if there's an ongoing walk in the service worker
  useEffect(() => {
    if('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      console.log('Requesting walk state from service worker...');
      navigator.serviceWorker.controller.postMessage({
        type: 'GET_WALK_STATE'
      });
    }
  }, []);

  // Load saved walk state from localStorage
  useEffect(() => {
    const savedWalkState = localStorage.getItem('walkTrackerState');
    if(savedWalkState) {
      try {
        const walkState: WalkState = JSON.parse(savedWalkState);

        setIsWalking(walkState.isWalking);
        setStartTime(walkState.startTime ? new Date(walkState.startTime) : null);
        setStatusChangeTime(walkState.statusChangeTime);

        // Calculate current duration based on status change time
        if(walkState.isWalking) {
          const now = Date.now();
          const elapsedSeconds = Math.floor((now - walkState.statusChangeTime) / 1000);
          setDuration(walkState.duration + elapsedSeconds);
        } else {
          setDuration(walkState.duration);
        }

        // Sync with service worker - preserve the statusChangeTime
        if('serviceWorker' in navigator && navigator.serviceWorker.controller) {
          const walkData = {
            startTime: walkState.startTime ? new Date(walkState.startTime).getTime() : null,
            duration: walkState.duration,
            isWalking: walkState.isWalking,
            statusChangeTime: walkState.statusChangeTime
          };

          navigator.serviceWorker.controller.postMessage({
            type: 'SYNC_WALK_STATE',
            walkData
          });
        }
      } catch(error) {
        console.error('Error parsing walk state:', error);
        localStorage.removeItem('walkTrackerState');
      }
    }
  }, []);

  // Handle page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if(document.visibilityState === 'visible') {
        // App came back to foreground, sync with service worker
        if('serviceWorker' in navigator && navigator.serviceWorker.controller) {
          navigator.serviceWorker.controller.postMessage({
            type: 'GET_WALK_STATE'
          });
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Improved standalone detection for iOS
  const isIOSStandalone = () => {
    // Check if running on iOS
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;
    if(!isIOS) return false;

    // Multiple ways to detect standalone mode
    const standalone =
      // Standard way to detect standalone mode
      window.matchMedia('(display-mode: standalone)').matches ||
      // iOS-specific way
      (window.navigator as any).standalone === true ||
      // Check if the display-mode is standalone in any media query
      window.matchMedia('(display-mode: standalone)').matches ||
      // Check URL parameters (can be used for testing)
      window.location.search.includes('standalone=true');

    console.log('iOS standalone detection:', {
      mediaQuery: window.matchMedia('(display-mode: standalone)').matches,
      navigatorStandalone: (window.navigator as any).standalone,
      result: standalone
    });

    return standalone;
  };

  // Update the requestNotificationPermission function with improved iOS detection
  const requestNotificationPermission = async () => {
    // Check if Notification API is supported
    if(!('Notification' in window)) {
      console.log('Notifications not supported on this device');
      return false;
    }

    // Detect platform
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;
    const isAndroid = /Android/.test(navigator.userAgent);

    console.log('Platform detection:', {isIOS, isAndroid});

    // iOS-specific handling
    if(isIOS) {
      // Check if app is installed as PWA with improved detection
      const isStandalone = isIOSStandalone();
      console.log('iOS standalone status:', isStandalone);

      // Skip the standalone check for testing purposes
      // const isStandalone = true;

      if(!isStandalone) {
        toast({
          title: "Install as App",
          description: "For background notifications, add this app to your Home Screen first (tap share icon → Add to Home Screen).",
          duration: 6000,
        });
        return false;
      }

      // For iOS PWA, always try to request permission explicitly
      try {
        console.log('Requesting notification permission on iOS...');
        // Request permission
        const permission = await Notification.requestPermission();
        console.log('iOS permission result:', permission);

        if(permission === 'granted') {
          // For iOS, we need to register for push notifications
          if('serviceWorker' in navigator && navigator.serviceWorker.controller) {
            navigator.serviceWorker.controller.postMessage({
              type: 'REGISTER_IOS_PUSH'
            });
          }

          toast({
            title: "Notifications enabled",
            description: "You'll receive updates about your dog's walk even when the app is in the background.",
            duration: 3000,
          });
          return true;
        } else {
          toast({
            title: "Notifications blocked",
            description: "To enable notifications, go to iOS Settings → Notifications → My Dog In Fit.",
            duration: 5000,
          });
          return false;
        }
      } catch(error) {
        console.error('Error requesting notification permission on iOS:', error);
        toast({
          title: "Permission error",
          description: "There was a problem enabling notifications on iOS.",
          variant: "destructive",
          duration: 3000,
        });
        return false;
      }
    }

    // Standard permission flow for non-iOS devices
    if(Notification.permission === 'granted') {
      return true;
    }

    if(Notification.permission === 'denied') {
      if(isAndroid) {
        toast({
          title: "Notifications blocked",
          description: "Please enable notifications in your device settings → Apps → My Dog In Fit → Notifications.",
          duration: 5000,
        });
      } else {
        toast({
          title: "Notifications blocked",
          description: "Please enable notifications in your browser settings to receive walk updates.",
          duration: 5000,
        });
      }
      return false;
    }

    try {
      const permission = await Notification.requestPermission();

      if(permission === 'granted') {
        toast({
          title: "Notifications enabled",
          description: "You'll receive updates about your dog's walk even when the app is in the background.",
          duration: 3000,
        });

        if(isAndroid && 'serviceWorker' in navigator && navigator.serviceWorker.controller) {
          navigator.serviceWorker.controller.postMessage({
            type: 'CHECK_BACKGROUND_PERMISSION'
          });
        }

        return true;
      } else {
        if(isAndroid) {
          toast({
            title: "Notifications blocked",
            description: "To enable notifications, go to Settings → Apps → My Dog In Fit → Notifications.",
            duration: 5000,
          });
        } else {
          toast({
            title: "Notifications blocked",
            description: "Please enable notifications in your browser settings.",
            duration: 5000,
          });
        }
        return false;
      }
    } catch(error) {
      console.error('Error requesting notification permission:', error);
      toast({
        title: "Permission error",
        description: "There was a problem requesting notification permission.",
        variant: "destructive",
        duration: 3000,
      });
      return false;
    }
  };

  // Update the startWalk function with improved iOS detection
  const startWalk = async () => {
    // Detect platform first for logging
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;
    const isAndroid = /Android/.test(navigator.userAgent);

    console.log('Starting walk, platform detection:', {isIOS, isAndroid});

    // Request notification permission - this is called in response to a user action
    const permissionGranted = await requestNotificationPermission();

    // Log the permission status for debugging
    console.log('Notification permission status:', permissionGranted ? 'granted' : 'not granted');

    // iOS-specific handling for PWA with improved detection
    if(isIOS) {
      const isStandalone = isIOSStandalone();
      console.log('iOS standalone status in startWalk:', isStandalone);

      if(!isStandalone) {
        // For iOS browser, show a different message
        toast({
          title: "Limited functionality",
          description: "For full background tracking, install this app to your Home Screen first.",
          duration: 5000,
        });
      }
    }

    // Check for wake lock API support (keeps screen on during walks)
    let wakeLockSupported = false;
    if('wakeLock' in navigator) {
      wakeLockSupported = true;
      try {
        // Try to acquire a wake lock
        const wakeLock = await (navigator as any).wakeLock.request('screen');
        console.log('Wake lock acquired');

        // Release it when the walk ends or page visibility changes
        document.addEventListener('visibilitychange', () => {
          if(document.visibilityState === 'visible' && isWalking) {
            // Re-acquire wake lock when page becomes visible again and walk is active
            (navigator as any).wakeLock.request('screen').catch(err => {
              console.error('Failed to re-acquire wake lock:', err);
            });
          }
        });
      } catch(err) {
        console.error('Failed to acquire wake lock:', err);
      }
    }

    // Continue with starting the walk regardless of permission status
    const now = Date.now();
    const newStartTime = new Date(now);
    setIsWalking(true);
    setStartTime(newStartTime);
    setDuration(0);
    setStatusChangeTime(now);

    // Show appropriate toast based on permissions and platform
    // if(permissionGranted) {
    //   toast({
    //     title: "Walk started with notifications",
    //     description: "We'll keep tracking even when the app is in the background.",
    //   });
    // } else {
    //   if(isIOS) {
    //     toast({
    //       title: "Walk started",
    //       description: "Keep the app open or install as Home Screen app for background tracking.",
    //     });
    //   } else {
    //     toast({
    //       title: "Walk started",
    //       description: "Keep the app open to track your dog's activity.",
    //     });
    //   }
    // }

    // Notify service worker
    if('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'START_WALK',
        startTime: now,
        duration: 0,
        statusChangeTime: now,
        notificationsEnabled: permissionGranted,
        platform: isIOS ? 'ios' : /Android/.test(navigator.userAgent) ? 'android' : 'other'
      });
    }
  };

  const pauseWalk = () => {
    // Calculate elapsed time since status change
    const now = Date.now();
    if(statusChangeTime) {
      const elapsedSeconds = Math.floor((now - statusChangeTime) / 1000);
      const newDuration = duration + elapsedSeconds;
      setDuration(newDuration);
    }

    setIsWalking(false);
    setStatusChangeTime(now);

    // toast({
    //   title: "Walk paused",
    //   description: "You can resume tracking anytime.",
    // });

    // Notify service worker
    if('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'PAUSE_WALK'
      });
    }
  };

  const continueWalk = () => {
    const now = Date.now();
    setIsWalking(true);
    setStatusChangeTime(now);

    // toast({
    //   title: "Walk continued",
    //   description: "We're tracking your dog's activity again.",
    // });

    // Notify service worker
    if('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'CONTINUE_WALK'
      });
    }
  };

  const endWalk = () => {
    // Calculate final duration
    const finalDuration = getCurrentDuration();
    setDuration(finalDuration);
    setIsWalking(false);
    setStatusChangeTime(null);

    if(finalDuration < 60) { // Less than a minute
      // toast({
      //   title: "Walk too short",
      //   description: "Walks should be at least 1 minute long.",
      //   variant: "destructive",
      // });

      // Reset walk tracker for short walks
      setDuration(0);
      setStartTime(null);
      localStorage.removeItem('walkTrackerState');
    } else {
      // Show the activity type modal
      setShowActivityModal(true);
    }

    // Notify service worker
    if('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'END_WALK'
      });
    }
  };

  const completeWalk = (type: string) => {
    // Calculate calories burned (simplified formula)
    const caloriesBurned = Math.round(duration / 60 * 100); // ~100 calories per hour of walking

    // Create walk details
    const walkDetails = {
      duration,
      type,
      calories: caloriesBurned,
      date: new Date().toISOString(),
    };

    // Adjust tomorrow's recommendations based on today's activity
    if(selectedPetId) {
      adjustActivityBasedOnWalk(selectedPetId, duration, type);
    }

    // Reset walk tracker
    setDuration(0);
    setStartTime(null);
    setStatusChangeTime(null);
    setShowActivityModal(false);

    // Clear localStorage
    localStorage.removeItem('walkTrackerState');

    // Show success toast
    toast({
      title: "Walk completed",
      description: `Great job! You completed a ${Math.floor(duration / 60)} min ${type.toLowerCase()}.`,
    });
  };

  return (
    <WalkTrackerContext.Provider
      value={{
        isWalking,
        duration,
        startTime,
        startWalk,
        pauseWalk,
        continueWalk,
        endWalk,
        showActivityModal,
        setShowActivityModal,
        completeWalk,
        getCurrentDuration,
        debugMode,
        toggleDebugMode
      }}
    >
      {children}
    </WalkTrackerContext.Provider>
  );
};

export const useWalkTracker = (): WalkTrackerContextType => {
  const context = useContext(WalkTrackerContext);
  if(context === undefined) {
    throw new Error('useWalkTracker must be used within a WalkTrackerProvider');
  }
  return context;
};
