
import { useState, useEffect, createContext, useContext, ReactNode } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { initializeAuth } from '@/lib/authHelpers';
import { shouldShowLoginPrompt, setLoginPromptShown } from '@/lib/localStorageHelpers';

interface AuthContextType {
  user: any | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  shouldPromptLogin: boolean;
  markLoginPrompted: () => void;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  isLoading: true,
  isAuthenticated: false,
  shouldPromptLogin: false,
  markLoginPrompted: () => {},
});

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [shouldPromptLogin, setShouldPromptLogin] = useState(false);

  useEffect(() => {
    // Check if we should show login prompt
    setShouldPromptLogin(shouldShowLoginPrompt());
    
    // Initialize auth and listen for changes
    const unsubscribe = initializeAuth((newUser) => {
      setUser(newUser);
      setIsLoading(false);
    });
    
    return () => {
      unsubscribe();
    };
  }, []);

  const markLoginPrompted = () => {
    setLoginPromptShown();
    setShouldPromptLogin(false);
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated: !!user,
        shouldPromptLogin,
        markLoginPrompted,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);

export default useAuth;
