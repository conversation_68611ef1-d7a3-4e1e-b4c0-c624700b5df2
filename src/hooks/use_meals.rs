use chrono::NaiveDate;
use dioxus::{prelude::*, signals::Signal};

use crate::{
    config::Config,
    hooks::use_auth,
    integrations::supabase::client::SupabaseClient,
    models::{
        food::Food,
        meals::{
            Meal,
            MealItem,
            MealSummary,
            MealType,
            NewMeal,
        },
    },
};

const MEALS_STORAGE_KEY: &str = "meals_list";
const FOOD_DATABASE_KEY: &str = "food_database";

#[derive(Clone, Default)]
pub struct MealsState {
    pub meals: Vec<Meal>,
    pub daily_summary: Option<MealSummary>,
    pub is_loading: bool,
    pub error: Option<String>,
    pub food_database: Vec<Food>,
    pub food_loading: bool,
}

#[derive(Clone)]
pub struct UseMealsHook {
    pub state: Signal<MealsState>,
    pub load_meals: Callback<(String, NaiveDate), ()>, // dog_id, date
    pub load_meals_range: Callback<(String, NaiveDate, NaiveDate), ()>, // dog_id, start_date, end_date
    pub create_meal: Callback<NewMeal, ()>,
    pub update_meal: Callback<Meal, ()>,
    pub delete_meal: Callback<String, ()>, // meal_id
    pub load_food_database: Callback<(), ()>,
    pub get_daily_summary: Callback<(String, NaiveDate), ()>, // dog_id, date
}

pub fn use_meals() -> UseMealsHook {
    let mut meals_state = use_signal(MealsState::default);
    let auth = use_auth();
    let config = use_signal(Config::from_env);
    let client = use_signal(|| SupabaseClient::new(config.read().clone()));

    // Initialize from local storage on startup
    use_effect(move || {
        spawn(async move {
            // For now, skip local storage initialization
            // TODO: Implement proper offline-first storage when dioxus-storage API is stable
            meals_state.write().is_loading = false;
        });
    });

    // Helper function to get access token
    let get_access_token = move || -> Option<String> {
        auth.state.read().access_token.clone()
    };

    let load_meals = use_callback(move |params: (String, NaiveDate)| {
        let (dog_id, date) = params;
        meals_state.write().is_loading = true;
        meals_state.write().error = None;
        
        spawn(async move {
            let access_token = get_access_token();
            
            // Query meals for specific dog and date
            let query = format!("dog_id=eq.{dog_id}&date=eq.{date}");
            
            match client.read().fetch_filtered::<Meal>("meals", &query, access_token.as_deref()).await {
                Ok(mut meals) => {
                    // Load meal items for each meal
                    for meal in &mut meals {
                        let items_query = format!("meal_id=eq.{}", meal.id);
                        match client.read().fetch_filtered::<MealItem>("meal_items", &items_query, access_token.as_deref()).await {
                            Ok(items) => {
                                meal.meal_items = Some(items);
                            },
                            Err(e) => {
                                meals_state.write().error = Some(format!("Failed to load meal items: {e}"));
                            }
                        }
                    }
                    
                    meals_state.write().meals = meals;
                    meals_state.write().is_loading = false;
                    
                    // TODO: Store to local storage when API is stable
                },
                Err(e) => {
                    meals_state.write().error = Some(format!("Failed to load meals: {e}"));
                    meals_state.write().is_loading = false;
                }
            }
        });
    });

    let load_meals_range = use_callback(move |params: (String, NaiveDate, NaiveDate)| {
        let (dog_id, start_date, end_date) = params;
        meals_state.write().is_loading = true;
        meals_state.write().error = None;
        
        spawn(async move {
            let access_token = get_access_token();
            
            // Query meals for date range
            let query = format!("dog_id=eq.{dog_id}&date=gte.{start_date}&date=lte.{end_date}");
            
            match client.read().fetch_filtered::<Meal>("meals", &query, access_token.as_deref()).await {
                Ok(mut meals) => {
                    // Load meal items for each meal
                    for meal in &mut meals {
                        let items_query = format!("meal_id=eq.{}", meal.id);
                        if let Ok(items) = client.read().fetch_filtered::<MealItem>("meal_items", &items_query, access_token.as_deref()).await {
                            meal.meal_items = Some(items);
                        } else {
                            // Continue without meal items if they fail to load
                        }
                    }
                    
                    meals_state.write().meals = meals;
                    meals_state.write().is_loading = false;
                    
                    // TODO: Store to local storage when API is stable
                },
                Err(e) => {
                    meals_state.write().error = Some(format!("Failed to load meals: {e}"));
                    meals_state.write().is_loading = false;
                }
            }
        });
    });

    let create_meal = use_callback(move |new_meal: NewMeal| {
        meals_state.write().is_loading = true;
        meals_state.write().error = None;
        
        spawn(async move {
            let access_token = get_access_token();
            
            // Calculate total calories from meal items
            let mut total_calories = 0.0;
            let food_db = meals_state.read().food_database.clone();
            
            for item in &new_meal.meal_items {
                if let Some(food) = food_db.iter().find(|f| f.id == item.food_id) {
                    total_calories += (item.quantity_grams / 100.0) * food.calories_per_100g as f32;
                }
            }
            
            // Create the meal first
            let meal = Meal {
                id: uuid::Uuid::new_v4().to_string(),
                dog_id: new_meal.dog_id.clone(),
                date: new_meal.date,
                meal_type: new_meal.meal_type.clone(),
                total_calories,
                notes: new_meal.notes.clone(),
                created_at: Some(chrono::Utc::now()),
                meal_items: None,
            };
            
            match client.read().insert::<Meal>("meals", &meal, access_token.as_deref()).await {
                Ok(saved_meal) => {
                    let meal_id = saved_meal.id.clone();
                    
                    // Create meal items
                    let mut saved_items = Vec::new();
                    for item in new_meal.meal_items {
                        let food_calories = if let Some(food) = food_db.iter().find(|f| f.id == item.food_id) {
                            (item.quantity_grams / 100.0) * food.calories_per_100g as f32
                        } else {
                            0.0
                        };
                        
                        let meal_item = MealItem {
                            id: uuid::Uuid::new_v4().to_string(),
                            meal_id: meal_id.clone(),
                            food_id: item.food_id,
                            quantity_grams: item.quantity_grams,
                            calories: food_calories,
                            created_at: Some(chrono::Utc::now()),
                            food: None,
                        };
                        
                        match client.read().insert::<MealItem>("meal_items", &meal_item, access_token.as_deref()).await {
                            Ok(saved_item) => {
                                saved_items.push(saved_item);
                            },
                            Err(e) => {
                                meals_state.write().error = Some(format!("Failed to create meal item: {e}"));
                                return;
                            }
                        }
                    }
                    
                    // Create complete meal with items for state
                    let mut complete_meal = saved_meal;
                    complete_meal.meal_items = Some(saved_items);
                    
                    meals_state.write().meals.push(complete_meal);
                    meals_state.write().is_loading = false;
                    
                    // TODO: Store to local storage when API is stable
                },
                Err(e) => {
                    meals_state.write().error = Some(format!("Failed to create meal: {e}"));
                    meals_state.write().is_loading = false;
                }
            }
        });
    });

    let update_meal = use_callback(move |meal: Meal| {
        meals_state.write().is_loading = true;
        meals_state.write().error = None;
        
        spawn(async move {
            let access_token = get_access_token();
            
            match client.read().update::<Meal>("meals", &meal, &meal.id, access_token.as_deref()).await {
                Ok(updated_meal) => {
                    // Update the meal in the list
                    let meal_index = meals_state.read().meals.iter().position(|m| m.id == meal.id);
                    if let Some(index) = meal_index {
                        meals_state.write().meals[index] = updated_meal;
                    }
                    meals_state.write().is_loading = false;
                    
                    // TODO: Store to local storage when API is stable
                },
                Err(e) => {
                    meals_state.write().error = Some(format!("Failed to update meal: {e}"));
                    meals_state.write().is_loading = false;
                }
            }
        });
    });

    let delete_meal = use_callback(move |meal_id: String| {
        meals_state.write().is_loading = true;
        meals_state.write().error = None;
        
        spawn(async move {
            let access_token = get_access_token();
            
            // First delete meal items
            let items_query = format!("meal_id=eq.{meal_id}");
            if let Ok(items) = client.read().fetch_filtered::<MealItem>("meal_items", &items_query, access_token.as_deref()).await {
                for item in items {
                    if let Err(e) = client.read().delete("meal_items", &item.id, access_token.as_deref()).await {
                        meals_state.write().error = Some(format!("Failed to delete meal item: {e}"));
                        meals_state.write().is_loading = false;
                        return;
                    }
                }
            } else {
                // Continue even if we can't fetch items
            }
            
            // Then delete the meal
            match client.read().delete("meals", &meal_id, access_token.as_deref()).await {
                Ok(()) => {
                    meals_state.write().meals.retain(|m| m.id != meal_id);
                    meals_state.write().is_loading = false;
                    
                    // TODO: Store to local storage when API is stable
                },
                Err(e) => {
                    meals_state.write().error = Some(format!("Failed to delete meal: {e}"));
                    meals_state.write().is_loading = false;
                }
            }
        });
    });

    let load_food_database = use_callback(move |(): ()| {
        meals_state.write().food_loading = true;
        
        spawn(async move {
            let access_token = get_access_token();
            
            match client.read().fetch_all::<Food>("foods", access_token.as_deref()).await {
                Ok(foods) => {
                    meals_state.write().food_database = foods;
                    meals_state.write().food_loading = false;
                    
                    // TODO: Store to local storage when API is stable
                },
                Err(e) => {
                    meals_state.write().error = Some(format!("Failed to load food database: {e}"));
                    meals_state.write().food_loading = false;
                }
            }
        });
    });

    let get_daily_summary = use_callback(move |params: (String, NaiveDate)| {
        let (dog_id, date) = params;
        
        spawn(async move {
            let access_token = get_access_token();
            
            // Query meals for the specific date
            let query = format!("dog_id=eq.{dog_id}&date=eq.{date}");
            
            match client.read().fetch_filtered::<Meal>("meals", &query, access_token.as_deref()).await {
                Ok(meals) => {
                    let total_calories: f32 = meals.iter().map(|m| m.total_calories).sum();
                    let total_meals = meals.len() as i32;
                    
                    // Group by meal type
                    let mut breakfast_calories = 0.0;
                    let mut lunch_calories = 0.0;
                    let mut dinner_calories = 0.0;
                    let mut snack_calories = 0.0;
                    let mut breakfast_count = 0;
                    let mut lunch_count = 0;
                    let mut dinner_count = 0;
                    let mut snack_count = 0;
                    
                    for meal in &meals {
                        match meal.meal_type {
                            MealType::Breakfast => {
                                breakfast_calories += meal.total_calories;
                                breakfast_count += 1;
                            },
                            MealType::Lunch => {
                                lunch_calories += meal.total_calories;
                                lunch_count += 1;
                            },
                            MealType::Dinner => {
                                dinner_calories += meal.total_calories;
                                dinner_count += 1;
                            },
                            MealType::Snack => {
                                snack_calories += meal.total_calories;
                                snack_count += 1;
                            },
                        }
                    }
                    
                    let summary = MealSummary {
                        date,
                        total_calories,
                        total_meals,
                        protein_percentage: 25.0, // Default values, can be calculated from food data
                        fat_percentage: 15.0,
                        carbs_percentage: 60.0,
                        meal_breakdown: vec![
                            crate::models::meals::MealTypeCalories {
                                meal_type: MealType::Breakfast,
                                calories: breakfast_calories,
                                meal_count: breakfast_count,
                            },
                            crate::models::meals::MealTypeCalories {
                                meal_type: MealType::Lunch,
                                calories: lunch_calories,
                                meal_count: lunch_count,
                            },
                            crate::models::meals::MealTypeCalories {
                                meal_type: MealType::Dinner,
                                calories: dinner_calories,
                                meal_count: dinner_count,
                            },
                            crate::models::meals::MealTypeCalories {
                                meal_type: MealType::Snack,
                                calories: snack_calories,
                                meal_count: snack_count,
                            },
                        ],
                    };
                    
                    meals_state.write().daily_summary = Some(summary);
                },
                Err(e) => {
                    meals_state.write().error = Some(format!("Failed to get daily summary: {e}"));
                }
            }
        });
    });

    // Load food database on first render if user is authenticated
    use_effect(move || {
        if auth.state.read().user.is_some() && meals_state.read().food_database.is_empty() && !meals_state.read().food_loading {
            load_food_database(());
        }
    });

    UseMealsHook {
        state: meals_state,
        load_meals,
        load_meals_range,
        create_meal,
        update_meal,
        delete_meal,
        load_food_database,
        get_daily_summary,
    }
}