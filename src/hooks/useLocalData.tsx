
import {createContext, useContext, ReactNode, useState, useEffect} from 'react'
import {usePetData} from './data/usePetData'
import {useActivityData} from './data/useActivityData'
import {useMealData} from './data/useMealData'
import {useWeightLogData} from './data/useWeightLogData'
import {useRecommendationData} from './data/useRecommendationData'
import {Pet, Activity, Meal, WeightLog, DogRecommendation, PetFood} from '@/data/types'

interface LocalDataContextType {
  activities: Activity[];
  addActivity: (activity: Omit<Activity, 'id'>) => Activity;
  addMeal: (meal: Omit<Meal, 'id'>) => Meal;
  addPet: (pet: Omit<Pet, 'id'>) => Pet;
  addPetFood: (petId: string, food: PetFood) => void;
  addTreat: (petId: string) => void;
  addWeightLog: (weightLog: Omit<WeightLog, 'id'>) => WeightLog;
  adjustActivityBasedOnWalk: (petId: string, duration: number, intensity: string) => void;
  deleteActivity: (id: string) => void;
  deleteMeal: (id: string) => void;
  deletePet: (id: string) => void;
  deleteWeightLog: (id: string) => void;
  getActivitiesByPetId: (petId: string) => Activity[];
  getMealsByPetId: (petId: string) => Meal[];
  getPetById: (id: string) => Pet | undefined;
  getWeightLogsByPetId: (petId: string) => WeightLog[];
  isLoading: boolean; // Add loading state
  meals: Meal[];
  pets: Pet[];
  recommendations: DogRecommendation[];
  removePetFood: (petId: string, foodId: string) => void;
  removeTreat: (petId: string) => void;
  setFoodPreferred: (petId: string, foodId: string, isPreferred: boolean) => void;
  selectedPetId: string;
  selectPet: (id: string) => void;
  updateActivity: (activity: Activity) => void;
  updateMeal: (meal: Meal) => void;
  updatePet: (pet: Pet) => void;
  updateWeightLog: (weightLog: WeightLog) => void;
  weightLogs: WeightLog[];
}

const LocalDataContext = createContext<LocalDataContextType | null>(null);

export const LocalDataProvider = ({children}: {children: ReactNode}) => {
  const petData = usePetData();
  const activityData = useActivityData();
  const mealData = useMealData();
  const weightLogData = useWeightLogData();
  const recommendationData = useRecommendationData();
  const [isLoading, setIsLoading] = useState(true); // Add loading state

  // Track loading state of all data sources
  useEffect(() => {
    // console.log('Loading states:', {
    //   petData: petData.isLoaded,
    //   activityData: activityData.isLoaded,
    //   mealData: mealData.isLoaded,
    //   weightLogData: weightLogData.isLoaded,
    //   recommendationData: recommendationData.isLoaded
    // });

    // Check if all data sources have finished loading
    if(
      petData.isLoaded &&
      activityData.isLoaded &&
      mealData.isLoaded &&
      weightLogData.isLoaded &&
      recommendationData.isLoaded
    ) {
      console.log('All data sources loaded.');
      setIsLoading(false);
    }
  }, [
    petData.isLoaded,
    activityData.isLoaded,
    mealData.isLoaded,
    weightLogData.isLoaded,
    recommendationData.isLoaded
  ]);

  // After deleting a pet, also delete all related data
  const deletePet = (id: string): void => {
    // First delete the pet
    petData.deletePet(id);

    // Then delete all related activities
    activityData.activities
      .filter(activity => activity.petId === id)
      .forEach(activity => activityData.deleteActivity(activity.id));

    // Delete related meals
    mealData.meals
      .filter(meal => meal.petId === id)
      .forEach(meal => mealData.deleteMeal(meal.id));

    // Delete related weight logs
    weightLogData.weightLogs
      .filter(log => log.petId === id)
      .forEach(log => weightLogData.deleteWeightLog(log.id));
  };

  const value = {
    activities: activityData.activities,
    addActivity: activityData.addActivity,
    addMeal: mealData.addMeal,
    addPet: petData.addPet,
    addPetFood: petData.addPetFood,
    addTreat: petData.addTreat,
    addWeightLog: weightLogData.addWeightLog,
    adjustActivityBasedOnWalk: petData.adjustActivityBasedOnWalk,
    deleteActivity: activityData.deleteActivity,
    deleteMeal: mealData.deleteMeal,
    deletePet: deletePet,
    deleteWeightLog: weightLogData.deleteWeightLog,
    getActivitiesByPetId: activityData.getActivitiesByPetId,
    getMealsByPetId: mealData.getMealsByPetId,
    getPetById: petData.getPetById,
    getWeightLogsByPetId: weightLogData.getWeightLogsByPetId,
    isLoading, // Add loading state to context
    meals: mealData.meals,
    pets: petData.pets,
    recommendations: recommendationData.recommendations,
    removePetFood: petData.removePetFood,
    removeTreat: petData.removeTreat,
    selectedPetId: petData.selectedPetId,
    selectPet: petData.selectPet,
    setFoodPreferred: petData.setFoodPreferred,
    updateActivity: activityData.updateActivity,
    updateMeal: mealData.updateMeal,
    updatePet: petData.updatePet,
    updateWeightLog: weightLogData.updateWeightLog,
    weightLogs: weightLogData.weightLogs,
  };

  return (
    <LocalDataContext.Provider value={value}>
      {children}
    </LocalDataContext.Provider>
  );
};

export const useLocalData = () => {
  const context = useContext(LocalDataContext);
  if(!context) {
    throw new Error('useLocalData must be used within a LocalDataProvider');
  }
  return context;
};

export default useLocalData;
