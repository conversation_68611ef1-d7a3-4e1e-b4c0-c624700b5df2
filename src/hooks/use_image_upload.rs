use dioxus::prelude::*;
use crate::integrations::supabase::storage::create_storage_client;
use crate::utils::native_file_picker::pick_image_file;

#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ImageUploadState {
    Idle,
    Uploading,
    Success(String), // URL of uploaded image
    Error(String),
}

#[derive(Clone)]
pub struct ImageUploadHook {
    pub state: Signal<ImageUploadState>,
    pub preview_url: Signal<Option<String>>,
}

impl ImageUploadHook {
    pub fn upload_image(&self, file_data: Vec<u8>, file_name: String, dog_id: Option<String>) {
        let mut state = self.state;
        let mut preview_url = self.preview_url;
        
        spawn(async move {
            state.set(ImageUploadState::Uploading);
            
            match create_storage_client() {
                Ok(storage) => {
                    let result = if let Some(dog_id) = dog_id {
                        // Extract file extension
                        let extension = file_name.split('.').next_back().unwrap_or("jpg");
                        storage.upload_dog_image(&dog_id, file_data, extension, None).await
                    } else {
                        let timestamp = chrono::Utc::now().timestamp();
                        let file_path = format!("temp/{timestamp}-{file_name}");
                        storage.upload_file(&file_path, file_data, Some("image/jpeg"), None).await
                    };
                    
                    match result {
                        Ok(url) => {
                            state.set(ImageUploadState::Success(url.clone()));
                            preview_url.set(Some(url));
                        }
                        Err(e) => {
                            state.set(ImageUploadState::Error(format!("Upload failed: {e}")));
                        }
                    }
                }
                Err(e) => {
                    state.set(ImageUploadState::Error(format!("Storage initialization failed: {e}")));
                }
            }
        });
    }

    pub fn reset(&mut self) {
        self.state.set(ImageUploadState::Idle);
        self.preview_url.set(None);
    }

    pub fn set_preview(&mut self, data_url: String) {
        self.preview_url.set(Some(data_url));
    }
}

pub fn use_image_upload() -> ImageUploadHook {
    let state = use_signal(|| ImageUploadState::Idle);
    let preview_url = use_signal(|| None);
    
    ImageUploadHook { state, preview_url }
}

// Helper function to validate image files
pub fn validate_image_file(file_type: &str, file_size: u64) -> Result<(), String> {
    // Check file type
    if !file_type.starts_with("image/") {
        return Err("Please select an image file".to_string());
    }

    // Check file size (10MB limit)
    const MAX_SIZE: u64 = 10 * 1024 * 1024;
    if file_size > MAX_SIZE {
        return Err("Image size must be less than 10MB".to_string());
    }

    // Check specific image formats
    match file_type {
        "image/jpeg" | "image/jpg" | "image/png" | "image/webp" => Ok(()),
        _ => Err("Please select a JPEG, PNG, or WebP image".to_string()),
    }
}

// Helper function for mobile file selection using native file pickers
#[cfg(not(target_family = "wasm"))]
pub async fn select_image_file() -> Result<Option<(Vec<u8>, String)>, String> {
    match pick_image_file().await {
        Ok(Some(selected_file)) => {
            // Validate the selected file
            validate_image_file(&selected_file.mime_type, selected_file.size)?;
            
            Ok(Some((selected_file.data, selected_file.name)))
        },
        Ok(None) => Ok(None), // User cancelled
        Err(e) => {
            match e {
                crate::utils::native_file_picker::FilePickerError::NoFileSelected => Ok(None),
                crate::utils::native_file_picker::FilePickerError::Cancelled => Ok(None),
                crate::utils::native_file_picker::FilePickerError::AccessDenied(msg) => {
                    Err(format!("Permission denied: {msg}. Please grant photo access permission in your device settings."))
                },
                crate::utils::native_file_picker::FilePickerError::FileTooLarge { size, max_size } => {
                    Err(format!("File too large: {size} bytes (max: {max_size} bytes)"))
                },
                crate::utils::native_file_picker::FilePickerError::UnsupportedFileType(file_type) => {
                    Err(format!("Unsupported file type: {file_type}. Please select a JPEG, PNG, or WebP image."))
                },
                crate::utils::native_file_picker::FilePickerError::PlatformNotSupported => {
                    Err("File picker not supported on this platform".to_string())
                },
                crate::utils::native_file_picker::FilePickerError::NativeError(msg) => {
                    Err(format!("File selection failed: {msg}"))
                },
            }
        }
    }
}

// Helper function to read file as bytes using web APIs
#[cfg(target_family = "wasm")]
pub async fn read_file_as_bytes(file: &web_sys::File) -> Result<Vec<u8>, String> {
    use wasm_bindgen::prelude::*;
    use wasm_bindgen_futures::JsFuture;
    use js_sys::ArrayBuffer;

    let array_buffer = JsFuture::from(file.array_buffer())
        .await
        .map_err(|_| "Failed to read file as array buffer")?;
    
    let uint8_array = js_sys::Uint8Array::new(&array_buffer);
    Ok(uint8_array.to_vec())
}

// Helper function to create data URL for preview
#[cfg(target_family = "wasm")]
pub async fn create_data_url(file: &web_sys::File) -> Result<String, String> {
    use wasm_bindgen::prelude::*;
    use wasm_bindgen_futures::JsFuture;

    let file_reader = web_sys::FileReader::new()
        .map_err(|_| "Failed to create FileReader")?;
    
    file_reader.read_as_data_url(file)
        .map_err(|_| "Failed to read file as data URL")?;

    // Wait for the file reader to finish
    let promise = js_sys::Promise::new(&mut |resolve, _reject| {
        let closure = Closure::wrap(Box::new(move |event: web_sys::Event| {
            if let Some(target) = event.target() {
                if let Ok(file_reader) = target.dyn_into::<web_sys::FileReader>() {
                    if let Ok(result) = file_reader.result() {
                        resolve.call1(&JsValue::UNDEFINED, &result).ok();
                    }
                }
            }
        }) as Box<dyn FnMut(web_sys::Event)>);

        file_reader.set_onload(Some(closure.as_ref().unchecked_ref()));
        closure.forget();
    });

    let result = JsFuture::from(promise)
        .await
        .map_err(|_| "Failed to read file")?;
    
    result.as_string()
        .ok_or_else(|| "Failed to convert result to string".to_string())
}