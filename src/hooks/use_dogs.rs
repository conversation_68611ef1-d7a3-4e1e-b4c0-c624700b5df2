use std::sync::Arc;

use chrono::Utc;
use dioxus::{
    prelude::*,
    signals::Signal,
};

use crate::{
    config::Config,
    integrations::supabase::client::SupabaseClient,
    models::dogs::Dog,
};

const DOGS_STORAGE_KEY: &str = "dogs_list";
const SELECTED_DOG_KEY: &str = "selected_dog_id";

#[derive(<PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct DogsState {
    pub dogs: Vec<Dog>,
    pub selected_dog_id: Option<String>,
    pub is_loading: bool,
    pub error: Option<String>,
}

#[derive(Clone)]
pub struct UseDogs {
    pub state: Signal<DogsState>,
    pub load_dogs: Callback<(), ()>,
    pub create_dog: Callback<Dog, ()>,
    pub update_dog: Callback<Dog, ()>,
    pub delete_dog: Callback<String, ()>,
    pub select_dog: Callback<String, ()>,
    pub get_selected_dog: Callback<(), Option<Dog>>,
    pub clear_error: Callback<(), ()>,
}

impl UseDogs {
    /// Get the current active dog
    pub fn current_dog(&self) -> Option<Dog> {
        let state = self.state.read();
        if let Some(selected_id) = &state.selected_dog_id {
            state.dogs.iter()
                .find(|dog| &dog.id == selected_id)
                .cloned()
        } else {
            state.dogs.first().cloned()
        }
    }
    
    /// List all dogs
    pub fn list_dogs(&self) -> Vec<Dog> {
        self.state.read().dogs.clone()
    }
    
    /// Get dog by ID
    pub fn get_dog_by_id(&self, id: &str) -> Option<Dog> {
        self.state.read().dogs.iter()
            .find(|dog| dog.id == id)
            .cloned()
    }
}

pub fn use_dogs() -> UseDogs {
    let mut dogs_state = use_signal(DogsState::default);
    let config = use_signal(Config::from_env);
    let client = use_signal(|| SupabaseClient::new(config.read().clone()));

    // Initialize from local storage on startup
    use_effect(move || {
        // Load from local storage immediately for offline-first behavior
        if let Some(local_dogs) = crate::utils::storage::local_storage::get_item::<Vec<Dog>>(DOGS_STORAGE_KEY) {
            dogs_state.write().dogs = local_dogs;
        }
        
        if let Some(selected_id) = crate::utils::storage::local_storage::get_item::<String>(SELECTED_DOG_KEY) {
            dogs_state.write().selected_dog_id = Some(selected_id);
        }
        
        dogs_state.write().is_loading = false;
    });

    let load_dogs = use_callback(move |(): ()| {
        // Load from local storage first - immediate response
        if let Some(local_dogs) = crate::utils::storage::local_storage::get_item::<Vec<Dog>>(DOGS_STORAGE_KEY) {
            dogs_state.write().dogs = local_dogs;
        }
        
        if let Some(selected_id) = crate::utils::storage::local_storage::get_item::<String>(SELECTED_DOG_KEY) {
            dogs_state.write().selected_dog_id = Some(selected_id);
        }
        
        dogs_state.write().is_loading = false;
        dogs_state.write().error = None;
        
        // Background sync from server (non-blocking)
        let config_clone = config.read().clone();
        let client_for_sync = SupabaseClient::new(config_clone);
        spawn(async move {
            match client_for_sync.select::<Dog>("dogs", None).await {
                Ok(server_dogs) => {
                    eprintln!("Successfully synced dogs from server");
                    dogs_state.write().dogs = server_dogs.clone();
                    crate::utils::storage::local_storage::save_item(DOGS_STORAGE_KEY, &server_dogs);
                    // TODO: Implement proper conflict resolution when dioxus-storage API is stable
                },
                Err(e) => {
                    eprintln!("Failed to sync dogs from server: {e}");
                    // TODO: Handle sync error gracefully when dioxus-storage API is stable
                }
            }
        });
    });

    let create_dog = use_callback(move |dog: Dog| {
        // Save locally first - this always succeeds for offline-first behavior
        dogs_state.write().dogs.push(dog.clone());
        dogs_state.write().is_loading = false;
        dogs_state.write().error = None;
        
        // Save to local storage
        let dogs_list = dogs_state.read().dogs.clone();
        crate::utils::storage::local_storage::save_item(DOGS_STORAGE_KEY, &dogs_list);
        
        // Background sync to server (non-blocking)
        let config_clone = config.read().clone();
        let client_for_sync = SupabaseClient::new(config_clone);
        let dog_for_sync = dog;
        spawn(async move {
            match client_for_sync.insert("dogs", &dog_for_sync, None).await {
                Ok(_) => {
                    eprintln!("Successfully synced new dog to server");
                    // TODO: Update sync status when dioxus-storage API is stable
                },
                Err(e) => {
                    eprintln!("Failed to sync new dog to server: {e}");
                    // TODO: Add to sync queue for retry when dioxus-storage API is stable
                }
            }
        });
    });

    let update_dog = use_callback(move |dog: Dog| {
        // Update locally first - this always succeeds for offline-first behavior
        let mut state = dogs_state.write();
        if let Some(index) = state.dogs.iter().position(|d| d.id == dog.id) {
            state.dogs[index] = dog.clone();
        }
        state.is_loading = false;
        state.error = None;
        
        // Save to local storage
        let dogs_list = state.dogs.clone();
        drop(state); // Release the lock before async operation
        crate::utils::storage::local_storage::save_item(DOGS_STORAGE_KEY, &dogs_list);
        
        // Background sync to server (non-blocking)
        let config_clone = config.read().clone();
        let client_for_sync = SupabaseClient::new(config_clone);
        let dog_for_sync = dog;
        spawn(async move {
            let filter = format!("id=eq.{}", dog_for_sync.id);
            match client_for_sync.update("dogs", &dog_for_sync, &filter, None).await {
                Ok(_) => {
                    eprintln!("Successfully synced dog update to server");
                    // TODO: Update sync status when dioxus-storage API is stable
                },
                Err(e) => {
                    eprintln!("Failed to sync dog update to server: {e}");
                    // TODO: Add to sync queue for retry when dioxus-storage API is stable
                }
            }
        });
    });

    let delete_dog = use_callback(move |dog_id: String| {
        // Delete locally first - this always succeeds for offline-first behavior
        let mut state = dogs_state.write();
        state.dogs.retain(|d| d.id != dog_id);
        
        // Clear selection if the deleted dog was selected
        if state.selected_dog_id.as_ref() == Some(&dog_id) {
            state.selected_dog_id = None;
            crate::utils::storage::local_storage::save_item(SELECTED_DOG_KEY, &Option::<String>::None);
        }
        
        state.is_loading = false;
        state.error = None;
        
        // Save to local storage
        let dogs_list = state.dogs.clone();
        drop(state); // Release the lock before async operation
        crate::utils::storage::local_storage::save_item(DOGS_STORAGE_KEY, &dogs_list);
        
        // Background sync to server (non-blocking)
        let config_clone = config.read().clone();
        let client_for_sync = SupabaseClient::new(config_clone);
        let dog_id_for_sync = dog_id;
        spawn(async move {
            let filter = format!("id=eq.{dog_id_for_sync}");
            match client_for_sync.delete("dogs", &filter, None).await {
                Ok(()) => {
                    eprintln!("Successfully synced dog deletion to server");
                    // TODO: Update sync status when dioxus-storage API is stable
                },
                Err(e) => {
                    eprintln!("Failed to sync dog deletion to server: {e}");
                    // TODO: Add to sync queue for retry when dioxus-storage API is stable
                }
            }
        });
    });

    let select_dog = use_callback(move |dog_id: String| {
        dogs_state.write().selected_dog_id = Some(dog_id.clone());
        
        // Save selected dog to local storage immediately
        crate::utils::storage::local_storage::save_item(SELECTED_DOG_KEY, &dog_id);
    });

    let get_selected_dog = use_callback(move |(): ()| -> Option<Dog> {
        let state = dogs_state.read();
        if let Some(selected_id) = &state.selected_dog_id {
            state.dogs.iter()
                .find(|dog| &dog.id == selected_id)
                .cloned()
        } else {
            None
        }
    });

    let clear_error = use_callback(move |(): ()| {
        dogs_state.write().error = None;
    });

    UseDogs {
        state: dogs_state,
        load_dogs,
        create_dog,
        update_dog,
        delete_dog,
        select_dog,
        get_selected_dog,
        clear_error,
    }
}