use std::collections::HashMap;

use chrono::Utc;
use dioxus::{prelude::*, signals::Signal};

use crate::{
    config::Config,
    integrations::supabase::{
        auth::{
            AuthError,
            SignInRequest,
            SignUpRequest,
            SupabaseAuth,
        },
        client::SupabaseClient,
    },
    models::{
        Theme,
        UserProfile,
    },
};

const AUTH_STORAGE_KEY: &str = "auth_state";
const ACCESS_TOKEN_KEY: &str = "access_token";
const REFRESH_TOKEN_KEY: &str = "refresh_token";

#[derive(C<PERSON>, Default)]
pub struct AuthState {
    pub user: Option<UserProfile>,
    pub access_token: Option<String>,
    pub refresh_token: Option<String>,
    pub is_loading: bool,
    pub error: Option<String>,
    pub is_offline_mode: bool, // Track if we're working in offline mode
}

#[derive(Clone)]
pub struct UseAuth {
    pub state: Signal<AuthState>,
    pub sign_up: Callback<(String, String), ()>,
    pub sign_in: Callback<(String, String), ()>,
    pub sign_out: Callback<(), ()>,
    pub refresh_auth: Callback<(), ()>,
    pub clear_error: Callback<(), ()>,
}

pub fn use_auth() -> UseAuth {
    let mut auth_state = use_signal(AuthState::default);
    let config = use_signal(Config::from_env);
    let client = use_signal(|| SupabaseClient::new(config.read().clone()));
    let auth_client = use_signal(|| SupabaseAuth::new(config.read().clone()));

    // Initialize from local storage on startup
    use_effect(move || {
        spawn(async move {
            // For now, skip local storage initialization
            // TODO: Implement proper offline-first storage when dioxus-storage API is stable
            // This would load stored auth tokens and user profile from local storage
            auth_state.write().is_loading = false;
        });
    });

    let sign_up = use_callback(move |credentials: (String, String)| {
        let (email, password) = credentials;
        auth_state.write().is_loading = true;
        auth_state.write().error = None;
        auth_state.write().is_offline_mode = false;
        
        spawn(async move {
            let mut user_metadata = HashMap::new();
            user_metadata.insert("created_via".to_string(), serde_json::Value::String("app".to_string()));
            
            let request = SignUpRequest {
                email: &email,
                password: &password,
                data: Some(user_metadata),
            };
            
            match auth_client.read().sign_up(&request).await {
                Ok(auth_response) => {
                    if let (Some(session), Some(user)) = (auth_response.session, auth_response.user) {
                        let user_profile = UserProfile {
                            id: user.id,
                            email: Some(user.email),
                            provider: Some("email".to_string()),
                            notifications_enabled: true,
                            unit_system: "metric".to_string(),
                            preferred_language: "en".to_string(),
                            theme: Theme::Light,
                            onboarding_completed: false,
                            created_at: Some(Utc::now()),
                            updated_at: Some(Utc::now()),
                        };
                        
                        auth_state.write().user = Some(user_profile);
                        auth_state.write().access_token = Some(session.access_token.clone());
                        auth_state.write().refresh_token = Some(session.refresh_token);
                        
                        // TODO: Store auth state to local storage when API is stable
                    }
                    auth_state.write().is_loading = false;
                },
                Err(e) => {
                    // Handle offline mode gracefully
                    let error_msg = format!("Sign up failed: {e}");
                    if error_msg.contains("network") || error_msg.contains("timeout") {
                        auth_state.write().is_offline_mode = true;
                        auth_state.write().error = Some("Unable to connect. Please check your internet connection and try again.".to_string());
                    } else {
                        auth_state.write().error = Some(error_msg);
                    }
                    auth_state.write().is_loading = false;
                }
            }
        });
    });

    let sign_in = use_callback(move |credentials: (String, String)| {
        let (email, password) = credentials;
        auth_state.write().is_loading = true;
        auth_state.write().error = None;
        auth_state.write().is_offline_mode = false;
        
        spawn(async move {
            let request = SignInRequest {
                email: &email,
                password: &password,
            };
            
            match auth_client.read().sign_in(&request).await {
                Ok(auth_response) => {
                    if let (Some(session), Some(user)) = (auth_response.session, auth_response.user) {
                        let user_profile = UserProfile {
                            id: user.id,
                            email: Some(user.email),
                            provider: Some("email".to_string()),
                            notifications_enabled: true,
                            unit_system: "metric".to_string(),
                            preferred_language: "en".to_string(),
                            theme: Theme::Light,
                            onboarding_completed: false,
                            created_at: Some(Utc::now()),
                            updated_at: Some(Utc::now()),
                        };
                        
                        auth_state.write().user = Some(user_profile);
                        auth_state.write().access_token = Some(session.access_token.clone());
                        auth_state.write().refresh_token = Some(session.refresh_token);
                        
                        // TODO: Store auth state to local storage when API is stable
                    }
                    auth_state.write().is_loading = false;
                },
                Err(e) => {
                    // Handle offline mode gracefully
                    let error_msg = format!("Sign in failed: {e}");
                    if error_msg.contains("network") || error_msg.contains("timeout") {
                        auth_state.write().is_offline_mode = true;
                        auth_state.write().error = Some("Unable to connect. Please check your internet connection and try again.".to_string());
                    } else {
                        auth_state.write().error = Some(error_msg);
                    }
                    auth_state.write().is_loading = false;
                }
            }
        });
    });

    let sign_out = use_callback(move |(): ()| {
        spawn(async move {
            let access_token = auth_state.read().access_token.clone();
            
            // Try to sign out from server, but don't fail if offline
            if let Some(token) = access_token {
                let _ = auth_client.read().sign_out(&token).await;
                // Ignore errors - sign out locally regardless of server response
            }
            
            // Clear local state
            auth_state.write().user = None;
            auth_state.write().access_token = None;
            auth_state.write().refresh_token = None;
            auth_state.write().error = None;
            auth_state.write().is_offline_mode = false;
            
            // TODO: Clear stored auth state when local storage API is stable
        });
    });

    let refresh_auth = use_callback(move |(): ()| {
        spawn(async move {
            // Extract refresh_token before any writes to avoid borrow conflicts
            let refresh_token = auth_state.read().refresh_token.clone();
            
            if let Some(refresh_token) = refresh_token {
                match auth_client.read().refresh_token(&refresh_token).await {
                    Ok(auth_response) => {
                        if let (Some(session), Some(user)) = (auth_response.session, auth_response.user) {
                            let user_profile = UserProfile {
                                id: user.id,
                                email: Some(user.email),
                                provider: Some("email".to_string()),
                                notifications_enabled: true,
                                unit_system: "metric".to_string(),
                                preferred_language: "en".to_string(),
                                theme: Theme::Light,
                                onboarding_completed: false,
                                created_at: Some(Utc::now()),
                                updated_at: Some(Utc::now()),
                            };
                            
                            auth_state.write().user = Some(user_profile);
                            auth_state.write().access_token = Some(session.access_token.clone());
                            auth_state.write().refresh_token = Some(session.refresh_token);
                            auth_state.write().is_offline_mode = false;
                            
                            // TODO: Update stored auth state when local storage API is stable
                        }
                    },
                    Err(e) => {
                        // Handle network errors gracefully in offline mode
                        let error_msg = format!("Token refresh failed: {e}");
                        if error_msg.contains("network") || error_msg.contains("timeout") {
                            // Don't clear tokens on network errors - keep user logged in locally
                            auth_state.write().is_offline_mode = true;
                        } else {
                            // Clear invalid tokens on authentication errors
                            auth_state.write().error = Some(error_msg);
                            auth_state.write().user = None;
                            auth_state.write().access_token = None;
                            auth_state.write().refresh_token = None;
                            
                            // TODO: Clear stored auth state when local storage API is stable
                        }
                    }
                }
            }
        });
    });

    let clear_error = use_callback(move |(): ()| {
        auth_state.write().error = None;
    });

    // TODO: Auto-refresh token periodically if user is authenticated
    // This would require adding gloo_timers as a dependency
    // use_effect(move || {
    //     spawn(async move {
    //         loop {
    //             // Wait 50 minutes before checking if we need to refresh
    //             // gloo_timers::future::sleep(std::time::Duration::from_secs(3000)).await;
    //
    //             if auth_state.read().user.is_some() && auth_state.read().refresh_token.is_some() {
    //                 refresh_auth(());
    //             }
    //         }
    //     });
    // });

    UseAuth {
        state: auth_state,
        sign_up,
        sign_in,
        sign_out,
        refresh_auth,
        clear_error,
    }
}