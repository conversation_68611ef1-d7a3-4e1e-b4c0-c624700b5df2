use chrono::{
    NaiveDate,
    Utc,
};
use dioxus_router::hooks::use_navigator;
use uuid::Uuid;

use crate::{
    integrations::supabase::client::SupabaseClient,
    models::{
        activity::ActivityType,
        dogs::{
            Dog,
            DogSize,
            Gender,
        },
        user::UserProfile,
    },
    prelude::*,
    state::{
        AppAction,
        use_state,
    },
    ui::routes::Route,
    utils::calorie_calculator::{
        CalorieCalculationResult,
        CalorieCalculator,
    },
};

pub fn use_onboarding() -> OnboardingHook {
    let state = use_signal(OnboardingState::default);
    let app_state = use_state();
    let dogs_hook = crate::hooks::use_dogs();
    let navigator = use_navigator();

    // Create persistent signals for form fields
    let dog_name_signal = use_signal(|| state.read().dog_name.clone());
    let dog_gender_signal = use_signal(|| {
        state
            .read()
            .dog_gender
            .as_ref()
            .map(|g| match g {
                Gender::Male => "male".to_string(),
                Gender::Female => "female".to_string(),
            })
            .unwrap_or_default()
    });
    let dog_size_signal = use_signal(|| {
        state
            .read()
            .dog_size
            .as_ref()
            .map(|s| match s {
                DogSize::Small => "small".to_string(),
                DogSize::Medium => "medium".to_string(),
                DogSize::Large => "large".to_string(),
            })
            .unwrap_or_default()
    });
    let dog_weight_signal = use_signal(|| state.read().dog_weight);
    let dog_birthday_signal = use_signal(|| {
        state
            .read()
            .dog_birthday
            .map(|d| d.format("%Y-%m-%d").to_string())
            .unwrap_or_default()
    });
    let dog_target_weight_signal = use_signal(|| state.read().dog_target_weight);
    let dog_typical_activity_signal = use_signal(|| {
        state
            .read()
            .typical_activity
            .as_ref()
            .map(|a| match a {
                ActivityType::Walking => "walking".to_string(),
                ActivityType::Running => "running".to_string(),
                ActivityType::Mixed => "mixed".to_string(),
            })
            .unwrap_or_default()
    });
    let dog_bcs_signal = use_signal(|| state.read().body_condition_score);
    let dog_image_url_signal = use_signal(|| state.read().dog_image_url.clone());

    let save_profile = use_callback(move |()| {
        let current_state = state.read().clone();
        let dogs_hook_clone = dogs_hook.clone();
        
        spawn(async move {
            let result = save_onboarding_data_with_dogs_hook(current_state, dogs_hook_clone).await;
            match result {
                Ok((dog, calorie_result)) => {
                    // Navigate to home with success
                    navigator.push("/");
                    // TODO: Show success message with calorie recommendations
                },
                Err(_e) => {
                    // TODO: Show error message
                    // For now, just navigate anyway during development
                    navigator.push("/");
                },
            }
        });
    });

    OnboardingHook {
        state,
        save_profile,
        dog_name_signal,
        dog_gender_signal,
        dog_size_signal,
        dog_weight_signal,
        dog_birthday_signal,
        dog_target_weight_signal,
        dog_typical_activity_signal,
        dog_bcs_signal,
        dog_image_url_signal,
    }
}

#[derive(Clone)]
pub struct OnboardingHook {
    pub state:                       Signal<OnboardingState>,
    pub save_profile:                Callback<()>,
    // Persistent signals for form fields
    pub dog_name_signal:             Signal<String>,
    pub dog_gender_signal:           Signal<String>,
    pub dog_size_signal:             Signal<String>,
    pub dog_weight_signal:           Signal<Option<f32>>,
    pub dog_birthday_signal:         Signal<String>,
    pub dog_target_weight_signal:    Signal<Option<f32>>,
    pub dog_typical_activity_signal: Signal<String>,
    pub dog_bcs_signal:              Signal<Option<i32>>,
    pub dog_image_url_signal:        Signal<Option<String>>,
}

impl OnboardingHook {
    /// Get current step number
    pub fn current_step(&self) -> usize { self.state.read().current_step }

    /// Get dog name signal
    pub const fn dog_name(&self) -> Signal<String> { self.dog_name_signal }

    /// Get dog gender signal
    pub const fn dog_gender(&self) -> Signal<String> { self.dog_gender_signal }

    /// Get dog size signal
    pub const fn dog_size(&self) -> Signal<String> { self.dog_size_signal }

    /// Get dog weight signal
    pub const fn dog_weight(&self) -> Signal<Option<f32>> { self.dog_weight_signal }

    /// Get dog birthday signal
    pub const fn dog_birthday(&self) -> Signal<String> { self.dog_birthday_signal }

    /// Get dog target weight signal
    pub const fn dog_target_weight(&self) -> Signal<Option<f32>> { self.dog_target_weight_signal }

    /// Get dog typical activity signal
    pub const fn dog_typical_activity(&self) -> Signal<String> { self.dog_typical_activity_signal }

    /// Get dog BCS signal
    pub const fn dog_bcs(&self) -> Signal<Option<i32>> { self.dog_bcs_signal }

    /// Get dog image URL signal
    pub const fn dog_image_url(&self) -> Signal<Option<String>> { self.dog_image_url_signal }

    /// Update onboarding state from signals
    fn sync_signals_to_state(&mut self) {
        self.state.with_mut(|state| {
            // Update dog name
            state.dog_name = self.dog_name_signal.read().clone();

            // Update dog gender
            let gender_str = self.dog_gender_signal.read().clone();
            state.dog_gender = match gender_str.as_str() {
                "male" => Some(Gender::Male),
                "female" => Some(Gender::Female),
                _ => None,
            };

            // Update dog size
            let size_str = self.dog_size_signal.read().clone();
            state.dog_size = match size_str.as_str() {
                "small" => Some(DogSize::Small),
                "medium" => Some(DogSize::Medium),
                "large" => Some(DogSize::Large),
                _ => None,
            };

            // Update dog weight
            state.dog_weight = *self.dog_weight_signal.read();

            // Update dog birthday
            let birthday_str = self.dog_birthday_signal.read().clone();
            state.dog_birthday = if birthday_str.is_empty() {
                None
            } else {
                chrono::NaiveDate::parse_from_str(&birthday_str, "%Y-%m-%d").ok()
            };

            // Update dog target weight
            state.dog_target_weight = *self.dog_target_weight_signal.read();

            // Update typical activity
            let activity_str = self.dog_typical_activity_signal.read().clone();
            state.typical_activity = match activity_str.as_str() {
                "walking" => Some(ActivityType::Walking),
                "running" => Some(ActivityType::Running),
                "mixed" => Some(ActivityType::Mixed),
                _ => None,
            };

            // Update BCS
            state.body_condition_score = *self.dog_bcs_signal.read();

            // Update image URL
            state.dog_image_url = self.dog_image_url_signal.read().clone();
        });
    }

    /// Get validation error message
    pub fn validation_error(&self) -> Option<String> { self.state.read().error_message.clone() }

    /// Get field-specific validation errors for current step (only shown after validation attempt)
    pub fn get_field_errors(&self, step: usize) -> std::collections::HashMap<String, bool> {
        let state = self.state.read();
        let mut errors = std::collections::HashMap::new();

        // Only show validation errors if validation has been attempted for this step
        if !state.validation_attempted.contains(&step) {
            return errors; // Return empty errors if validation not attempted
        }

        match step {
            1 => {
                errors.insert("pet_name".to_string(), state.dog_name.trim().is_empty());
                errors.insert("pet_gender".to_string(), state.dog_gender.is_none());
                errors.insert("pet_size".to_string(), state.dog_size.is_none());
            },
            2 => {
                errors.insert("dog_weight".to_string(), state.dog_weight.is_none());
                errors.insert("dog_birthday".to_string(), state.dog_birthday.is_none());
            },
            3 => {
                errors.insert("typical_activity".to_string(), state.typical_activity.is_none());
                errors.insert("body_condition_score".to_string(), state.body_condition_score.is_none());
            },
            _ => {},
        }

        errors
    }

    /// Get loading state
    pub fn is_loading(&self) -> bool { self.state.read().is_loading }

    /// Get error message
    pub fn error(&self) -> Option<String> { self.state.read().error_message.clone() }

    /// Get calorie recommendations
    pub fn calorie_recommendations(&self) -> Option<CalorieCalculationResult> {
        self.state.read().calorie_recommendation.clone()
    }

    /// Move to next step if current step is valid
    pub fn next_step(&mut self) {
        // First, sync signals to state to ensure validation is based on current form values
        self.sync_signals_to_state();

        let current_step = self.state.read().current_step;

        // Mark validation as attempted for this step
        self.state.with_mut(|state| {
            state.validation_attempted.insert(current_step);
        });

        if self.state.read().is_valid_for_step(current_step) {
            self.state.with_mut(|state| {
                state.current_step = (current_step + 1).min(3);
                state.error_message = None;
                // Clear validation attempt flag for the next step (fresh start)
                state.validation_attempted.remove(&(current_step + 1));
            });
        } else {
            self.state.with_mut(|state| {
                state.error_message = Some("Please fill in all required fields".to_string());
            });
        }
    }

    /// Move to previous step
    pub fn previous_step(&mut self) {
        self.state.with_mut(|state| {
            let new_step = state.current_step.saturating_sub(1).max(1);
            state.current_step = new_step;
            state.error_message = None;
            // Clear validation attempt flag for the previous step (fresh start)
            state.validation_attempted.remove(&new_step);
        });
    }

    /// Finish onboarding process
    pub async fn finish_onboarding(&mut self, dogs_hook: crate::hooks::UseDogs) -> Result<(), String> {
        // First, sync signals to state to ensure we have the latest form data
        self.sync_signals_to_state();

        let current_step = self.state.read().current_step;

        // Mark validation as attempted for the current step
        self.state.with_mut(|state| {
            state.validation_attempted.insert(current_step);
        });

        // Check if the current step is valid before proceeding
        if !self.state.read().is_valid_for_step(current_step) {
            self.state.with_mut(|state| {
                state.error_message = Some("Please fill in all required fields".to_string());
            });
            return Err("Please fill in all required fields".to_string());
        }

        self.state.with_mut(|state| state.is_loading = true);

        debug!("Saving onboarding data...");
        let result = save_onboarding_data_with_dogs_hook(self.state.read().clone(), dogs_hook).await;
        debug!("Onboarding data save result: {:?}", result);

        self.state.with_mut(|state| {
            state.is_loading = false;
            match &result {
                Ok((_, calorie_result)) => {
                    state.calorie_recommendation = Some(calorie_result.clone());
                    state.current_step = 4; // Move to recommendations step
                },
                Err(e) => {
                    state.error_message = Some(e.clone());
                },
            }
        });

        result.map(|_| ())
    }
}

#[derive(Clone)]
pub struct OnboardingState {
    // Dog basic information
    pub dog_name:             String,
    pub dog_gender:           Option<Gender>,
    pub dog_breed:            Option<String>,
    pub dog_size:             Option<DogSize>,
    pub dog_weight:           Option<f32>,
    pub dog_target_weight:    Option<f32>,
    pub dog_birthday:         Option<NaiveDate>,
    pub body_condition_score: Option<i32>,
    pub typical_activity:     Option<ActivityType>,
    pub dog_image_url:        Option<String>,

    // Form state
    pub current_step:           usize,
    pub is_loading:             bool,
    pub error_message:          Option<String>,
    pub calorie_recommendation: Option<CalorieCalculationResult>,
    /// Track validation attempts per step to only show errors after submission attempt
    pub validation_attempted:   std::collections::HashSet<usize>,
}

impl Default for OnboardingState {
    fn default() -> Self {
        Self {
            dog_name:               String::new(),
            dog_gender:             None,
            dog_breed:              None,
            dog_size:               None,
            dog_weight:             None,
            dog_target_weight:      None,
            dog_birthday:           None,
            body_condition_score:   None,
            typical_activity:       None,
            dog_image_url:          None,
            current_step:           1, // Start at step 1, not 0
            is_loading:             false,
            error_message:          None,
            calorie_recommendation: None,
            validation_attempted:   std::collections::HashSet::new(),
        }
    }
}

impl OnboardingState {
    pub fn to_dog(&self) -> Dog {
        Dog {
            id:                   Uuid::new_v4().to_string(),
            user_id:              String::new(), // Will be set after user creation
            name:                 self.dog_name.clone(),
            breed_id:             None, // Could be set based on breed selection later
            gender:               self.dog_gender.clone().unwrap_or(Gender::Male),
            size:                 self.dog_size.unwrap_or(DogSize::Medium),
            current_weight:       self.dog_weight,
            target_weight:        self.dog_target_weight,
            birthday:             self.dog_birthday,
            body_condition_score: self.body_condition_score,
            typical_activity:     self.typical_activity.as_ref().map(|a| match a {
                ActivityType::Walking => "walking".to_string(),
                ActivityType::Running => "running".to_string(),
                ActivityType::Mixed => "mixed".to_string(),
            }),
            photo_url:            self.dog_image_url.clone(),
            created_at:           Some(Utc::now()),
            updated_at:           Some(Utc::now()),
            // Additional fields for compatibility
            activity_level:       Some(crate::models::dogs::ActivityLevel::Moderate),
            is_neutered:          Some(true),
            // Non-database fields with defaults
            activities:           vec![],
            preferred_foods:      vec![],
            food_history:         vec![],
            daily_calorie_goal:   None,
            daily_activity_goal:  None,
            calorie_progress:     None,
        }
    }

    pub fn to_user(&self) -> UserProfile {
        UserProfile {
            id:                    Uuid::new_v4().to_string(),
            email:                 None, // Anonymous user for now
            provider:              Some("anonymous".to_string()),
            notifications_enabled: true,
            unit_system:           "metric".to_string(),
            preferred_language:    "en".to_string(),
            theme:                 crate::models::Theme::Light,
            onboarding_completed:  true,
            created_at:            Some(Utc::now()),
            updated_at:            Some(Utc::now()),
        }
    }

    pub const fn is_valid_for_step(&self, step: usize) -> bool {
        match step {
            1 => !self.dog_name.is_empty() && self.dog_gender.is_some() && self.dog_size.is_some(),
            2 => self.dog_weight.is_some() && self.dog_birthday.is_some(),
            3 => self.body_condition_score.is_some() && self.typical_activity.is_some(),
            _ => false,
        }
    }

    pub fn calculate_calories(&self) -> Option<CalorieCalculationResult> {
        let dog = self.to_dog();
        if dog.current_weight.is_some() && dog.birthday.is_some() {
            Some(CalorieCalculator::calculate_daily_calories(&dog))
        } else {
            None
        }
    }
}

async fn save_onboarding_data_with_dogs_hook(state: OnboardingState, dogs_hook: crate::hooks::UseDogs) -> Result<(Dog, CalorieCalculationResult), String> {
    // Create dog profile - this ALWAYS succeeds for offline-first behavior
    let dog = state.to_dog();

    // Calculate calorie recommendations immediately
    let calorie_result = CalorieCalculator::calculate_daily_calories(&dog);

    // Use the dogs hook to create the dog - this handles all storage and state management
    (dogs_hook.create_dog)(dog.clone());
    
    // Select the newly created dog automatically
    (dogs_hook.select_dog)(dog.id.clone());

    // Save calorie recommendations separately for the recommendations step
    crate::utils::storage::local_storage::save_item("calorie_recommendations", &calorie_result);

    // Always return success since the dogs hook handles everything
    Ok((dog, calorie_result))
}

// Keep the old function for backward compatibility, but mark it as deprecated
#[deprecated(note = "Use save_onboarding_data_with_dogs_hook instead")]
async fn save_onboarding_data(state: OnboardingState) -> Result<(Dog, CalorieCalculationResult), String> {
    // Create dog profile - this ALWAYS succeeds for offline-first behavior
    let dog = state.to_dog();

    // Calculate calorie recommendations immediately
    let calorie_result = CalorieCalculator::calculate_daily_calories(&dog);

    // Save locally first - this ensures the user can continue using the app
    crate::utils::storage::local_storage::save_item("current_dog", &dog);
    crate::utils::storage::local_storage::save_item("calorie_recommendations", &calorie_result);

    // Try to sync to server in the background (non-blocking)
    let config = crate::config::Config::default_dev();
    let client = SupabaseClient::new(config);
    let dog_for_sync = dog.clone();
    
    spawn(async move {
        // Background sync - failures don't affect user experience
        match client.insert::<Dog>("dogs", &dog_for_sync, None).await {
            Ok(_) => {
                eprintln!("Successfully synced dog profile to server");
                // TODO: Update local sync status when dioxus-storage API is stable
            },
            Err(e) => {
                eprintln!("Failed to sync dog profile to server: {e}");
                // TODO: Add to sync queue for retry when dioxus-storage API is stable
            }
        }
    });

    // Always return success since local save succeeded
    Ok((dog, calorie_result))
}
