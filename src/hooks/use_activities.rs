use dioxus::{
    prelude::*,
    signals::Signal,
};

use crate::{
    config::Config,
    hooks::use_auth,
    integrations::supabase::client::{
        SupabaseClient,
        SupabaseError,
    },
    models::activity::{Activity, ActivityType},
};

#[derive(Clone, Default)]
pub struct ActivitiesState {
    pub activities: Vec<Activity>,
    pub loading: bool,
    pub error: Option<String>,
    pub current_activity: Option<Activity>,
    pub activity_in_progress: bool,
    // Offline-first state
    pub is_offline_mode: bool,
    pub pending_sync_count: usize,
    pub last_sync: Option<chrono::DateTime<chrono::Utc>>,
}


#[derive(Clone)]
pub struct UseActivitiesHook {
    pub state: Signal<ActivitiesState>,
    pub load_activities: Callback<String, ()>, // dog_id
    pub start_activity: Callback<(String, ActivityType), ()>, // dog_id, activity_type
    pub stop_activity: Callback<(), ()>,
    pub save_activity: Callback<Activity, ()>,
    pub delete_activity: Callback<String, ()>, // activity_id
    pub clear_error: Callback<(), ()>,
    // TODO: Add when implementing background sync
    // pub force_sync: Callback<(), ()>,
    // pub retry_failed_operations: Callback<(), ()>,
}

pub fn use_activities() -> UseActivitiesHook {
    let mut activities_state = use_signal(ActivitiesState::default);
    let auth = use_auth();
    let config = use_signal(Config::from_env);
    let client = use_signal(|| SupabaseClient::new(config.read().clone()));

    // Helper function to get access token
    let get_access_token = move || -> Option<String> {
        auth.state.read().access_token.clone()
    };

    let clear_error = use_callback(move |(): ()| {
        activities_state.write().error = None;
    });

    let load_activities = use_callback(move |dog_id: String| {
        activities_state.write().loading = true;
        activities_state.write().error = None;
        
        // TODO: Load from local storage first
        // TODO: Load activities for specific dog_id from local storage
        
        // For now, start with empty activities locally
        // In a real offline-first implementation, this would load from local storage
        activities_state.write().activities = Vec::new();
        activities_state.write().loading = false;
        
        // TODO: Background sync - attempt to fetch from server and merge with local data
        spawn(async move {
            let access_token = get_access_token();
            
            // Use query parameters to filter by dog_id
            let query = format!("dog_id=eq.{dog_id}");
            
            match client.read().fetch_filtered::<Activity>("activities", &query, access_token.as_deref()).await {
                Ok(server_activities) => {
                    // TODO: Merge server activities with local activities, resolving conflicts
                    // For now, just update the state
                    activities_state.write().activities = server_activities;
                    activities_state.write().last_sync = Some(chrono::Utc::now());
                    activities_state.write().is_offline_mode = false;
                },
                Err(e) => {
                    // Handle network errors gracefully - continue working offline
                    activities_state.write().is_offline_mode = true;
                    activities_state.write().error = Some(format!("Working offline: {e}"));
                    
                    // TODO: Add proper logging when tracing is available
                    eprintln!("Failed to sync activities, working offline: {e}");
                    // TODO: Schedule retry for later when network becomes available
                }
            }
        });
    });

    let start_activity = use_callback(move |params: (String, ActivityType)| {
        let (dog_id, activity_type) = params;
        
        activities_state.write().error = None;
        
        // Create a new activity with current timestamp - works completely offline
        let activity = Activity {
            id: uuid::Uuid::new_v4().to_string(),
            dog_id,
            activity_type,
            start_time: chrono::Utc::now(),
            end_time: None,
            duration_minutes: Some(0),
            calories_burned: Some(0),
            distance_meters: None,
            notes: None,
            created_at: Some(chrono::Utc::now()),
            updated_at: None,
        };
        
        // Immediate local operation
        activities_state.write().current_activity = Some(activity);
        activities_state.write().activity_in_progress = true;
        
        // TODO: Store current activity state in local storage for persistence across app restarts
    });

    let stop_activity = use_callback(move |(): ()| {
        let current_activity_clone = activities_state.read().current_activity.clone();
        if let Some(mut activity) = current_activity_clone {
            let now = chrono::Utc::now();
            activity.end_time = Some(now);
            activity.updated_at = Some(now);
            
            // Calculate duration in minutes
            let duration = now.signed_duration_since(activity.start_time);
            let duration_minutes = duration.num_minutes().max(0) as i32;
            activity.duration_minutes = Some(duration_minutes);
            
            // Estimate calories burned (rough calculation - can be improved)
            let calories_burned = match activity.activity_type {
                ActivityType::Walking => duration_minutes as f32 * 3.0,
                ActivityType::Running => duration_minutes as f32 * 8.0,
                ActivityType::Mixed => duration_minutes as f32 * 4.0,
            };
            activity.calories_burned = Some(calories_burned as i32);
            
            // Immediate local operation - save completed activity
            let mut state_write = activities_state.write();
            state_write.activities.insert(0, activity.clone());
            state_write.current_activity = None;
            state_write.activity_in_progress = false;
            state_write.pending_sync_count += 1;
            drop(state_write); // Release the write lock
            
            // TODO: Store completed activity in local storage
            // TODO: Mark activity as pending sync to server
            
            // Background sync to server
            spawn(async move {
                let access_token = get_access_token();
                
                match client.read().insert::<Activity>("activities", &activity, access_token.as_deref()).await {
                    Ok(_saved_activity) => {
                        // TODO: Mark activity as synced in local storage
                        let current_count = activities_state.read().pending_sync_count;
                        activities_state.write().pending_sync_count = current_count.saturating_sub(1);
                        activities_state.write().last_sync = Some(chrono::Utc::now());
                        activities_state.write().is_offline_mode = false;
                    },
                    Err(e) => {
                        // Don't show error to user - activity is saved locally
                        activities_state.write().is_offline_mode = true;
                        // TODO: Add proper logging when tracing is available
                        eprintln!("Failed to sync activity to server: {e}");
                        // TODO: Schedule retry for later when network becomes available
                    }
                }
            });
        }
    });

    let save_activity = use_callback(move |mut activity: Activity| {
        activities_state.write().loading = true;
        activities_state.write().error = None;
        
        // Immediate local operation - optimistic UI update
        activity.updated_at = Some(chrono::Utc::now());
        
        // Check if this is an update or new activity
        let is_update = activities_state.read().activities.iter().any(|a| a.id == activity.id);
        
        if is_update {
            // Update existing activity locally
            let mut state_write = activities_state.write();
            if let Some(existing) = state_write.activities.iter_mut().find(|a| a.id == activity.id) {
                *existing = activity.clone();
            }
            state_write.loading = false;
            state_write.pending_sync_count += 1;
        } else {
            // Add new activity locally
            let mut state_write = activities_state.write();
            state_write.activities.insert(0, activity.clone());
            state_write.loading = false;
            state_write.pending_sync_count += 1;
        }
        
        // TODO: Store activity in local storage
        // TODO: Mark activity as pending sync
        
        // Background sync to server
        spawn(async move {
            let access_token = get_access_token();
            
            let result = if is_update {
                client.read().update::<Activity>("activities", &activity, &activity.id, access_token.as_deref()).await
            } else {
                client.read().insert::<Activity>("activities", &activity, access_token.as_deref()).await
            };
            
            match result {
                Ok(_) => {
                    // TODO: Mark activity as synced in local storage
                    let current_count = activities_state.read().pending_sync_count;
                    activities_state.write().pending_sync_count = current_count.saturating_sub(1);
                    activities_state.write().last_sync = Some(chrono::Utc::now());
                    activities_state.write().is_offline_mode = false;
                },
                Err(e) => {
                    // Don't show error to user - activity is saved locally
                    activities_state.write().is_offline_mode = true;
                    // TODO: Add proper logging when tracing is available
                    eprintln!("Failed to sync activity to server: {e}");
                    // TODO: Schedule retry for later when network becomes available
                }
            }
        });
    });

    let delete_activity = use_callback(move |activity_id: String| {
        activities_state.write().loading = true;
        activities_state.write().error = None;
        
        // Immediate local operation - optimistic UI update
        let mut state_write = activities_state.write();
        state_write.activities.retain(|a| a.id != activity_id);
        state_write.loading = false;
        state_write.pending_sync_count += 1;
        drop(state_write);
        
        // TODO: Mark activity as deleted in local storage (soft delete)
        // TODO: Add to pending sync queue for server deletion
        
        // Background sync to server
        spawn(async move {
            let access_token = get_access_token();
            
            match client.read().delete("activities", &activity_id, access_token.as_deref()).await {
                Ok(()) => {
                    // TODO: Remove activity from local storage permanently
                    let current_count = activities_state.read().pending_sync_count;
                    activities_state.write().pending_sync_count = current_count.saturating_sub(1);
                    activities_state.write().last_sync = Some(chrono::Utc::now());
                    activities_state.write().is_offline_mode = false;
                },
                Err(e) => {
                    // Deletion failed on server - activity is still deleted locally
                    activities_state.write().is_offline_mode = true;
                    // TODO: Add proper logging when tracing is available
                    eprintln!("Failed to delete activity from server: {e}");
                    // TODO: Schedule retry for later when network becomes available
                }
            }
        });
    });

    UseActivitiesHook {
        state: activities_state,
        load_activities,
        start_activity,
        stop_activity,
        save_activity,
        delete_activity,
        clear_error,
    }
}