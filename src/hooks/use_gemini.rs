//! Gemini AI Hook for the My Dog in Fit Application
//!
//! This hook provides a reactive interface for interacting with the Gemini AI
//! assistant, integrating with the offline-first architecture and providing
//! personalized dog health recommendations.

use std::{
    collections::VecDeque,
    sync::Arc,
};

use chrono::{
    DateTime,
    Utc,
};
use dioxus::prelude::*;

use crate::{
    config::Config,
    integrations::gemini::{
        AiMessage,
        DogContextService,
        FallbackResponse,
        GeminiClient,
        GeminiError,
        GeminiService,
        MessageStatus,
    },
    models::dogs::Dog,
    state::{
        NetworkStatus,
        OfflineQueue,
    },
};

/// Chat message for the AI assistant interface
#[derive(Clone, Debug, PartialEq, Eq)]
pub struct ChatMessage {
    pub id:              String,
    pub content:         String, // Full prompt sent to API
    pub display_message: String, // Clean message shown to users
    pub sender:          MessageSender,
    pub timestamp:       DateTime<Utc>,
    pub status:          MessageStatus,
    pub dog_context:     Option<String>, // Dog name for context
}

#[derive(<PERSON><PERSON>, Debug, PartialEq, Eq)]
pub enum MessageSender {
    User,
    Assistant,
}

/// Request type for specialized AI recommendations
#[derive(C<PERSON>, Debug, PartialEq, Eq)]
pub enum RecommendationType {
    Nutrition,
    Exercise,
    Health,
    General,
    DailyTip,
    WeightManagement,
    AgeAppropriate,
    Emergency,
}

/// Offline tips pool for when AI is not available
pub struct OfflineTips;

impl OfflineTips {
    /// Get a random tip related to the user's request or a general tip
    pub fn get_random_tip(request_content: &str, dog_name: Option<&str>) -> String {
        let dog_name = dog_name.unwrap_or("your dog");

        // Analyze request content to determine the best tip category
        let tips = if request_content.to_lowercase().contains("nutrition")
            || request_content.to_lowercase().contains("food")
        {
            Self::nutrition_tips(dog_name)
        } else if request_content.to_lowercase().contains("exercise")
            || request_content.to_lowercase().contains("activity")
        {
            Self::exercise_tips(dog_name)
        } else if request_content.to_lowercase().contains("weight") {
            Self::weight_tips(dog_name)
        } else if request_content.to_lowercase().contains("health") {
            Self::health_tips(dog_name)
        } else if request_content.to_lowercase().contains("emergency") {
            Self::emergency_tips(dog_name)
        } else {
            Self::general_tips(dog_name)
        };

        // Select random tip from the appropriate category
        use std::{
            collections::hash_map::DefaultHasher,
            hash::{
                Hash,
                Hasher,
            },
        };

        let mut hasher = DefaultHasher::new();
        request_content.hash(&mut hasher);
        chrono::Utc::now().timestamp().hash(&mut hasher);
        let hash = hasher.finish();

        let index = (hash as usize) % tips.len();
        tips[index].to_string()
    }

    fn nutrition_tips(dog_name: &str) -> Vec<&'static str> {
        vec![
            "💡 Always measure your dog's food portions to maintain consistent calorie intake.",
            "🥘 High-quality protein should make up about 25-30% of your dog's diet.",
            "💧 Fresh water should always be available - dogs need about 1 ounce per pound of body weight \
             daily.",
            "🕐 Try to feed at consistent times each day to help with digestion.",
            "🍖 Avoid giving table scraps - they can disrupt your dog's balanced nutrition.",
            "📏 Use a measuring cup specifically for pet food to ensure accuracy.",
            "🥕 Some vegetables like carrots and green beans make healthy, low-calorie treats.",
            "⚖️ Monitor your dog's weight weekly to catch any changes early.",
        ]
    }

    fn exercise_tips(dog_name: &str) -> Vec<&'static str> {
        vec![
            "🚶 Start with short walks and gradually increase duration as fitness improves.",
            "🎾 Interactive play sessions can be just as valuable as long walks.",
            "🏊 Swimming is excellent low-impact exercise, especially for older dogs.",
            "🌡️ Avoid intensive exercise during hot weather - early morning or evening is best.",
            "🎯 Mental stimulation through puzzle toys can tire dogs as much as physical exercise.",
            "👥 Socialization with other dogs provides both mental and physical benefits.",
            "🦴 Let your dog sniff during walks - it's mentally enriching exercise too.",
            "⏰ Most dogs need 30 minutes to 2 hours of exercise daily, depending on breed and age.",
        ]
    }

    fn weight_tips(dog_name: &str) -> Vec<&'static str> {
        vec![
            "📊 You should be able to feel (but not see) your dog's ribs with gentle pressure.",
            "👀 When viewed from above, your dog should have a visible waist behind the ribs.",
            "⚖️ Weekly weigh-ins help track progress - small changes are easier to address.",
            "🍎 Replace high-calorie treats with pieces of apple, carrot, or green beans.",
            "🥄 Reduce meal portions by 10-15% if weight loss is needed, but consult your vet first.",
            "🏃 Increase activity gradually - sudden changes can cause injury.",
            "📝 Keep a food and exercise diary to identify patterns and progress.",
            "👨‍⚕️ Regular vet checkups ensure weight management is safe and effective.",
        ]
    }

    fn health_tips(dog_name: &str) -> Vec<&'static str> {
        vec![
            "🦷 Daily teeth brushing prevents dental disease, which affects 80% of dogs by age 3.",
            "👁️ Check your dog's eyes regularly for redness, discharge, or cloudiness.",
            "👂 Clean ears weekly to prevent infections - they should smell fresh, not musty.",
            "💅 Trim nails every 2-4 weeks to prevent discomfort and joint problems.",
            "🧼 Regular grooming helps you spot lumps, bumps, or skin issues early.",
            "🌡️ Know your dog's normal temperature (101-102.5°F) and behavior patterns.",
            "💊 Keep a pet first aid kit handy with bandages, antiseptic, and emergency contacts.",
            "📅 Stay current with vaccinations and preventive treatments as recommended by your vet.",
        ]
    }

    fn emergency_tips(dog_name: &str) -> Vec<&'static str> {
        vec![
            "🚨 Keep your vet's emergency number and nearest animal hospital contact handy.",
            "🍫 Chocolate, grapes, onions, and xylitol are toxic to dogs - keep them away!",
            "🌡️ Signs of heatstroke: excessive panting, drooling, weakness - cool immediately and seek \
             help.",
            "🤮 If your dog is vomiting repeatedly or has bloody diarrhea, contact your vet immediately.",
            "⚡ Seizures lasting more than 5 minutes require immediate emergency care.",
            "💊 Never give human medications without vet approval - many are toxic to dogs.",
            "🩹 For cuts: clean with saline, apply pressure to stop bleeding, and see your vet.",
            "🫁 Difficulty breathing or blue gums are always emergencies - get help immediately.",
        ]
    }

    fn general_tips(dog_name: &str) -> Vec<&'static str> {
        vec![
            "❤️ Every dog is unique - what works for others might need adjustment for yours.",
            "📚 Consistency in routine helps dogs feel secure and reduces anxiety.",
            "🎉 Positive reinforcement is more effective than punishment for training.",
            "🕰️ Patience is key - health and behavior changes take time to show results.",
            "👨‍⚕️ When in doubt about your dog's health, it's always better to consult your vet.",
            "📱 This app works offline, so you can track your dog's data anytime!",
            "🏆 Small daily improvements add up to big health benefits over time.",
            "💝 The bond between you and your dog is the foundation of their wellbeing.",
        ]
    }
}

/// Hook for managing Gemini AI interactions
pub fn use_gemini() -> GeminiHook {
    let config = use_context::<Arc<Config>>();
    let network_status = use_context::<Signal<NetworkStatus>>();
    let offline_queue = use_context::<Signal<OfflineQueue>>();

    // Chat state
    let messages = use_signal(VecDeque::<ChatMessage>::new);
    let is_loading = use_signal(|| false);
    let current_error = use_signal(|| None::<GeminiError>);

    // AI service state
    let client = use_signal(|| None::<GeminiClient>);
    let service = use_signal(|| None::<GeminiService>);

    // Initialize client and service
    use_effect(move || {
        let config = config.clone();
        let mut client = client;
        let mut service = service;
        let mut current_error = current_error;

        spawn(async move {
            tracing::debug!("🤖 Initializing Gemini client...");

            match GeminiClient::new(config) {
                Ok(gemini_client) => {
                    // tracing::debug!("🤖 ✅ Gemini client created successfully");
                    let gemini_service = GeminiService::new(gemini_client.clone());
                    // tracing::debug!("🤖 ✅ Gemini service initialized");

                    client.set(Some(gemini_client));
                    service.set(Some(gemini_service));

                    // Clear any previous errors
                    current_error.set(None);
                    // tracing::debug!("🤖 ✅ Gemini integration fully initialized");
                },
                Err(error) => {
                    tracing::error!("🤖 ❌ Failed to create Gemini client: {:?}", error);
                    current_error.set(Some(error));
                },
            }
        });
    });

    GeminiHook {
        messages,
        is_loading,
        current_error,
        client,
        service,
        network_status,
        offline_queue,
    }
}

/// Gemini hook interface
#[derive(Clone)]
pub struct GeminiHook {
    messages:       Signal<VecDeque<ChatMessage>>,
    is_loading:     Signal<bool>,
    current_error:  Signal<Option<GeminiError>>,
    client:         Signal<Option<GeminiClient>>,
    service:        Signal<Option<GeminiService>>,
    network_status: Signal<NetworkStatus>,
    offline_queue:  Signal<OfflineQueue>,
}

impl GeminiHook {
    /// Send a chat message to the AI assistant
    pub fn send_message(&mut self, content: String, dog: Option<&Dog>) -> Result<(), GeminiError> {
        self.send_message_with_display(content.clone(), content, dog)
    }

    /// Send a message with custom display text
    pub fn send_message_with_display(
        &mut self,
        content: String,
        display_message: String,
        dog: Option<&Dog>,
    ) -> Result<(), GeminiError> {
        let message_id = uuid::Uuid::new_v4().to_string();

        // Add user message to chat
        let user_message = ChatMessage {
            id: message_id.clone(),
            content: content.clone(),
            display_message,
            sender: MessageSender::User,
            timestamp: Utc::now(),
            status: MessageStatus::Sent,
            dog_context: dog.map(|d| d.name.clone()),
        };

        self.messages.write().push_back(user_message);
        self.current_error.set(None);

        // Handle message based on network status
        let is_online = *self.network_status.read() == NetworkStatus::Online;

        tracing::info!(
            "🤖 📤 Sending message - online: {}, content: {}",
            is_online,
            &content[..std::cmp::min(50, content.len())]
        );

        if is_online {
            tracing::info!("🤖 🌐 Processing message online");
            self.is_loading.set(true); // Only set loading when online
            self.send_online_message(content, dog, message_id)
        } else {
            tracing::info!("🤖 📴 Processing message offline");
            // Don't set loading when offline - we'll provide immediate response
            self.send_offline_message(content, dog, message_id)
        }
    }

    /// Send a specialized recommendation request
    pub fn get_recommendation(
        &mut self,
        recommendation_type: RecommendationType,
        dog: &Dog,
    ) -> Result<(), GeminiError> {
        let prompt = self.build_recommendation_prompt(&recommendation_type, dog)?;
        let display_message = self.get_recommendation_display_message(&recommendation_type, dog);
        self.send_message_with_display(prompt, display_message, Some(dog))
    }

    /// Get a random daily recommendation
    pub fn get_daily_recommendation(&mut self, dog: &Dog) -> Result<(), GeminiError> {
        let is_online = *self.network_status.read() == NetworkStatus::Online;

        tracing::info!("🤖 🎯 Getting daily recommendation for {} - online: {}", dog.name, is_online);

        if is_online {
            tracing::info!("🤖 🌐 Processing daily recommendation online");
            // Set loading state for online processing
            self.is_loading.set(true);
            let service = self.service.read();
            if let Some(service) = service.as_ref() {
                tracing::info!("🤖 ✅ Service available, creating dog context");
                let dog_context = DogContextService::create_context(dog);

                spawn({
                    let mut service = service.clone();
                    let dog_context = dog_context;
                    let mut messages = self.messages;
                    let mut is_loading = self.is_loading;
                    let dog_name = dog.name.clone();

                    async move {
                        tracing::info!("🤖 🔄 Calling generate_health_tip API for {}", dog_name);
                        match service.generate_health_tip(&dog_context).await {
                            Ok(response) => {
                                tracing::info!(
                                    "🤖 ✅ Successfully generated health tip: {}",
                                    &response[..std::cmp::min(50, response.len())]
                                );
                                let ai_message = ChatMessage {
                                    id:              uuid::Uuid::new_v4().to_string(),
                                    content:         response.clone(),
                                    display_message: response,
                                    sender:          MessageSender::Assistant,
                                    timestamp:       Utc::now(),
                                    status:          MessageStatus::Delivered,
                                    dog_context:     Some(dog_name.clone()),
                                };
                                messages.write().push_back(ai_message);
                            },
                            Err(error) => {
                                tracing::error!("🤖 ❌ Failed to generate health tip: {:?}", error);
                                // Use contextual offline tip for daily recommendations
                                let offline_tip =
                                    OfflineTips::get_random_tip("daily health tip", Some(&dog_name));
                                let ai_message = ChatMessage {
                                    id:              uuid::Uuid::new_v4().to_string(),
                                    content:         offline_tip.clone(),
                                    display_message: offline_tip,
                                    sender:          MessageSender::Assistant,
                                    timestamp:       Utc::now(),
                                    status:          MessageStatus::Delivered,
                                    dog_context:     Some(dog_name.clone()),
                                };
                                messages.write().push_back(ai_message);
                            },
                        }
                        tracing::info!("🤖 🏁 Clearing loading state for daily recommendation");
                        is_loading.set(false);
                    }
                });
            } else {
                tracing::error!("🤖 ❌ Service not available for daily recommendation");
                self.is_loading.set(false);
                return Err(GeminiError::Configuration {
                    message: "Gemini service not initialized".to_string(),
                });
            }
        } else {
            tracing::info!("🤖 📴 Using offline tip for daily recommendation");
            // Use offline tips instead of fallback
            let offline_tip = OfflineTips::get_random_tip("daily health tip", Some(&dog.name));

            let ai_message = ChatMessage {
                id:              uuid::Uuid::new_v4().to_string(),
                content:         offline_tip.clone(),
                display_message: offline_tip,
                sender:          MessageSender::Assistant,
                timestamp:       Utc::now(),
                status:          MessageStatus::Delivered,
                dog_context:     Some(dog.name.clone()),
            };

            self.messages.write().push_back(ai_message);

            // Clear loading state when offline - CRITICAL FIX
            self.is_loading.set(false);
        }

        Ok(())
    }

    /// Clear chat history
    pub fn clear_messages(&mut self) {
        self.messages.write().clear();
        self.current_error.set(None);
    }

    /// Retry failed message
    pub fn retry_message(&mut self, message_id: String, dog: Option<&Dog>) {
        let mut messages = self.messages.write();
        if let Some(message) = messages.iter_mut().find(|m| m.id == message_id)
            && message.status == MessageStatus::Failed
        {
            let content = message.content.clone();
            message.status = MessageStatus::Pending;
            drop(messages); // Release the write lock

            // Retry sending the message
            let _ = self.send_message(content, dog);
        }
    }

    /// Get current messages
    pub fn get_messages(&self) -> VecDeque<ChatMessage> { self.messages.read().clone() }

    /// Check if currently loading
    pub fn is_loading(&self) -> bool { *self.is_loading.read() }

    /// Get current error
    pub fn get_error(&self) -> Option<GeminiError> { self.current_error.read().clone() }

    /// Process offline queue when network becomes available
    pub fn process_offline_queue(&mut self) {
        if *self.network_status.read() == NetworkStatus::Online {
            let queue = self.offline_queue.read();
            let ai_messages: Vec<_> = queue.ai_requests.clone();
            drop(queue);

            for ai_message in ai_messages {
                if ai_message.status == MessageStatus::Queued {
                    // Reprocess queued AI messages
                    spawn({
                        let service = self.service.read().clone();
                        let mut messages = self.messages;
                        let mut is_loading = self.is_loading;
                        let mut offline_queue = self.offline_queue;
                        let ai_message = ai_message.clone();

                        async move {
                            if let Some(mut service) = service
                                && let Some(ref dog_context) = ai_message.dog_context
                            {
                                match service
                                    .send_chat_message(&ai_message.user_message, dog_context)
                                    .await
                                {
                                    Ok(response) => {
                                        let chat_message = ChatMessage {
                                            id:              ai_message.id.clone(),
                                            content:         response.clone(),
                                            display_message: response,
                                            sender:          MessageSender::Assistant,
                                            timestamp:       Utc::now(),
                                            status:          MessageStatus::Delivered,
                                            dog_context:     Some(dog_context.name.clone()),
                                        };
                                        messages.write().push_back(chat_message);

                                        // Remove from offline queue
                                        offline_queue.write().remove_ai_request(&ai_message.id);
                                    },
                                    Err(_error) => {
                                        // Update status to failed
                                        offline_queue.write().update_ai_request_status(
                                            &ai_message.id,
                                            MessageStatus::Failed,
                                        );
                                    },
                                }
                            }
                            is_loading.set(false);
                        }
                    });
                }
            }
        }
    }

    // Private helper methods

    fn send_online_message(
        &mut self,
        content: String,
        dog: Option<&Dog>,
        message_id: String,
    ) -> Result<(), GeminiError> {
        tracing::info!(
            "🤖 🌐 send_online_message called with content: {}",
            &content[..std::cmp::min(50, content.len())]
        );

        let service = self.service.read();
        if let Some(service) = service.as_ref() {
            tracing::info!("🤖 ✅ Service available for online message");
            let dog_context = dog.map(DogContextService::create_context);
            let dog_name_for_log = dog.map_or_else(|| "None".to_string(), |d| d.name.clone());
            tracing::info!("🤖 📝 Dog context created for: {}", dog_name_for_log);

            spawn({
                let mut service = service.clone();
                let content = content;
                let dog_context = dog_context;
                let mut messages = self.messages;
                let mut is_loading = self.is_loading;
                let mut current_error = self.current_error;
                let dog_name = dog.map(|d| d.name.clone());

                async move {
                    tracing::info!("🤖 🔄 Starting async API call for online message");

                    let result = if let Some(dog_context) = dog_context {
                        tracing::info!("🤖 🐕 Sending chat message with dog context");
                        service.send_chat_message(&content, &dog_context).await
                    } else {
                        tracing::info!("🤖 💬 Sending general message without dog context");
                        service.send_general_message(&content).await
                    };

                    match result {
                        Ok(response) => {
                            tracing::info!(
                                "🤖 ✅ API call successful, response: {}",
                                &response[..std::cmp::min(50, response.len())]
                            );
                            let ai_message = ChatMessage {
                                id:              uuid::Uuid::new_v4().to_string(),
                                content:         response.clone(),
                                display_message: response,
                                sender:          MessageSender::Assistant,
                                timestamp:       Utc::now(),
                                status:          MessageStatus::Delivered,
                                dog_context:     dog_name.clone(),
                            };
                            messages.write().push_back(ai_message);
                            tracing::info!("🤖 📨 Message added to chat for dog: {:?}", dog_name);
                        },
                        Err(error) => {
                            tracing::error!("🤖 ❌ API call failed with error: {:?}", error);
                            current_error.set(Some(error));

                            // Use contextual offline tip instead of generic fallback
                            let dog_name_str = dog_name.as_deref();
                            let offline_tip = OfflineTips::get_random_tip(&content, dog_name_str);
                            let ai_message = ChatMessage {
                                id:              uuid::Uuid::new_v4().to_string(),
                                content:         offline_tip.clone(),
                                display_message: offline_tip,
                                sender:          MessageSender::Assistant,
                                timestamp:       Utc::now(),
                                status:          MessageStatus::Delivered, /* Changed from Failed to Delivered since we're providing useful content */
                                dog_context:     dog_name.clone(),
                            };
                            messages.write().push_back(ai_message);
                            tracing::info!(
                                "🤖 📨 Offline tip message added to chat for dog: {:?}",
                                dog_name
                            );
                        },
                    }
                    tracing::info!("🤖 🏁 Clearing loading state for online message");
                    is_loading.set(false);
                }
            });
        } else {
            tracing::error!("🤖 ❌ Service not available for online message");
            return Err(GeminiError::Configuration {
                message: "Gemini service not initialized".to_string(),
            });
        }

        tracing::info!("🤖 ✅ send_online_message completed successfully");
        Ok(())
    }

    fn send_offline_message(
        &mut self,
        content: String,
        dog: Option<&Dog>,
        message_id: String,
    ) -> Result<(), GeminiError> {
        // Add to offline queue
        let dog_context = dog.map_or_else(
            crate::integrations::gemini::models::DogContext::default,
            DogContextService::create_context,
        );

        let ai_message = AiMessage {
            id:           message_id,
            user_message: content.clone(),
            message_type: crate::integrations::gemini::MessageType::Text,
            dog_context:  Some(dog_context),
            timestamp:    Utc::now(),
            response:     None,
            status:       MessageStatus::Queued,
        };

        self.offline_queue.write().add_ai_request(ai_message);

        // Generate appropriate offline tip instead of showing loading state
        let dog_name = dog.map(|d| d.name.as_str());
        let offline_tip = OfflineTips::get_random_tip(&content, dog_name);

        let offline_message = ChatMessage {
            id:              uuid::Uuid::new_v4().to_string(),
            content:         offline_tip.clone(),
            display_message: offline_tip,
            sender:          MessageSender::Assistant,
            timestamp:       Utc::now(),
            status:          MessageStatus::Delivered, /* Changed from Queued to Delivered since we're
                                                        * providing immediate content */
            dog_context:     dog.map(|d| d.name.clone()),
        };

        self.messages.write().push_back(offline_message);
        self.is_loading.set(false);

        Ok(())
    }

    fn build_recommendation_prompt(
        &self,
        recommendation_type: &RecommendationType,
        dog: &Dog,
    ) -> Result<String, GeminiError> {
        // let base_context =
        //     DogContextService::generate_prompt_context(&DogContextService::create_context(dog));

        let specific_request = match recommendation_type {
            RecommendationType::Nutrition => {
                "Based on this dog profile, please provide specific nutrition recommendations. Focus \
                     on:\n- Daily calorie intake optimization\n- Food type recommendations\n- Feeding \
                     schedule suggestions\n- Treats and supplements".to_string()
            },
            RecommendationType::Exercise => {
                "Based on this dog profile, please provide specific exercise recommendations. Focus \
                     on:\n- Daily activity duration and intensity\n- Types of exercises suitable for this \
                     dog\n- Activity scheduling\n- Safety considerations".to_string()
            },
            RecommendationType::Health => {
                "Based on this dog profile, please provide general health guidance. Focus on:\n- \
                     Weight management strategies\n- General wellness tips\n- When to consult a \
                     veterinarian\n- Preventive care recommendations".to_string()
            },
            RecommendationType::General => {
                "Based on this dog profile, please provide general care advice and tips for \
                     maintaining optimal health and happiness.".to_string()
            },
            RecommendationType::DailyTip => {
                "Based on this dog profile, please provide a helpful daily tip for today. Keep it \
                     concise and actionable.".to_string()
            },
            RecommendationType::WeightManagement => {
                "Based on this dog profile, please provide specific weight management advice. Focus \
                     on:\n- Current weight assessment\n- Target weight goals\n- Diet adjustments\n- \
                     Exercise modifications".to_string()
            },
            RecommendationType::AgeAppropriate => {
                "Based on this dog profile, please provide age-appropriate care recommendations. \
                     Focus on:\n- Life stage-specific needs\n- Age-related health considerations\n- \
                     Activity level adjustments\n- Preventive care for this age".to_string()
            },
            RecommendationType::Emergency => {
                "Based on this dog profile, please provide emergency preparedness information. Focus \
                     on:\n- Warning signs to watch for\n- When to contact a veterinarian immediately\n- \
                     Basic first aid tips\n- Emergency contact information importance".to_string()
            },
        };

        Ok(specific_request)
    }

    /// Get user-friendly display message for recommendation requests
    fn get_recommendation_display_message(
        &self,
        recommendation_type: &RecommendationType,
        dog: &Dog,
    ) -> String {
        match recommendation_type {
            RecommendationType::Nutrition => {
                format!("Can you provide nutrition recommendations for {}?", dog.name)
            },
            RecommendationType::Exercise => {
                format!("What exercise recommendations do you have for {}?", dog.name)
            },
            RecommendationType::Health => {
                format!("Can you give me general health advice for {}?", dog.name)
            },
            RecommendationType::General => {
                format!("Can you provide general care advice for {}?", dog.name)
            },
            RecommendationType::DailyTip => {
                format!("Can you give me a daily tip for {}?", dog.name)
            },
            RecommendationType::WeightManagement => {
                format!("Can you help with weight management advice for {}?", dog.name)
            },
            RecommendationType::AgeAppropriate => {
                format!("What age-appropriate care recommendations do you have for {}?", dog.name)
            },
            RecommendationType::Emergency => {
                format!("Can you provide emergency preparedness information for {}?", dog.name)
            },
        }
    }
}

/// Default implementation for `DogContext` when no dog is available
impl Default for crate::integrations::gemini::models::DogContext {
    fn default() -> Self {
        Self {
            name:                 "Unknown Dog".to_string(),
            breed:                Some("Mixed".to_string()),
            age_months:           None,
            age_years:            None,
            weight_kg:            None,
            gender:               "unknown".to_string(),
            size:                 "medium".to_string(),
            current_weight_kg:    None,
            target_weight_kg:     None,
            activity_level:       "moderate".to_string(),
            body_condition_score: None,
            daily_calories:       None,
            typical_activity:     None,
            is_neutered:          None,
            recent_activities:    vec![],
            preferred_foods:      vec![],
            health_concerns:      vec![],
            daily_calorie_goal:   None,
            activity_summary:     "No activity data available".to_string(),
            health_notes:         None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_chat_message_creation() {
        let message = ChatMessage {
            id:              "test-id".to_string(),
            content:         "Hello AI".to_string(),
            display_message: "Hello AI".to_string(),
            sender:          MessageSender::User,
            timestamp:       Utc::now(),
            status:          MessageStatus::Sent,
            dog_context:     Some("Buddy".to_string()),
        };

        assert_eq!(message.sender, MessageSender::User);
        assert_eq!(message.content, "Hello AI");
        assert_eq!(message.display_message, "Hello AI");
        assert_eq!(message.dog_context, Some("Buddy".to_string()));
    }

    #[test]
    fn test_recommendation_type_prompt_building() {
        // This would be expanded with actual tests when the hook is used in practice
        let recommendation = RecommendationType::Nutrition;
        assert!(matches!(recommendation, RecommendationType::Nutrition));
    }

    #[test]
    fn test_offline_tips_contextual_responses() {
        // Test nutrition-related requests
        let nutrition_tip = OfflineTips::get_random_tip("What should I feed my dog?", Some("Buddy"));
        assert!(
            nutrition_tip.contains("💡")
                || nutrition_tip.contains("🥘")
                || nutrition_tip.contains("💧")
                || nutrition_tip.contains("🕐")
                || nutrition_tip.contains("🍖")
                || nutrition_tip.contains("📏")
                || nutrition_tip.contains("🥕")
                || nutrition_tip.contains("⚖️")
        );

        // Test exercise-related requests
        let exercise_tip =
            OfflineTips::get_random_tip("How much exercise does my dog need?", Some("Buddy"));
        assert!(
            exercise_tip.contains("🚶")
                || exercise_tip.contains("🎾")
                || exercise_tip.contains("🏊")
                || exercise_tip.contains("🌡️")
                || exercise_tip.contains("🎯")
                || exercise_tip.contains("👥")
                || exercise_tip.contains("🦴")
                || exercise_tip.contains("⏰")
        );

        // Test weight-related requests
        let weight_tip = OfflineTips::get_random_tip("My dog needs to lose weight", Some("Buddy"));
        assert!(
            weight_tip.contains("📊")
                || weight_tip.contains("👀")
                || weight_tip.contains("⚖️")
                || weight_tip.contains("🍎")
                || weight_tip.contains("🥄")
                || weight_tip.contains("🏃")
                || weight_tip.contains("📝")
                || weight_tip.contains("👨‍⚕️")
        );

        // Test health-related requests
        let health_tip = OfflineTips::get_random_tip("Is my dog healthy?", Some("Buddy"));
        assert!(
            health_tip.contains("🦷")
                || health_tip.contains("👁️")
                || health_tip.contains("👂")
                || health_tip.contains("💅")
                || health_tip.contains("🧼")
                || health_tip.contains("🌡️")
                || health_tip.contains("💊")
                || health_tip.contains("📅")
        );

        // Test emergency-related requests
        let emergency_tip =
            OfflineTips::get_random_tip("My dog ate chocolate! Emergency help!", Some("Buddy"));
        assert!(
            emergency_tip.contains("🚨")
                || emergency_tip.contains("🍫")
                || emergency_tip.contains("🌡️")
                || emergency_tip.contains("🤮")
                || emergency_tip.contains("⚡")
                || emergency_tip.contains("💊")
                || emergency_tip.contains("🩹")
                || emergency_tip.contains("🫁")
        );

        // Test general requests
        let general_tip = OfflineTips::get_random_tip("Hello, how are you?", Some("Buddy"));
        assert!(
            general_tip.contains("❤️")
                || general_tip.contains("📚")
                || general_tip.contains("🎉")
                || general_tip.contains("🕰️")
                || general_tip.contains("👨‍⚕️")
                || general_tip.contains("📱")
                || general_tip.contains("🏆")
                || general_tip.contains("💝")
        );

        // Test without dog name
        let no_dog_tip = OfflineTips::get_random_tip("General dog care advice", None);
        assert!(!no_dog_tip.is_empty());
        assert!(no_dog_tip.contains("your dog") || no_dog_tip.contains("dogs"));
    }

    #[test]
    fn test_offline_tips_consistency() {
        // Test that the same request returns the same tip (due to hash-based selection)
        let request = "nutrition advice for my dog";
        let dog_name = Some("Buddy");

        let tip1 = OfflineTips::get_random_tip(request, dog_name);
        let tip2 = OfflineTips::get_random_tip(request, dog_name);

        // Should be the same due to hash-based selection, but only within the same time window
        // We'll just check that both return valid nutrition tips
        assert!(tip1.len() > 10); // Non-empty meaningful tip
        assert!(tip2.len() > 10); // Non-empty meaningful tip
        assert!(
            tip1.contains("💡")
                || tip1.contains("🥘")
                || tip1.contains("measure")
                || tip1.contains("food")
                || tip1.contains("water")
        );
        assert!(
            tip2.contains("💡")
                || tip2.contains("🥘")
                || tip2.contains("measure")
                || tip2.contains("food")
                || tip2.contains("water")
        );
    }

    #[test]
    fn test_offline_tips_dog_name_personalization() {
        // Test that tips with dog names are personalized vs without
        let with_dog = OfflineTips::get_random_tip("health advice", Some("Buddy"));
        let without_dog = OfflineTips::get_random_tip("health advice", None);

        // Both should be valid tips
        assert!(!with_dog.is_empty());
        assert!(!without_dog.is_empty());

        // Without dog name should contain generic references
        assert!(without_dog.contains("your dog") || without_dog.contains("dogs"));
    }
}
