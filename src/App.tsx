
import {Toaster} from "@/components/ui/toaster"
import {Toaster as Sonner} from "@/components/ui/sonner"
import {QueryClient, QueryClientProvider} from "@tanstack/react-query"
import {BrowserRouter, Routes, Route, useLocation, useNavigate} from "react-router-dom"
import {AuthProvider} from "@/hooks/useAuth"
import {LocalDataProvider} from "@/hooks/useLocalData"
import {LanguageProvider} from "@/hooks/useLanguage"
import {useTheme} from "@/components/ThemeProvider"
import {WalkTrackerProvider} from "@/hooks/useWalkTracker"
import BottomNavbar from "@/components/BottomNavbar"
import OfflineIndicator from "@/components/OfflineIndicator"
import InstallPrompt from "@/components/InstallPrompt"
import SplashScreen from "@/components/SplashScreen"
import {useEffect} from "react"
import {useLocalData} from "@/hooks/useLocalData"
import {ThemeProvider} from "@/components/ThemeProvider"

import HomePage from "@/pages/HomePage"
import OnboardingPage from "@/pages/OnboardingPage"
import ResultsPage from "@/pages/ResultsPage"
import ActivityHistoryPage from "@/pages/ActivityHistoryPage"
import WeightHistoryPage from "@/pages/WeightHistoryPage"
import SettingsPage from "@/pages/SettingsPage"
import AddDogPage from "@/pages/AddDogPage"
import DogProfilePage from "@/pages/DogProfilePage"
import NotFound from "@/pages/NotFound"
import AIChatPage from "@/pages/AIChatPage"

const queryClient = new QueryClient();

const AppInner = () => {
  const {isLoading} = useLocalData();

  // Show splash screen while data is loading
  if(isLoading) {
    return <SplashScreen />;
  }

  return (
    <LanguageProvider>
      <ThemeProvider>
        <WalkTrackerProvider>
          <BrowserRouter>
            <AppContent />
          </BrowserRouter>
        </WalkTrackerProvider>
      </ThemeProvider>
    </LanguageProvider>
  );
};

const AppContent = () => {
  const {theme} = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const {pets} = useLocalData();

  // Redirect to onboarding if no pets and not already on onboarding page
  useEffect(() => {
    // console.log('Current location:', location.pathname, ', pets:', pets?.length);
    if(pets.length === 0 &&
      location.pathname !== '/onboarding' &&
      !location.pathname.includes('/add-dog')) {
      navigate('/onboarding');
    }
  }, [location.pathname, pets]);

  // Define different gradient backgrounds for different routes in colored theme
  const getPageGradientClass = () => {
    if(theme !== 'colored') return '';

    const path = location.pathname;

    if(path === '/') return 'page-gradient-1';
    if(path === '/activities/history') return 'page-gradient-2';
    if(path === '/meals') return 'page-gradient-3';
    if(path === '/weight-history') return 'page-gradient-4';
    if(path === '/settings') return 'page-gradient-1';
    if(path.startsWith('/dog/')) return 'page-gradient-5';
    if(path === '/add-dog') return 'page-gradient-4';
    if(path === '/results') return 'page-gradient-2';
    if(path === '/onboarding') return 'page-gradient-1';

    return 'page-gradient-1';
  };

  return (
    // <div className={`max-w-md mx-auto min-h-screen bg-primary/20 absolute top-0 ${getPageGradientClass()}`}>
    // <div className={`bg-primary/20 ${getPageGradientClass()} m-0 p-0`} style={{minHeight: '517px', height: '517px', maxHeight: '517px'}}>
    // <div className={`absolute top-0 left-0 right-0 bg-primary/20 ${getPageGradientClass()} m-0 p-0`}>
    <div className={`max-w-md mx-auto min-h-screen absolute top-0 left-0 right-0 ${getPageGradientClass()}`}>
      <Toaster />
      <Sonner />
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/activities/history" element={<ActivityHistoryPage />} />
        <Route path="/add-dog" element={<AddDogPage />} />
        <Route path="/ai" element={<AIChatPage />} />
        <Route path="/dog/:id" element={<DogProfilePage />} />
        <Route path="/dog/" element={<DogProfilePage />} />
        <Route path="/onboarding" element={<OnboardingPage />} />
        <Route path="/results" element={<ResultsPage />} />
        <Route path="/settings" element={<SettingsPage />} />
        <Route path="/weight-history" element={<WeightHistoryPage />} />
        <Route path="*" element={<NotFound />} />
      </Routes>
      <BottomNavbar />
      <OfflineIndicator />
      <InstallPrompt />
    </div>
  );
};

const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <LocalDataProvider>
          <AppInner />
        </LocalDataProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
