
import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'
import './index.css'

// // Register service worker for background processing
// console.log('serviceWorker in navigator (main)', 'serviceWorker' in navigator);
// if('serviceWorker' in navigator) {
//   console.log('Registering service worker (main)...');
//   window.addEventListener('load', () => {
//     navigator.serviceWorker.register('/service-worker.js')
//       .then(registration => {
//         console.log('ServiceWorker registration successful with scope: ', registration.scope);
//       })
//       .catch(error => {
//         console.log('ServiceWorker registration failed: ', error);
//       });
//   });
// }

console.log('Starting app...');

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)

// document.body.style.height = '400px';
// document.querySelector('html').setAttribute('height', '400px')
// document.querySelector('body').setAttribute('height', '400px')
// document.querySelector('html').setAttribute('style', 'height: 400px;')
// document.documentElement.style.setProperty('height', '400px');
// const viewport = document.querySelector("meta[name=viewport]");
// viewport.setAttribute("content", `height=400px, width=device-width, initial-scale=1.0`);


// window.visualViewport.addEventListener("resize", function() {
//   const initialHeight = window.visualViewport.height;
//   const currentHeight = window.innerHeight;
//   const screenHeight = screen.height;
//   const offsetHeight = document.documentElement.offsetHeight;
//   const documentHeight = document.documentElement.clientHeight || document.body.clientHeight;

//   const metaViewport = document.querySelector('meta[name=viewport]');
//   if(currentHeight < screenHeight) {
//     // Keyboard is open, adjust layout
//     document.documentElement.style.setProperty('overflow', 'auto');
//     // metaViewport.setAttribute('content', 'height=' + currentHeight + 'px, width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover, interactive-widget=resizes-content');
//     // document.body.style.height = currentHeight + 'px';
//     alert(`Open: ${initialHeight}, ${currentHeight}, ${screenHeight}, ${offsetHeight}, ${documentHeight}`);
//   } else {
//     // Keyboard is closed, reset layout
//     document.documentElement.style.setProperty('overflow', 'hidden');
//     // metaViewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover, interactive-widget=resizes-content');
//     alert(`Closed: ${initialHeight}, ${currentHeight}, ${screenHeight}, ${offsetHeight}, ${documentHeight}`);
//   }
// });

document.ontouchmove = function(e) {
  e.preventDefault();
}
// input.onfocus = function () {
//   window.scrollTo(0, 0)
//   document.body.scrollTop = 0
// }
