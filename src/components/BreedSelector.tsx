import React from 'react'
import {
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage
} from '@/components/ui/form';
import {BreedAutocomplete} from '@/components/BreedAutocomplete'
import {FormField} from "@/components/ui/form"

interface BreedSelectorProps {
  control: any;
  name: string;
  photoUrl?: string;
  showLabel?: boolean;
}

const BreedSelector = ({control, name, photoUrl, showLabel = true}: BreedSelectorProps) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({field}) => (
        <FormItem>
          {showLabel && <FormLabel>Breed (Optional)</FormLabel>}
          <FormControl>
            <BreedAutocomplete
              value={field.value}
              onChange={field.onChange}
              imageUrl={photoUrl}
            />
          </FormControl>
          {showLabel && <FormDescription>
            Select or let AI recognize your dog's breed
          </FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default BreedSelector;
