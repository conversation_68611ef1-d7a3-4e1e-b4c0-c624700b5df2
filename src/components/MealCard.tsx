
import React from 'react'
import {Card, CardContent} from '@/components/ui/card'
import {cn} from '@/lib/utils'
import {useTheme} from '@/components/ThemeProvider'

interface MealCardProps {
  icon: React.ReactNode;
  title: string;
  time: string;
  food: string;
  portion?: string;
  calories?: number;
  color?: string;
  className?: string;
}

const MealCard = ({
  icon,
  title,
  time,
  food,
  portion,
  calories,
  color = 'bg-pet-yellow',
  className
}: MealCardProps) => {
  const {theme} = useTheme();

  return (
    <Card className={cn("border-0 shadow-xs hover:shadow-md transition-all", className)}>
      <CardContent className="p-4 flex items-center space-x-3">
        <div className={cn(
          "p-3 rounded-full flex items-center justify-center",
          theme === "colored" ? "bg-white/20" : color
        )}>
          {React.isValidElement(icon) ?
            React.cloneElement(icon as React.ReactElement<{className?: string}>, {
              className: cn("h-5 w-5",
                theme === "colored" ? "text-pet-food" : "text-white"
              )
            }) : icon}
        </div>
        <div className="flex-1">
          <div className="flex justify-between">
            <p className="font-medium">{title}</p>
            <p className="text-sm text-gray-500">{time}</p>
          </div>
          <p className="text-sm font-medium mt-1">{food}</p>
          <div className="flex items-center mt-0.5 text-sm text-gray-500">
            {portion && <span className="mr-3">{portion}</span>}
            {calories && <span>{calories} kcal</span>}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MealCard;
