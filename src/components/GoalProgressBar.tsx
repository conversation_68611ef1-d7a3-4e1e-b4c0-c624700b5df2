
import React from 'react';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

interface GoalProgressBarProps {
  title: string;
  currentValue: number;
  targetValue: number;
  unit?: string;
  className?: string;
}

const GoalProgressBar = ({ 
  title, 
  currentValue, 
  targetValue, 
  unit = '', 
  className 
}: GoalProgressBarProps) => {
  const progressPercentage = Math.min(Math.round((currentValue / targetValue) * 100), 100);
  
  return (
    <div className={cn("w-full", className)}>
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-gray-600">{title}</span>
        <span className="text-sm font-medium">
          {currentValue}/{targetValue} {unit}
        </span>
      </div>
      <Progress value={progressPercentage} className="h-2" />
    </div>
  );
};

export default GoalProgressBar;
