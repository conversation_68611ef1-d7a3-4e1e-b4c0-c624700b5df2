import React, {useState, useEffect} from 'react'
import {Edit2 as Edit} from 'lucide-react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import {Pet} from '@/data/types'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {Button} from '@/components/ui/button'
import SizeSelector from '@/components/SizeSelector'
import {Calendar} from '@/components/ui/calendar'
import {useForm} from 'react-hook-form'
import * as z from 'zod'
import {zodResolver} from '@hookform/resolvers/zod'
import {formatDate} from '@/lib/utils'

interface EditDogProfileModalProps {
  pet: Pet;
  field: 'size' | 'birthday';
  onSave: (newValue: string | Date | undefined) => void;
  onCancel: () => void;
}

const EditDogProfileModal: React.FC<EditDogProfileModalProps> = ({pet, field, onSave, onCancel}) => {
  const [open, setOpen] = useState(false);

  const schema = z.object({
    size: z.enum(['small', 'medium', 'large']).optional(),
    birthday: z.date().optional(),
  });

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      size: pet?.size as "small" | "medium" | "large" || "medium",
      birthday: pet?.birthday || undefined,
    },
  });

  useEffect(() => {
    form.setValue('size', pet?.size as "small" | "medium" | "large" || "medium");
    form.setValue('birthday', pet?.birthday || undefined);
  }, [pet, field, form.setValue]);

  const handleSave = () => {
    const newValue = form.getValues(field);
    onSave(newValue);
    setOpen(false);
  };

  const handleCancel = () => {
    onCancel();
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="text-secondary rounded-full"
        >
          <Edit className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit {field === 'size' ? 'Size' : 'Birthday'}</DialogTitle>
          <DialogDescription>
            {`Update your dog's ${field === 'size' ? 'size' : 'birthday'}.`}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <FormField
            control={form.control}
            name={field}
            render={({field: formField}) => (
              <FormItem>
                {field === 'size' && <FormLabel>Size</FormLabel>}
                <FormControl>
                  {field === 'size' ? (
                    <SizeSelector
                      control={form.control}
                      name="size"
                    />
                  ) : (
                    <Calendar
                      selected={formField.value instanceof Date ? formField.value : undefined}
                      onSelect={formField.onChange}
                      startMonth={new Date(new Date() - 1000 * 60 * 60 * 24 * 365 * 20)}
                      endMonth={new Date()}
                      defaultMonth={formField.value instanceof Date ? formField.value : new Date()}
                    />
                  )}
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </Form>
        <div className="grid gap-4 py-4">
          <Button onClick={handleSave}>Save</Button>
          <Button type="button" variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditDogProfileModal;
