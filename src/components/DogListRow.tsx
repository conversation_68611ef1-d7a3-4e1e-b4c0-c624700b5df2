import React from 'react'
import { ChevronRight, GripVertical, Trash2 } from 'lucide-react'
import { Link } from 'react-router-dom'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Pet } from '@/data/types'
import PetAvatar from './PetAvatar'

interface DogListRowProps {
  pet: Pet;
  // onDeleteClick: (pet: Pet) => void;
}

const DogListRow = ({ pet }: DogListRowProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: pet.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 1 : 0,
    opacity: isDragging ? 0.75 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex items-center justify-between py-3 px-2"
    >
      <div className="flex items-center flex-1">
        <div
          {...attributes}
          {...listeners}
          className="cursor-grab touch-none px-2"
        >
          <GripVertical className="h-5 w-5 text-gray-400" />
        </div>

        <PetAvatar
          src={pet.image}
          name={pet.name}
          size="sm"
          className="mr-3"
        />

        <div className="flex-1">
          <p className="font-medium">{pet.name}</p>
          <p className="text-xs text-muted-foreground">
            {pet.breed || pet.type || 'Dog'}
          </p>
        </div>
      </div>

      <div className="flex items-center">
        {/* <button
          onClick={() => onDeleteClick(pet)}
          className="p-2 text-red-500 hover:text-red-600 transition-colors mr-1"
          aria-label={`Delete ${pet.name}`}
        >
          <Trash2 className="h-4 w-4" />
        </button> */}

        <Link
          to={`/dog/${pet.id}`}
          className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          aria-label={`Edit ${pet.name}`}
        >
          <ChevronRight className="h-5 w-5" />
        </Link>
      </div>
    </div>
  );
};

export default DogListRow;
