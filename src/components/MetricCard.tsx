
import React from 'react'
import {Card, CardContent} from '@/components/ui/card'
import {cn} from '@/lib/utils'
import {useTheme} from '@/components/ThemeProvider'

interface MetricCardProps {
  icon: React.ReactNode;
  title: string;
  value: string | number;
  unit?: string;
  color?: string;
  // iconColor?: string;
  className?: string;
}

const MetricCard = ({
  icon,
  title,
  value,
  unit,
  color = 'lightPurple',
  // iconColor,
  className
}: MetricCardProps) => {
  const {theme} = useTheme();
  // const bgColor = theme === "colored" ? "bg-white/20" : 'pet-card-gradient-' + color;
  let bgColor = '';
  let iconColor = '';
  switch(color) {
    case 'teal':
      bgColor = 'pet-card-gradient-teal';
      iconColor = 'bg-pet-teal/95';
      break;
    case 'purple':
      bgColor = 'pet-card-gradient-purple';
      iconColor = 'bg-pet-purple/50';
      break;
    case 'pink':
      bgColor = 'pet-card-gradient-pink';
      iconColor = 'bg-pet-pink/95';
      break;
    case 'blue':
      bgColor = 'pet-card-gradient-blue';
      iconColor = 'bg-pet-blue/90';
      break;
    case 'orange':
      bgColor = 'pet-card-gradient-orange';
      iconColor = 'bg-pet-orange/90';
      break;
    case 'yellow':
      bgColor = 'pet-card-gradient-yellow';
      iconColor = 'bg-pet-yellow/90';
      break;
    default:
      bgColor = 'pet-card-gradient-lightPurple';
      iconColor = 'bg-pet-lightPurple/60';
      break;
  }
  // bgColor = 'bg-white';

  return (
    // <Card className={cn("shadow-none border overflow-hidden transition-all hover:shadow-md bg-pet-teal/75", bgColor, className)}>
    // <Card className={cn("shadow-xs border-2 overflow-hidden transition-all border-primary/20", className)}>
    <Card className={cn("shadow-xs overflow-hidden transition-all bg-primary/15", className)}>
      <CardContent className="px-4 py-3 flex items-center space-x-2">
        <div className="flex-1">
          <p className="text-md text-black font-medium mb-2">{title}</p>
          <div className="flex items-center justify-start space-x-2">
            <div className={cn(
              "p-3 rounded-full flex items-center justify-center",
              theme === "colored" ? "bg-white/20" : iconColor
            )}>
              {React.isValidElement(icon)
                ? React.cloneElement(icon as React.ReactElement<{className?: string}>, {
                  className: cn(
                    "h-4 w-4",
                    theme === "colored" ? "text-pet-purple" : "text-white"
                  )
                })
                : icon}
            </div>
            <div>
              {/* <p className="text-3xl font-semibold text-white/85"> */}
              <p className="text-3xl font-semibold text-black/85">
                {value}{unit && <span className="text-sm">{unit}</span>}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MetricCard;
