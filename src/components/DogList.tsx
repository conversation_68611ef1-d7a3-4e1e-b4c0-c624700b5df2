
import React, { useState, useEffect } from 'react'
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors, DragEndEvent } from '@dnd-kit/core'
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { restrictToVerticalAxis } from '@dnd-kit/modifiers'
import { Pet } from '@/data/types'
import { useLocalData } from '@/hooks/useLocalData'
import DogListRow from './DogListRow'
import DeleteDogDialog from './DeleteDogDialog'
import { Card } from './ui/card'
import { Separator } from './ui/separator'

const DogList = () => {
  const { pets, updatePet, deletePet } = useLocalData();
  const [orderedPets, setOrderedPets] = useState<Pet[]>([]);
  const [petToDelete, setPetToDelete] = useState<Pet | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Configure sensors for keyboard and pointer (mouse/touch) input
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 8px movement required before drag starts
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    // Sort pets based on their existing order or default to current order
    const sorted = [...pets].sort((a, b) => {
      const orderA = a.order || 0;
      const orderB = b.order || 0;
      return orderA - orderB;
    });

    setOrderedPets(sorted);
  }, [pets]);

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) return;

    if (active.id !== over.id) {
      setOrderedPets((items) => {
        const oldIndex = items.findIndex(pet => pet.id === active.id);
        const newIndex = items.findIndex(pet => pet.id === over.id);

        const newOrderedPets = arrayMove(items, oldIndex, newIndex);

        // Update order for each pet
        newOrderedPets.forEach((pet, index) => {
          const updatedPet = { ...pet, order: index };
          updatePet(updatedPet);
        });

        return newOrderedPets;
      });
    }
  };

  const handleDeleteClick = (pet: Pet) => {
    setPetToDelete(pet);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteDialogClose = () => {
    setIsDeleteDialogOpen(false);
    setPetToDelete(null);
  };

  const handleDeletePet = () => {
    if (petToDelete) {
      deletePet(petToDelete.id);
      setIsDeleteDialogOpen(false);
      setPetToDelete(null);
    }
  };

  if (pets.length === 0) {
    return (
      <div className="text-center py-4 text-gray-500">
        No pets added yet.
      </div>
    );
  }

  return (
    <>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
        modifiers={[restrictToVerticalAxis]}
      >
        <Card className="overflow-hidden">
          <SortableContext
            items={orderedPets.map(pet => pet.id)}
            strategy={verticalListSortingStrategy}
          >
            <div className="divide-y">
              {orderedPets.map((pet, index) => (
                <React.Fragment key={pet.id}>
                  <DogListRow
                    pet={pet}
                    // onDeleteClick={handleDeleteClick}
                  />
                  {index < orderedPets.length - 1 && <Separator />}
                </React.Fragment>
              ))}
            </div>
          </SortableContext>
        </Card>
      </DndContext>

      {/* {petToDelete && (
        <DeleteDogDialog
          dogId={petToDelete.id}
          dogName={petToDelete.name}
          isOpen={isDeleteDialogOpen}
          onClose={handleDeleteDialogClose}
          onDelete={handleDeletePet}
        />
      )} */}
    </>
  );
};

export default DogList;
