
import React from 'react'
import {Card, CardContent} from '@/components/ui/card'
import {Utensils, Dog} from 'lucide-react'
import {PetFood} from '@/data/types'
import * as FoodIcons from 'lucide-react'
import {useLanguage} from '@/hooks/useLanguage'
import {useTheme} from '@/components/ThemeProvider'
import {cn} from '@/lib/utils'

interface DailyDietCardProps {
  petName: string;
  dailyCalories: number;
  preferredFoods?: PetFood[];
  dietAdjustment?: number;
}

const TodayDiet = ({
  petName,
  dailyCalories,
  preferredFoods = [],
  dietAdjustment = 0
}: DailyDietCardProps) => {
  const {t} = useLanguage();
  const {theme} = useTheme();
  const activeFoods = preferredFoods.filter(food => food.isPreferred);

  const getFoodIcon = (iconName: string) => {
    // @ts-ignore - This is safe because we're checking for existence
    const IconComponent = FoodIcons[iconName.charAt(0).toUpperCase() + iconName.slice(1)];
    return IconComponent ?
      <IconComponent className={cn(
        "h-5 w-5",
        theme === "colored" ? "text-pet-food" : "text-white"
      )} /> :
      <Utensils className={cn(
        "h-5 w-5",
        theme === "colored" ? "text-pet-food" : "text-white"
      )} />;
  };

  // Calculate recommended cups per day based on calories and active foods
  const calculateFoodPortions = () => {
    if(activeFoods.length === 0) return [];

    // Adjust calories based on dietAdjustment
    const adjustedCalories = dailyCalories * (1 + dietAdjustment / 100);

    // Distribute calories evenly among active foods
    const caloriesPerFood = adjustedCalories / activeFoods.length;

    return activeFoods.map(food => {
      const cupsPerDay = caloriesPerFood / food.caloriesPerCup;
      return {
        ...food,
        cupsPerDay: parseFloat(cupsPerDay.toFixed(2))
      };
    });
  };

  const foodPortions = calculateFoodPortions();

  return (
    <>
      {/* <Card className="border-0 bg-transparent shadow-none w-full"> */}
      {/* <CardContent className="p-5"> */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold flex items-center">
          {t.diet?.todaysDiet || "Today's Diet"}
        </h2>
        <Utensils className={cn(
          "h-5 w-5 mr-2 text-primary"
        )} />
        {dietAdjustment !== 0 && (
          <div className={`px-2 py-1 text-xs rounded-full ${dietAdjustment < 0 ? 'bg-green-100 text-green-800' : 'bg-amber-100 text-amber-800'}`}>
            {dietAdjustment > 0 ? `+${dietAdjustment}%` : `${dietAdjustment}%`}
          </div>
        )}
      </div>

      {activeFoods.length === 0 ? (
        <div className="text-center py-1">
          {/* <Dog className={cn(
            "h-8 w-8 mx-auto mb-2",
            theme === "colored" ? "text-pet-food" : "text-gray-400"
          )} /> */}
          <p className="text-sm font-medium">{t.diet?.noPreferredFoods || "No preferred foods set"}</p>
          <p className="text-xs text-gray-500 mt-1">
            {t.diet?.addFoodsPrompt || "Add foods in your dog's profile to see daily feeding recommendations"}
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {foodPortions.map((food) => (
            <div key={food.id} className="flex items-center justify-between bg-white/50 p-3 rounded-lg">
              <div className="flex items-center gap-2">
                <div className={cn(
                  "p-2 rounded-full",
                  theme === "colored" ? "bg-white/20" : "bg-pet-purple"
                )}>
                  {getFoodIcon(food.icon)}
                </div>
                <div>
                  <p className="font-medium">{food.name}</p>
                  <p className="text-xs text-gray-500">{food.brand}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-bold">{food.cupsPerDay} {t.diet?.cups || "cups"}</p>
                <p className="text-xs text-gray-500">
                  {Math.round(food.cupsPerDay * food.caloriesPerCup)} {t.diet?.kcal || "kcal"}
                </p>
              </div>
            </div>
          ))}

          <div className="pt-2 text-sm text-gray-700">
            <p className="flex justify-between">
              <span>{t.diet?.totalDailyCalories || "Total Daily Calories"}:</span>
              <span className="font-semibold">
                {Math.round(dailyCalories * (1 + dietAdjustment / 100))} {t.diet?.kcal || "kcal"}
                {dietAdjustment !== 0 && (
                  <span className="text-xs text-gray-500 ml-1">
                    ({t.diet?.adjusted || "adjusted"})
                  </span>
                )}
              </span>
            </p>
          </div>
        </div>
      )}
      {/* </CardContent> */}
      {/* </Card > */}
    </>
  );
};

export default TodayDiet;
