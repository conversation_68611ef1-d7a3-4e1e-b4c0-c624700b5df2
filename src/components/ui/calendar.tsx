
import * as React from "react"
import {ChevronLeft, ChevronRight} from "lucide-react"
import {DayPicker, DayPickerProps, UI} from "react-day-picker"

import {cn} from "@/lib/utils"
import {buttonVariants} from "@/components/ui/button"

/// https://daypicker.dev/docs/customization
// export type CalendarProps = React.ComponentProps<typeof DayPicker>;

function Calendar({
  captionLayout = "dropdown",
  navLayout = "around",
  numberOfMonths = 1,
  pagedNavigation = true,
  // onDayClick,
  showOutsideDays = true,
  showWeekNumber = false,
  className,
  classNames,
  ...props
}: DayPickerProps) {
  return (
    <div className="w-full" >
      <DayPicker
        animate
        mode="single"
        showOutsideDays={showOutsideDays}
        captionLayout={captionLayout}
        navLayout={navLayout}
        numberOfMonths={numberOfMonths}
        showWeekNumber={showWeekNumber}
        fixedWeeks={true}
        pagedNavigation={pagedNavigation}
        required={true}
        className={className}
        classNames={{
          // [UI.Chevron]: "rdp-chevron pl-2 pr-8",
          // [UI.DropdownRoot]: "rdp-dropdown_root w-full",
          // [UI.MonthCaption]: "rdp-month_caption flex justify-between",
          // [UI.Week]: "rdp-week w-full flex justify-between",
          // [UI.Weeks]: "rdp-weeks w-full flex flex-col items-center justify-between",
          // [UI.Weekdays]: "rdp-weekdays w-full flex flex-col items-center justify-between",
          [UI.NextMonthButton]: "rdp-button_next z-10",
          [UI.PreviousMonthButton]: "rdp-button_previous z-10",
          [UI.CaptionLabel]: "rdp-caption_label hidden",
          [UI.Day]: "rdp-day text-center py-2",
          [UI.Dropdown]: "rdp-dropdown bg-transparent mr-3 bg-white/5",
          [UI.Dropdowns]: "rdp-dropdowns flex justify-center mt-1 mb-3 pr-7 w-full pl-10",
          [UI.Nav]: "rdp-nav -mb-9 flex justify-between px-2",
          [UI.Month]: "rdp-month w-full",
          [UI.MonthGrid]: "rdp-month_grid w-full",
          [UI.Outside]: "rdp-outside text-muted-foreground",
          [UI.Selected]: "rdp-selected bg-secondary/90 text-white rounded-full",
          [UI.Weekday]: "rdp-weekday text-center",
          ...classNames,
        }}
        // components={{
        //   PreviousMonthButton: ({...props}) => <ChevronLeft className="h-4 w-4" {...props} />,
        //   NextMonthButton: ({...props}) => <ChevronRight className="h-4 w-4" {...props} />,
        // }}
        {...props}
      />
    </div>
  );
}
Calendar.displayName = "Calendar";

export {Calendar};
