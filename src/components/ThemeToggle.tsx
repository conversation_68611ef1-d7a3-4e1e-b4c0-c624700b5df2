
import {Moon, Sun, Palette} from "lucide-react"
import {But<PERSON>} from "@/components/ui/button"
import {useTheme} from "@/components/ThemeProvider"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {useLanguage} from "@/hooks/useLanguage"

interface ThemeToggleProps {
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  showLabel?: boolean;
}

export function ThemeToggle({
  variant = "ghost",
  size = "sm",
  showLabel = true,
}: ThemeToggleProps) {
  const {theme, setTheme} = useTheme();
  const {t} = useLanguage();

  const icons = {
    light: Sun,
    dark: Moon,
    colored: Palette
  };

  // Theme labels mapping
  const themeLabels = {
    light: t.settings?.theme === 'Light' ? t.settings.theme : 'Light',
    dark: t.settings?.theme === 'Dark' ? t.settings.theme : 'Dark',
    colored: t.settings?.theme === 'Colored' ? t.settings.theme : 'Colored'
  };

  const Icon = icons[theme];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={`group transition-all duration-300 ${theme === "light"
            ? "text-secondary"
            : theme === "dark"
              ? "text-pet-glow hover:text-white"
              : "text-white hover:text-white"
            }`}
        >
          {showLabel && (
            <span className="mr-2 transition-all">
              {themeLabels[theme] || theme}
            </span>
          )}
          <Icon className="h-4 w-4 transition-transform duration-300 group-hover:rotate-45" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setTheme("light")}>
          <Sun className="mr-2 h-4 w-4" />
          <span>{themeLabels.light}</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")}>
          <Moon className="mr-2 h-4 w-4" />
          <span>{themeLabels.dark}</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("colored")}>
          <Palette className="mr-2 h-4 w-4" />
          <span>{themeLabels.colored}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
