
import React from 'react'
import {format, parseISO, subDays} from 'date-fns'
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import {
  Line,
  LineChart,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  Bar,
  ComposedChart,
} from 'recharts';
import {Activity} from '@/data/types'

interface ActivityHistoryChartProps {
  activities: Activity[];
  timeframe: 'week' | 'month' | 'year';
}

const ActivityHistoryChart = ({activities, timeframe}: ActivityHistoryChartProps) => {
  // Generate date range based on timeframe
  const generateDateRange = () => {
    const today = new Date();
    let days;

    switch(timeframe) {
      case 'week':
        days = 7;
        break;
      case 'month':
        days = 30;
        break;
      case 'year':
        days = 365;
        break;
      default:
        days = 7;
    }

    // Generate an array of the last X days
    return Array.from({length: days}).map((_, i) => {
      const date = subDays(today, days - 1 - i);
      return {
        date,
        dateFormatted: format(date, 'yyyy-MM-dd'),
        display: format(date, timeframe === 'year' ? 'MMM' : 'MMM dd'),
      };
    });
  };

  // Prepare chart data
  const prepareChartData = () => {
    const dateRange = generateDateRange();

    // Group activities by date
    const activitiesByDate = activities.reduce((acc, activity) => {
      const dateKey = format(activity.date, 'yyyy-MM-dd');
      if(!acc[dateKey]) {
        acc[dateKey] = [];
      }
      acc[dateKey].push(activity);
      return acc;
    }, {} as Record<string, Activity[]>);

    // Map the date range to chart data points
    return dateRange.map(day => {
      const activitiesOnDay = activitiesByDate[day.dateFormatted] || [];
      const totalDuration = activitiesOnDay.reduce((sum, act) => sum + act.duration, 0);
      const totalCalories = activitiesOnDay.reduce((sum, act) => sum + act.calories, 0);

      return {
        date: day.display,
        minutes: totalDuration,
        calories: totalCalories,
        fullDate: day.date,
      };
    });
  };

  const chartData = prepareChartData();

  return (
    <ChartContainer
      className="h-[250px]"
      config={{
        minutes: {
          label: "Duration (min)",
          // color: "#9b87f5" // purple
          color: "#009ee3" // blue
        },
        calories: {
          label: "Calories",
          color: "#F97316"
        }
      }}
    >
      <ComposedChart data={chartData} margin={{top: 5, right: 20, bottom: 15, left: 0}}>
        <CartesianGrid strokeDasharray="3 3" vertical={false} />
        <XAxis
          dataKey="date"
          tick={{fontSize: 12}}
          tickLine={{stroke: "#e2e8f0"}}
          axisLine={{stroke: "#e2e8f0"}}
        />
        <YAxis
          yAxisId="left"
          tick={{fontSize: 12}}
          tickLine={{stroke: "#e2e8f0"}}
          axisLine={{stroke: "#e2e8f0"}}
        />
        <YAxis
          yAxisId="right"
          orientation="right"
          tick={{fontSize: 12}}
          tickLine={{stroke: "#e2e8f0"}}
          axisLine={{stroke: "#e2e8f0"}}
        />
        <Tooltip content={<ChartTooltipContent />} />
        <Legend />
        <Bar
          dataKey="minutes"
          yAxisId="left"
          fill="#009ee3"
          radius={[4, 4, 0, 0]}
          barSize={timeframe === 'week' ? 20 : 10}
          name="minutes"
        />
        <Line
          type="monotone"
          dataKey="calories"
          yAxisId="right"
          stroke="#F97316"
          strokeWidth={2}
          dot={{stroke: "#F97316", strokeWidth: 2, r: 3, fill: "#ffffff"}}
          activeDot={{r: 5}}
          name="calories"
        />
      </ComposedChart>
    </ChartContainer>
  );
};

export default ActivityHistoryChart;
