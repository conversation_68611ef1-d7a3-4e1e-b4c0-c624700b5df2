
import React from 'react'
import {DogB<PERSON>} from '@/data/dogBreeds'
import {cn} from '@/lib/utils'
import {Check} from 'lucide-react'

interface BreedItemProps {
  breed: DogBreed;
  isSelected: boolean;
  onClick: () => void;
  displayName?: string;
}

export function BreedItem({
  breed,
  isSelected,
  onClick,
  displayName
}: BreedItemProps) {
  return (
    <div
      className={cn(
        "flex items-center gap-2 p-2 cursor-pointer hover:bg-gray-100 transition-colors rounded-xl",
        isSelected && "bg-gray-100"
      )}
      onClick={onClick}
    >
      <div className="w-8 h-8 rounded-full overflow-hidden shrink-0 shadow-xs">
        <img
          src={breed.image}
          alt={breed.name}
          className="w-full h-full object-cover"
        />
      </div>
      <div className="flex-1 min-w-0">
        <p className="truncate text-sm">{displayName || breed.name}</p>
      </div>
      {isSelected && <Check className="w-4 h-4 text-pet-purple" />}
    </div>
  );
}
