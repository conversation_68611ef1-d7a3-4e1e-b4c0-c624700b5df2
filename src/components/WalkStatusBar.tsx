
import React, {useEffect, useState} from 'react'
import {cn} from '@/lib/utils'
import {Progress} from '@/components/ui/progress'
import {useWalkTracker} from '@/hooks/useWalkTracker'

interface WalkStatusBarProps {
  isActive: boolean;
}

const WalkStatusBar = ({isActive}: WalkStatusBarProps) => {
  const color1 = "bg-secondary/60";
  const color2 = "bg-secondary/90";
  const [color, setColor] = React.useState(color1);

  // Create a subtle animation effect for the progress bar
  useEffect(() => {
    const interval = setInterval(() => {
      setColor((prev) => {
        return prev === color1 ? color2 : color1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className={`fixed top-3.5 left-9 right-0 z-9999 w-20 h-6 rounded-lg ${color} transition-colors duration-1000 `} />
    // <div className="fixed top-0 left-0 right-0 w-full z-9999 flex flex-col items-center">
    //   <div className="ml-10 mt-4 w-10 h-4 rounded-lg bg-secondary/60">
    //     {/* <div className="w-full h-4"> */}
    //     {/* <Progress
    //       value={progress}
    //       className={cn(
    //         "h-10 w-full transition-colors duration-300 rounded-none",
    //         "bg-secondary/60",
    //         "[&>div]:bg-secondary/70"
    //       )}
    //     /> */}
    //   </div>
    //   {/* <div className={cn(
    //     "text-xs font-mono font-bold px-2 py-0.5 rounded-b-md",
    //     isActive ? "bg-pet-acidGreen text-black" : "bg-blue-500 text-white"
    //   )}>
    //     {formatTime(displayDuration)}
    //   </div> */}
    // </div>
  );
};

export default WalkStatusBar;
