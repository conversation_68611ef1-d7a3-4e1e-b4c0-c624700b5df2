import React from 'react';

// Icon type definition
interface IconProps extends React.SVGProps<SVGSVGElement> {
  size?: number;
  className?: string;
}

// Dog paw icon
export const PawIcon: React.FC<IconProps> = ({ 
  size = 24, 
  className = "", 
  ...props 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      {...props}
    >
      <path d="M8.5 2a2.5 2.5 0 0 1 2.45 2.97A2.5 2.5 0 0 1 13.5 7h.5a2.5 2.5 0 0 1 2.5 2.5 2.5 2.5 0 0 1 2.5 2.5c0 .94-.52 1.75-1.29 2.17.06.42.09.85.09 1.28a7.5 7.5 0 0 1-15 0c0-.43.03-.86.09-1.28A2.5 2.5 0 0 1 2 12a2.5 2.5 0 0 1 2.5-2.5 2.5 2.5 0 0 1 2.5-2.5h.5a2.5 2.5 0 0 1 2.5-2.97V4" />
      <path d="M12 18.52A7.5 7.5 0 0 1 4.5 12v-1.5a2.5 2.5 0 0 1 5 0V12c0 4.14 3.36 7.5 7.5 7.5h1.5a2.5 2.5 0 0 0 0-5H16" />
    </svg>
  );
};

// Dog bowl icon
export const DogBowlIcon: React.FC<IconProps> = ({ 
  size = 24, 
  className = "", 
  ...props 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      {...props}
    >
      <path d="M10 14a3.5 3.5 0 0 0 5 0" />
      <path d="M18 12.3V9a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v3.3a8 8 0 0 0 12 0Z" />
      <path d="M3 3h18v2a8 8 0 0 1-8 8h-2a8 8 0 0 1-8-8V3Z" />
    </svg>
  );
};

// Dog treat icon
export const DogTreatIcon: React.FC<IconProps> = ({ 
  size = 24, 
  className = "", 
  ...props 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      {...props}
    >
      <path d="M9 11.5 5 15.5l3.5 3.5L12 15" />
      <path d="m14 6 4-4 6 6-4 4" />
      <path d="M12.5 9.5 9 6l4-4 6 6-2.5 2.5" />
      <path d="m7 14 3-3" />
      <path d="M14 14.5 9 19.5l-5-5L8.5 10" />
    </svg>
  );
};

// Walk icon
export const WalkIcon: React.FC<IconProps> = ({ 
  size = 24, 
  className = "", 
  ...props 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      {...props}
    >
      <path d="m13 4 3 8 4-3" />
      <path d="m13 4-4 1" />
      <path d="m13 4-2.5 6" />
      <path d="M7 17c.9-3 3-5 6.3-5.1" />
      <path d="m16 16-3.9 1.5" />
      <path d="m12 4 1.5-2" />
      <circle cx="17" cy="4" r="1" />
      <path d="M20 8c-1.4 0-2.8-.5-4-1.5" />
      <path d="M4 22c1-3 2.5-5.5 5.5-5.5 1.5 0 3 .5 4.5 2" />
      <path d="M14 22c-.5-1.5-1-3-2-4" />
    </svg>
  );
};

// Leash icon
export const LeashIcon: React.FC<IconProps> = ({ 
  size = 24, 
  className = "", 
  ...props 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      {...props}
    >
      <circle cx="5" cy="5" r="3" />
      <path d="M6 8v12a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V9.5a3.5 3.5 0 0 0-7 0V12" />
      <path d="M5 10V8a3 3 0 0 1 6 0v1" />
    </svg>
  );
};

// Dog house icon
export const DogHouseIcon: React.FC<IconProps> = ({ 
  size = 24, 
  className = "", 
  ...props 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      {...props}
    >
      <path d="M3 21h18" />
      <path d="M4 16h16" />
      <path d="m12 3-9 9h18l-9-9Z" />
      <path d="M9 21v-5a3 3 0 0 1 6 0v5" />
    </svg>
  );
};

// Dog food icon
export const DogFoodIcon: React.FC<IconProps> = ({ 
  size = 24, 
  className = "", 
  ...props 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      {...props}
    >
      <path d="M6 12a6 6 0 0 0 12 0c0-4-3.5-7-6-8.5C9.5 5 6 8 6 12Z" />
      <path d="M6 20a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v1H6Z" />
      <path d="M12 10v4" />
      <path d="M10 12h4" />
    </svg>
  );
};

// Bone icon
export const BoneIcon: React.FC<IconProps> = ({ 
  size = 24, 
  className = "", 
  ...props 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      {...props}
    >
      <path d="M19 3c.6.1 1 .5 1.4.9.4.4.7.9.6 1.6-.1.5-.3.9-.6 1.2l-.7.7c-.9.9-.9 2.3 0 3.2l.7.7c.3.3.5.7.6 1.2.1.6-.2 1.1-.6 1.6-.4.4-.9.8-1.4.9-1.1.2-2.2-.4-2.7-1.4-.2-.5-.7-.9-1.2-.9-.6 0-1.1.4-1.3.9-.5 1-1.6 1.6-2.8 1.4-.6-.1-1-.5-1.4-.9-.4-.4-.7-.9-.6-1.6.1-.5.3-.9.6-1.2l.7-.7c.9-.9.9-2.3 0-3.2l-.7-.7c-.3-.3-.5-.7-.6-1.2-.1-.6.2-1.1.6-1.6.4-.4.9-.8 1.4-.9 1.1-.2 2.2.4 2.7 1.4.2.5.7.9 1.2.9.6 0 1.1-.4 1.3-.9.5-1 1.7-1.6 2.8-1.4Z" />
    </svg>
  );
};

// Veterinarian icon
export const VetIcon: React.FC<IconProps> = ({ 
  size = 24, 
  className = "", 
  ...props 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      {...props}
    >
      <path d="M8 7H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-4" />
      <path d="M8 7V3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1v4" />
      <path d="M10 15h4" />
      <path d="M12 13v4" />
    </svg>
  );
};

// Dog bath icon
export const DogBathIcon: React.FC<IconProps> = ({ 
  size = 24, 
  className = "", 
  ...props 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      {...props}
    >
      <path d="M4 12h16a1 1 0 0 1 1 1v2a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4v-2a1 1 0 0 1 1-1Z" />
      <path d="M6 12V5a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v7" />
      <path d="M10 5v7" />
      <path d="M14 5v7" />
      <path d="M6 19v2" />
      <path d="M18 19v2" />
    </svg>
  );
};

// Dog training icon
export const DogTrainingIcon: React.FC<IconProps> = ({ 
  size = 24, 
  className = "", 
  ...props 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      {...props}
    >
      <circle cx="12" cy="5" r="3" />
      <path d="M12 8v11" />
      <path d="M8 14h8" />
      <path d="M18 10c-.5 0-1.9-.5-2-2 0 0-2 1-2 3" />
      <path d="M6 10c.5 0 1.9-.5 2-2 0 0 2 1 2 3" />
    </svg>
  );
};