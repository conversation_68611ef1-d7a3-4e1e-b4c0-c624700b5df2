import React, {useState} from 'react'
import {useNavigate} from 'react-router-dom'
import {
  Camera,
  Dog,
  Image,
  ChevronRight
} from 'lucide-react';
import {useToast} from '@/hooks/use-toast'
import {Button} from '@/components/ui/button'
import {Calendar} from '@/components/ui/calendar'
import {Input} from '@/components/ui/input'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import {
  ToggleGroup,
  ToggleGroupItem,
} from "@/components/ui/toggle-group";
import {zodResolver} from '@hookform/resolvers/zod'
import {useForm} from 'react-hook-form'
import * as z from 'zod'
import {Pet} from '@/data/types'
import {useLocalData} from '@/hooks/useLocalData'
import {fileToBase64} from '@/utils/breed-recognition/fileUtils'
import BreedSelector from "@/components/BreedSelector"
import SizeSelector from "@/components/SizeSelector"


const activityTypes = [
  {value: 'walking', label: 'Walking'},
  {value: 'running', label: 'Running'},
  {value: 'mixed', label: 'Mixed (walking & running)'},
];

const formSchema = z.object({
  name: z.string().min(2, {message: "Name must be at least 2 characters."}),
  breed: z.string().optional(),
  birthday: z.date(),
  weight: z.coerce.number().min(1),
  size: z.enum(['small', 'medium', 'large']),
  bodyFitState: z.coerce.number().min(1).max(5),
  activityType: z.enum(['walking', 'running', 'mixed']).optional(),
  activityDuration: z.coerce.number().min(5).max(240).optional(),
});

interface OnboardingSectionProps {
  onComplete?: (petData: Omit<Pet, 'id'>) => void;
  isModal?: boolean;
}

const OnboardingSection = ({onComplete, isModal = false}: OnboardingSectionProps) => {
  const [photoUrl, setPhotoUrl] = useState('');
  const {addPet} = useLocalData();
  const {toast} = useToast();
  const navigate = useNavigate();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      breed: '',
      birthday: undefined,
      weight: 0,
      // Remove default values for fields requiring user input
    },
  });

  const handlePhotoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if(file) {
      const url = await fileToBase64(file);
      setPhotoUrl(url);
      if(!form.getValues('breed')) form.setValue('breed', '');
    }
  };

  // const validateForm = () => {
  //   const values = form.getValues();
  //   let isValid = true;

  //   if(!values.name) {
  //     form.setError('name', {message: "Name is required"});
  //     isValid = false;
  //   }

  //   if(!values.weight) {
  //     form.setError('weight', {message: "Weight is required"});
  //     isValid = false;
  //   }

  //   if(!values.size) {
  //     form.setError('size', {message: "Please select your dog's size"});
  //     isValid = false;
  //   }

  //   if(!values.bodyFitState) {
  //     form.setError('bodyFitState', {message: "Please select your dog's body condition"});
  //     isValid = false;
  //   }

  //   if(!values.activityType) {
  //     form.setError('activityType', {message: "Please select an activity type"});
  //     isValid = false;
  //   }

  //   if(!values.activityDuration) {
  //     form.setError('activityDuration', {message: "Activity duration is required"});
  //     isValid = false;
  //   }

  //   if(!isValid) {
  //     toast({
  //       title: "Missing information",
  //       description: "Please fill in all required fields to continue.",
  //       variant: "destructive",
  //     });
  //   }

  //   return isValid;
  // };

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    // if(!validateForm()) return;

    console.log(values);

    // Convert form data to Pet object
    const petData: Omit<Pet, 'id'> = {
      name: values.name,
      type: 'dog',
      breed: values.breed,
      birthday: values.birthday,
      weight: values.weight,
      image: photoUrl || undefined,
      size: values.size,
      bodyFitState: values.bodyFitState,
      dailyCalorieGoal: 0, // Default values that can be calculated later
      dailyActivityGoal: values.activityDuration || 30,
      activityProgress: 0,
      calorieProgress: 0,
      typicalActivity: {
        duration: values.activityDuration || 30,
        type: values.activityType || 'walking',
      }
    };
    const newPet = addPet(petData);

    // Set onboarding as completed
    // localStorage.setItem('onboardingCompleted', 'true');

    // Call the onComplete callback if provided, otherwise redirect
    toast({
      title: "Profile created!",
      description: `We've created ${values.name}'s profile. Let's start tracking their fitness!`,
    });
    // if(onComplete) {
    //   onComplete(petData);
    // } else {
    // }
    navigate(`/results?petId=${newPet.id}`);
  };

  // Helper functions for body condition descriptions
  const getBodyConditionDescription = (score: number): string => {
    const descriptions = [
      "Ribs, spine, and hip bones are easily visible. Fat can't be seen or felt. Obvious loss of muscle mass. Extreme waist and abdominal tuck.",
      "Ribs, spine and hip bones are easy to feel and visible. Fat can be seen or felt under the skin, especially around the ribs. Some muscle loss. Obvious waist and abdominal tuck.",
      "Ribs, spine and hip bones are easily felt but not visible (coat may interfere with observation). Ribs and abdomen are seen when viewed from above and sides. Fat can be felt around ribs, spine and hip bones.",
      "Ribs, spine and hip bones are difficult to feel. Excess fat around ribs, spine and hip bones. Waist and abdominal tuck barely visible.",
      "Ribs, spine and hip bones are difficult to feel under a thick layer of fat. Waist not visible. Distended abdomen viewed from above and sides. Prominent fat deposits over lower spine, neck and chest."
    ];
    return descriptions[score - 1];
  };

  // Helper function to get the color for body condition state
  const getBodyConditionColor = (score: number): string => {
    const colors = [
      "from-green-300 to-green-400", // Very Thin - green
      "from-emerald-300 to-emerald-400", // Thin - emerald
      "from-cyan-400 to-blue-500", // Ideal - blue
      "from-yellow-300 to-yellow-500", // Overweight - yellow
      "from-red-300 to-red-500", // Obesity - red
    ];
    return colors[score - 1];
  };

  // Helper function to get BCS range for body condition state
  const getBCSRange = (score: number): string => {
    const ranges = ["1-2", "3", "4-5", "6-7", "8-9"];
    return ranges[score - 1];
  };

  // Current selected body condition
  const selectedBodyCondition = form.watch('bodyFitState');

  return (
    <div className={isModal ? "" : "pb-20 px-4 pt-3"}>
      {isModal && (
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-black">Welcome to My Dog In Fit</h1>
          <p className="text-gray-600">Let's set up your dog's profile</p>
        </div>
      )}

      <div className="text-xs text-gray-500 mb-4">
        Fields marked with <span className="text-red-500">*</span> are required
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="space-y-8">
            {/* Basic Information Section */}
            <div className="space-y-4">
              <h2 className="text-xl font-bold flex items-center">
                <Dog className="mr-2 text-primary" />
                Basic Information
              </h2>

              <FormField
                control={form.control}
                name="name"
                render={({field}) => (
                  <FormItem>
                    <FormLabel>Dog's Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Buddy" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="mt-6">
                <label className="block text-sm font-medium mb-2">Dog's Photo</label>
                <div className="flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-6 bg-gray-50">
                  {photoUrl ? (
                    <div className="text-center">
                      <img
                        src={photoUrl}
                        alt="Dog preview"
                        className="mx-auto h-40 w-40 object-cover rounded-lg mb-2"
                        onError={() => console.error("Error loading preview image")}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setPhotoUrl('');
                          form.setValue('breed', '');
                        }}
                      >
                        Change Photo
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center">
                      <Camera className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="mt-2 flex text-sm text-gray-600">
                        <label
                          htmlFor="file-upload"
                          className="relative cursor-pointer rounded-md font-medium text-primary hover:text-indigo-500"
                        >
                          <span>Upload a photo</span>
                          <input
                            id="file-upload"
                            name="file-upload"
                            type="file"
                            className="sr-only"
                            accept="image/*"
                            onChange={handlePhotoUpload}
                          />
                        </label>
                        <p className="pl-1">or drag and drop</p>
                      </div>
                      <p className="text-xs text-gray-500">
                        PNG, JPG, GIF up to 10MB
                      </p>
                    </div>
                  )}
                </div>
              </div>

              <BreedSelector
                control={form.control}
                name="breed"
                photoUrl={photoUrl}
              />

              <FormField
                control={form.control}
                name="birthday"
                render={({field}) => (
                  <FormItem>
                    <FormLabel>Birthday</FormLabel>
                    <FormControl>
                      {/* <Input
                        type="number"
                        step="0.1"
                        placeholder="3.5"
                        {...field}
                        value={field.value || ''} // Ensure value is never undefined
                        onChange={(e) => {
                          const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                          field.onChange(value);
                        }}
                      /> */}
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        className="rounded-md border shadow-xs"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Physical Information Section */}
            <div className="space-y-4">
              <h2 className="text-xl font-bold flex items-center border-t pt-8">
                <Image className="mr-2 text-primary" />
                Physical Information
              </h2>

              <FormField
                control={form.control}
                name="weight"
                render={({field}) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Weight (kg)
                      <Sheet>
                        <SheetTrigger asChild>
                          <Button variant="ghost" size="sm" className="ml-2 h-6 w-6 rounded-full p-0">
                            <span className="sr-only">Open weight measurement guide</span>
                            <Image className="h-4 w-4" />
                          </Button>
                        </SheetTrigger>
                        <SheetContent>
                          <SheetHeader>
                            <SheetTitle>How to weigh your dog at home</SheetTitle>
                            <SheetDescription>
                              Follow these simple steps to accurately measure your dog's weight
                            </SheetDescription>
                          </SheetHeader>
                          <div className="space-y-4 mt-6">
                            <p>1. Weigh yourself on a bathroom scale and note down the weight</p>
                            <p>2. Pick up your dog and weigh yourself while holding them</p>
                            <p>3. Subtract your weight from the combined weight</p>
                            <p>4. For large dogs, weigh yourself holding your dog partially, supporting some weight on the ground, then estimate total weight</p>
                            <img
                              src="https://images.unsplash.com/photo-1466721591366-2d5fba72006d"
                              alt="Dog weighing illustration"
                              className="rounded-md mt-4"
                            />
                          </div>
                        </SheetContent>
                      </Sheet>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.1"
                        placeholder="12.5"
                        {...field}
                        value={field.value || ''} // Ensure value is never undefined
                        onChange={(e) => {
                          const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                          field.onChange(value);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <SizeSelector
                control={form.control}
                name="size"
              />
            </div>

            {/* Body Condition Section */}
            <div className="space-y-4">
              <h2 className="text-xl font-bold flex items-center border-t pt-8">
                <Image className="mr-2 text-primary" />
                Body Condition Score (BCS)
              </h2>

              <FormField
                control={form.control}
                name="bodyFitState"
                render={({field}) => (
                  <FormItem>
                    <FormLabel>Body Condition <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <ToggleGroup
                        type="single"
                        value={field.value?.toString() || ""}
                        onValueChange={(value) => {
                          if(value) field.onChange(parseInt(value));
                        }}
                        className="flex flex-wrap justify-between gap-y-2"
                      >
                        {/* Very Thin - BCS 1-2 */}
                        <ToggleGroupItem
                          value="1"
                          aria-label="Very Thin"
                          className="flex flex-col items-center w-[19%] h-24 rounded-md transition-all"
                        >
                          <div className="flex-1 flex items-center justify-center mt-1">
                            <svg viewBox="0 0 100 30" className="w-16 h-6">
                              <path
                                d="M10,15 C15,5 30,8 40,15 C50,18 70,18 80,15 C85,10 90,15 95,20 C90,25 85,25 80,18 C70,15 50,20 40,17 C30,15 15,25 10,15 Z"
                                stroke="currentColor"
                                strokeWidth="1"
                                fill="none"
                                className={field.value === 1 ? `bg-linear-to-r ${getBodyConditionColor(1)} text-white` : "text-gray-600"}
                              />
                            </svg>
                          </div>
                          <span className="mt-1 text-xs font-semibold">Very Thin</span>
                          <span className="text-[10px] text-gray-600">BCS {getBCSRange(1)}</span>
                        </ToggleGroupItem>

                        {/* Thin - BCS 3 */}
                        <ToggleGroupItem
                          value="2"
                          aria-label="Thin"
                          className="flex flex-col items-center w-[19%] h-24 rounded-md transition-all"
                        >
                          <div className="flex-1 flex items-center justify-center mt-1">
                            <svg viewBox="0 0 100 30" className="w-16 h-6">
                              <path
                                d="M10,15 C15,8 30,10 40,15 C50,17 70,17 80,15 C85,12 90,15 95,20 C90,22 85,22 80,18 C70,15 50,18 40,17 C30,15 15,22 10,15 Z"
                                stroke="currentColor"
                                strokeWidth="1"
                                fill="none"
                                className={field.value === 2 ? `bg-linear-to-r ${getBodyConditionColor(2)} text-white` : "text-gray-600"}
                              />
                            </svg>
                          </div>
                          <span className="mt-1 text-xs font-semibold">Thin</span>
                          <span className="text-[10px] text-gray-600">BCS {getBCSRange(2)}</span>
                        </ToggleGroupItem>

                        {/* Ideal - BCS 4-5 */}
                        <ToggleGroupItem
                          value="3"
                          aria-label="Ideal"
                          className="flex flex-col items-center w-[19%] h-24 rounded-md transition-all"
                        >
                          <div className="flex-1 flex items-center justify-center mt-1">
                            <svg viewBox="0 0 100 30" className="w-16 h-6">
                              <path
                                d="M10,15 C15,10 30,12 40,15 C50,16 70,16 80,15 C85,13 90,15 95,18 C90,20 85,20 80,17 C70,15 50,16 40,16 C30,15 15,20 10,15 Z"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                fill="none"
                                className={field.value === 3 ? `bg-linear-to-r ${getBodyConditionColor(3)} text-white` : "text-gray-600"}
                              />
                            </svg>
                          </div>
                          <span className="mt-1 text-xs font-semibold">Ideal</span>
                          <span className="text-[10px] text-gray-600">BCS {getBCSRange(3)}</span>
                        </ToggleGroupItem>

                        {/* Overweight - BCS 6-7 */}
                        <ToggleGroupItem
                          value="4"
                          aria-label="Overweight"
                          className="flex flex-col items-center w-[19%] h-24 rounded-md transition-all"
                        >
                          <div className="flex-1 flex items-center justify-center mt-1">
                            <svg viewBox="0 0 100 30" className="w-16 h-6">
                              <path
                                d="M10,15 C15,12 30,14 40,15 C50,15 70,15 80,15 C85,14 90,15 95,16 C90,17 85,17 80,16 C70,15 50,15 40,15 C30,15 15,18 10,15 Z"
                                stroke="currentColor"
                                strokeWidth="1.75"
                                fill="none"
                                className={field.value === 4 ? `bg-linear-to-r ${getBodyConditionColor(4)} text-white` : "text-gray-600"}
                              />
                            </svg>
                          </div>
                          <span className="mt-1 text-xs font-semibold">Overweight</span>
                          <span className="text-[10px] text-gray-600">BCS {getBCSRange(4)}</span>
                        </ToggleGroupItem>

                        {/* Obesity - BCS 8-9 */}
                        <ToggleGroupItem
                          value="5"
                          aria-label="Obesity"
                          className="flex flex-col items-center w-[19%] h-24 rounded-md transition-all"
                        >
                          <div className="flex-1 flex items-center justify-center mt-1">
                            <svg viewBox="0 0 100 30" className="w-16 h-6">
                              <path
                                d="M10,15 C15,13 30,14 40,15 C50,15 70,15 80,15 C85,15 90,15 95,15 C90,15 85,15 80,15 C70,15 50,15 40,15 C30,15 15,16 10,15 Z"
                                stroke="currentColor"
                                strokeWidth="2"
                                fill="none"
                                className={field.value === 5 ? `bg-linear-to-r ${getBodyConditionColor(5)} text-white` : "text-gray-600"}
                              />
                            </svg>
                          </div>
                          <span className="mt-1 text-xs font-semibold">Obesity</span>
                          <span className="text-[10px] text-gray-600">BCS {getBCSRange(5)}</span>
                        </ToggleGroupItem>
                      </ToggleGroup>
                    </FormControl>
                    {field.value && (
                      <p className="mt-2 text-xs text-gray-700">{getBodyConditionDescription(field.value)}</p>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Activity Information Section */}
            {/* <div className="space-y-4">
              <h2 className="text-xl font-bold flex items-center border-t pt-8">
                <Image className="mr-2 text-primary" />
                Average Activity
              </h2>

              <FormField
                control={form.control}
                name="activityType"
                render={({field}) => (
                  <FormItem>
                    <FormLabel>Activity Type <span className="text-red-500">*</span></FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || undefined}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select activity type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {activityTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="activityDuration"
                render={({field}) => (
                  <FormItem>
                    <FormLabel>Daily Duration (minutes) <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="30"
                        {...field}
                        value={field.value || ''}
                        onChange={(e) => {
                          const value = e.target.value === '' ? undefined : parseFloat(e.target.value);
                          field.onChange(value);
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      How many minutes per day does your dog typically exercise?
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div> */}
          </div>

          <Button
            type="submit"
            className="w-full bg-secondary/90 hover:bg-secondary text-white"
          >
            Done
            {/* <ChevronRight className="ml-2 h-4 w-4" /> */}
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default OnboardingSection;
