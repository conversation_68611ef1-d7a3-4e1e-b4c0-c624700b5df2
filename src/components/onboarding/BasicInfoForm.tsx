import React from 'react'
import {Calendar} from '@/components/ui/calendar'
import {But<PERSON>} from '@/components/ui/button'
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import {Input} from '@/components/ui/input'
import {BreedAutocomplete} from '@/components/BreedAutocomplete'
import {UseFormReturn} from 'react-hook-form'
import {z} from 'zod'
import ImageUploader from './ImageUploader'

// Define the schema here to keep it consistent across components
export const petFormSchema = z.object({
  name: z.string().min(1, {message: 'Name is required'}),
  breed: z.string().optional(),
  birthday: z.date().optional(),
  weight: z.coerce.number().min(0),
  size: z.enum(['small', 'medium', 'large']).default('medium'),
  bodyFitState: z.number().min(1).max(5).default(3)
});

export type PetFormData = z.infer<typeof petFormSchema>;

interface BasicInfoFormProps {
  form: UseFormReturn<PetFormData>;
  image?: string;
  setImage: (image?: string) => void;
  onNext: () => void;
}

const BasicInfoForm = ({form, image, setImage, onNext}: BasicInfoFormProps) => {
  return (
    <div className="space-y-6">
      <div className="text-xl font-semibold mb-4">Basic Information</div>

      <ImageUploader
        image={image}
        setImage={setImage}
        onBreedDetected={(breed) => form.setValue('breed', breed)}
      />

      <FormField
        control={form.control}
        name="name"
        render={({field}) => (
          <FormItem>
            <FormLabel>Name</FormLabel>
            <FormControl>
              <Input placeholder="Your dog's name" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="breed"
        render={({field}) => (
          <FormItem>
            <FormLabel>Breed</FormLabel>
            <FormControl>
              <BreedAutocomplete value={field.value} onChange={field.onChange} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="birthday"
        render={({field}) => (
          <FormItem className="flex flex-col space-y-3">
            <FormLabel>Birthday</FormLabel>
            <FormControl>
              <div className="relative">
                <Calendar
                  mode="single"
                  selected={field.value}
                  onSelect={field.onChange}
                  className="rounded-md border shadow-xs"
                />
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="flex justify-end">
        <Button
          type="button"
          onClick={() => {
            // Validate the current fields before proceeding
            const isNameValid = form.getValues('name').trim() !== '';
            if(isNameValid) {
              onNext();
            } else {
              form.setError('name', {
                type: 'manual',
                message: 'Name is required before proceeding'
              });
            }
          }}
        >
          Next
        </Button>
      </div>
    </div>
  );
};

export default BasicInfoForm;
