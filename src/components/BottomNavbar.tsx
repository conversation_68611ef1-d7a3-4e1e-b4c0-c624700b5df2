
import React, {useState, useEffect} from 'react'
import {Link, useLocation} from 'react-router-dom'
import {Home, Clock, Calendar, Settings, <PERSON>, Brain, Play, Pause, Square, StopCircle} from 'lucide-react'
import {cn} from '@/lib/utils'
import {useLanguage} from '@/hooks/useLanguage'
import {useTheme} from '@/components/ThemeProvider'
import {useLocalData} from '@/hooks/useLocalData'
import {useWalkTracker} from '@/hooks/useWalkTracker'
import {Button} from '@/components/ui/button'
import ActivityTypeModal from '@/components/ActivityTypeModal'
import WalkStatusBar from '@/components/WalkStatusBar'

const BottomNavbar = () => {
  // Always call all hooks at the top level
  const location = useLocation();
  const {t} = useLanguage();
  const {theme} = useTheme();
  const {pets} = useLocalData();

  const {
    isWalking,
    duration,
    startWalk,
    pauseWalk,
    continueWalk,
    endWalk,
    showActivityModal,
    setShowActivityModal,
    completeWalk,
    getCurrentDuration
  } = useWalkTracker();

  // Always initialize all state variables
  const [displayDuration, setDisplayDuration] = useState(0);

  // Update display duration in real-time
  useEffect(() => {
    // Initial update
    if(getCurrentDuration) {
      setDisplayDuration(getCurrentDuration());
    }

    // Only create interval if walk is active
    if(isWalking) {
      const interval = setInterval(() => {
        if(getCurrentDuration) {
          setDisplayDuration(getCurrentDuration());
        }
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [duration, isWalking, getCurrentDuration]);

  // Format duration as mm:ss
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  const handleActivitySelect = (type: string) => {
    if(completeWalk) {
      completeWalk(type);
    }
  };

  // Hide navbar if no pets
  if(pets.length === 0) {
    return null;
  }

  const currentPath = location.pathname;

  const getIconColor = (isActive: boolean) => {
    if(theme === "colored") {
      return isActive ? "text-white" : "text-gray-500";
    }
    return isActive ? "text-black" : "text-gray-400";
    // return isActive ? "text-pet-teal" : "text-gray-400";
    // return isActive ? "text-pet-blue" : "text-gray-400";
    // return isActive ? "text-pet-orange" : "text-gray-400";
  };

  const navItems = [
    {
      icon: Home,
      label: t.navigation?.home || 'Home',
      path: '/',
      colorClass: "icon-activity"
    },
    {
      icon: Dog,
      label: t.navigation?.profile || 'Profile',
      path: '/dog/',
      colorClass: "icon-dog"
    },
    {
      icon: null,
    },
    {
      icon: Brain,
      label: t.navigation?.ai || 'AI',
      path: '/ai',
      colorClass: "icon-food"
    },
    {
      icon: Settings,
      label: t.navigation?.settings || 'Settings',
      path: '/settings',
      colorClass: "icon-weight"
    }
  ];


  // Define routes where navbar should be shown
  const visibleNavbarRoutes = navItems.filter(item => item.icon).map(item => item.path);

  // Check if current path is in the visible routes list
  const shouldShowNavbar = visibleNavbarRoutes.some(route =>
    location.pathname === route ||
    (route.endsWith('/') && location.pathname.startsWith(route) &&
      location.pathname.length === route.length + 1)
  );

  // Hide navbar if no pets or not on a main navigation page
  if(pets.length === 0 || !shouldShowNavbar) {
    return null;
  }

  {/* Walk Button - positioned to intersect the navbar border */}
  const walkButton =
    <div className="absolute flex flex-col items-center bottom-3">
      <Button
        onClick={isWalking ? pauseWalk : duration > 0 ? continueWalk : startWalk}
        className={cn(
          "rounded-full flex items-center justify-center shadow-lg",
          isWalking
            ? "bg-pet-pink hover:bg-pet-pink/90"
            // : duration > 0
            // ? "bg-teal-400 hover:bg-teal-500"
            : "bg-secondary hover:bg-secondary/90",
          location.pathname === '/ai' || location.pathname === '/settings' ? "w-10 h-10 " : "w-14 h-14 "
        )}
      >
        {isWalking ? (
          <Square className="h-6 w-6 text-white" />
          // ) : duration > 0 ? (
          //   <Play className="h-6 w-6 text-black" />
        ) : (
          <Play className="h-6 w-6 text-white" />
        )}
      </Button>

      <span className="text-xs mt-2 font-bold">{formatDuration(displayDuration)}</span>
      {/* {!isWalking && duration > 0 && (
            <Button
              onClick={endWalk}
              variant="ghost"
              size="sm"
              className="absolute -right-10 -top-4 p-1 h-8 w-8 rounded-full bg-pet-red/75 hover:bg-pet-red/90"
            >
              <StopCircle className="h-5 w-5 text-white" />
            </Button>
          )} */}
    </div>

  return (
    <>
      {/* Status bar at the top of the screen */}
      {isWalking && (
        <WalkStatusBar isActive={isWalking} />
      )}

      <div className={cn(
        // "fixed bottom-0 left-0 right-0 px-4 pt-1 pb-3 flex justify-around items-end z-10 shadow-[0_-4px_10px_rgba(0,0,0,0.05)] transition-colors duration-300",
        "fixed bottom-0 left-0 right-0 px-4 pt-1 pb-3 flex justify-around items-end z-10 transition-colors duration-300",
        theme === "light" ? "bg-background/35 backdrop-blur-3xl" :
          // theme === "light" ? "backdrop-blur-3xl" :
          theme === "dark" ? "bg-card" :
            "bg-white/5 backdrop-blur-3xl"
      )}>
        {navItems.map((item) => {
          const isActive = currentPath === item.path;
          return item.icon ? (
            <Link
              key={item.path}
              to={item.path}
              className={cn(
                "flex flex-col items-center py-1 px-3 rounded-xl",
                isActive ? getIconColor(true) : getIconColor(false),
                theme === "colored" && "hover:bg-white/10",
                theme === "colored" && isActive && item.colorClass
              )}
            >
              <div
              // className={cn(
              //   isActive ? "animate-bounce-light bg-pet-acidGreen/30 rounded-full px-3 py-1" : "px-3 py-1",
              //   theme === "colored" && isActive && item.colorClass
              // )}
              >
                <item.icon
                  size={24}
                  className={cn(
                    isActive ? "animate-bounce-light" : "",
                    theme === "colored" && isActive && item.colorClass
                  )}
                />
              </div>
              <span className="text-xs mt-1">{item.label}</span>
            </Link>
          ) : <div className="w-24" key="none">
          </div>
        })}
        {walkButton}
      </div>

      {/* Activity Type Modal */}
      <ActivityTypeModal
        isOpen={showActivityModal}
        onClose={() => setShowActivityModal(false)}
        onSelect={handleActivitySelect}
      />
    </>
  );
};

export default BottomNavbar;
