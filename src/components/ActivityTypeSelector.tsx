
import React from 'react';
import { cn } from '@/lib/utils';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface ActivityType {
  value: string;
  label: string;
  description: string;
}

const activityTypes: ActivityType[] = [
  {
    value: 'walking',
    label: 'Walking',
    description: 'Casual walking pace',
  },
  {
    value: 'running',
    label: 'Running',
    description: 'Jogging or running',
  },
  {
    value: 'mixed',
    label: 'Mixed',
    description: 'Combination of activities',
  },
];

interface ActivityTypeSelectorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

const ActivityTypeSelector = ({ value, onChange, className }: ActivityTypeSelectorProps) => {
  return (
    <RadioGroup
      value={value}
      onValueChange={onChange}
      className={cn("grid grid-cols-3 gap-2", className)}
    >
      {activityTypes.map((type) => (
        <label
          key={type.value}
          className={cn(
            "flex flex-col items-center justify-between rounded-md border-2 border-muted p-3 hover:border-accent cursor-pointer",
            value === type.value ? "border-pet-purple bg-pet-lightPurple" : "border-gray-200"
          )}
        >
          <RadioGroupItem value={type.value} className="sr-only" />
          <div className="text-center">
            <span className="font-medium">{type.label}</span>
            <p className="text-xs text-gray-500 mt-1">{type.description}</p>
          </div>
        </label>
      ))}
    </RadioGroup>
  );
};

export default ActivityTypeSelector;
