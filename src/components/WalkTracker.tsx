
import React, {useState, useEffect} from 'react'
import {Play, Pause, Clock, Dumbbell} from 'lucide-react'
import {Button} from '@/components/ui/button'
import {Card, CardContent} from '@/components/ui/card'
import {useToast} from '@/hooks/use-toast'
import WalkStatusBar from '@/components/WalkStatusBar'
import ActivityTypeModal from '@/components/ActivityTypeModal'

interface WalkTrackerProps {
  onWalkComplete: (details: {
    duration: number;
    type: string;
    calories: number;
  }) => void;
}

const WalkTracker = ({onWalkComplete}: WalkTrackerProps) => {
  const [isWalking, setIsWalking] = useState(false);
  const [duration, setDuration] = useState(0);
  const [walkType, setWalkType] = useState<string>('mixed');
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [showActivityModal, setShowActivityModal] = useState(false);
  const {toast} = useToast();

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if(isWalking) {
      interval = setInterval(() => {
        setDuration(prev => prev + 1);
      }, 1000);
    }

    return () => {
      if(interval) clearInterval(interval);
    };
  }, [isWalking]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const calculateCalories = (minutes: number, type: string): number => {
    // Simple calorie estimation based on activity type
    const caloriesPerMinute = {
      walking: 3,
      running: 7,
      mixed: 5
    };

    return Math.round(minutes * (caloriesPerMinute[type as keyof typeof caloriesPerMinute] || 3));
  };

  const startWalk = () => {
    setIsWalking(true);
    setStartTime(new Date());
    toast({
      title: "Walk started",
      description: "We're tracking your dog's activity.",
    });
  };

  const pauseWalk = () => {
    setIsWalking(false);
    toast({
      title: "Walk paused",
      description: "You can resume tracking anytime.",
    });
  };

  const continueWalk = () => {
    setIsWalking(true);
    toast({
      title: "Walk continued",
      description: "We're tracking your dog's activity again.",
    });
  };

  const tryEndWalk = () => {
    setIsWalking(false);

    if(duration < 60) { // Less than a minute
      toast({
        title: "Walk too short",
        description: "Walks should be at least 1 minute long.",
        variant: "destructive",
      });
      setDuration(0);
      setStartTime(null);
      return;
    }

    // Show the activity type modal
    setShowActivityModal(true);
  };

  const confirmWalkEnd = () => {
    const minutes = Math.floor(duration / 60);
    const calories = calculateCalories(minutes, walkType);

    onWalkComplete({
      duration: minutes,
      type: walkType,
      calories
    });

    toast({
      title: "Walk completed!",
      description: `${minutes} minute ${walkType} walk added. Burned approx. ${calories} calories.`,
    });

    setDuration(0);
    setStartTime(null);
    setShowActivityModal(false);
  };

  return (
    <>
      {/* Status bar header - always maintain same height */}
      <div className="fixed top-0 left-0 right-0 z-50 h-1.5">
        {(isWalking || (duration > 0 && !isWalking)) ? (
          <WalkStatusBar isActive={isWalking} />
        ) : (
          <div className="h-1.5" /> /* Spacer to prevent layout jumps */
        )}
      </div>

      {/* <ActivityTypeModal
        open={showActivityModal}
        onOpenChange={setShowActivityModal}
        value={walkType}
        onChange={setWalkType}
        onConfirm={confirmWalkEnd}
      /> */}

      {/* <> */}
      {/* <h3 className="font-bold text-lg mb-4 flex items-center">
            <Dumbbell className="h-5 w-5 mr-2 text-pet-purple" />
            Track Walk
          </h3> */}

      {/* Show timer only when started or paused */}
      {(startTime || duration > 0) && (
        <div className="text-center mb-4">
          <div className="text-3xl font-mono font-bold mb-1">
            {formatTime(duration)}
          </div>
          <p className="text-sm text-gray-500 flex items-center justify-center">
            <Clock className="h-3 w-3 mr-1" />
            {startTime ? `Started at ${startTime.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'})}` : 'Not started'}
          </p>
        </div>
      )}

      <div className="flex space-x-2 justify-center">
        {!isWalking ? (
          <>
            {duration > 0 ? (
              <Button
                className="rounded-full flex-1 bg-teal-400 hover:bg-teal-500 text-md text-black"
                onClick={continueWalk}
              >
                <Play className="h-4 w-4 mr-2" />
                Continue Walk
              </Button>
            ) : (
              <Button
                // className="rounded-full flex-1  bg-green-500 hover:bg-green-600 text-white text-md max-w-56"
                className="rounded-full flex-1  bg-pet-acidGreen hover:bg-teal-500 text-black text-md max-w-56"
                onClick={startWalk}
              >
                <Play className="h-4 w-4 mr-2" />
                Start Walk
              </Button>
            )}

            {duration > 0 && (
              <Button
                // variant="destructive"
                className="flex-1 rounded-full bg-pet-pink/65 hover:bg-pet-pink/75 text-md text-black"
                onClick={tryEndWalk}
              >
                End Walk
              </Button>
            )}
          </>
        ) : (
          <>
            <Button
              variant="outline"
              className="rounded-full flex-1 bg-gray-100 text-md"
              onClick={pauseWalk}
            >
              <Pause className="h-4 w-4 mr-2" />
              Pause
            </Button>

            <Button
              // variant="destructive"
              className="rounded-full flex-1 bg-pet-red/75 hover:bg-pet-red/80 text-md text-black"
              onClick={tryEndWalk}
            >
              End Walk
            </Button>
          </>
        )}
      </div>
    </>
    // </>
  );
};

export default WalkTracker;
