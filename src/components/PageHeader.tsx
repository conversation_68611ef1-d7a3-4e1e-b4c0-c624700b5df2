
import React from 'react'
import {<PERSON>} from 'react-router-dom'
import {ArrowLeft} from 'lucide-react'

import {useTheme} from '@/components/ThemeProvider'
import {cn} from '@/lib/utils'
import {Button} from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import PetAvatar from '@/components/PetAvatar'
import {ChevronDown} from 'lucide-react'
import {useLocalData} from '@/hooks/useLocalData'
import {useNavigate} from 'react-router-dom'


export const PageHeaderProfile = () => {
  const {theme} = useTheme();
  const navigate = useNavigate();
  const {
    pets,
    selectPet,
    selectedPetId,
  } = useLocalData(); // Get pets and new functions from useLocalData hook
  const pet = pets.find(pet => pet.id === selectedPetId) || (pets.length > 0 ? pets[0] : null);

  const getAge = (birthday: Date) => {
    if(!(birthday instanceof Date)) return 'Birthday not set';

    const today = new Date();
    let years = today.getFullYear() - birthday.getFullYear();
    const m = today.getMonth() - birthday.getMonth();
    if(m < 0 || (m === 0 && today.getDate() < birthday.getDate())) {
      years--;
    }

    if(years === 0) {
      const months = today.getMonth() - birthday.getMonth();
      if(months === 0) {
        const days = today.getDate() - birthday.getDate();

        return `${days} day${days === 1 ? '' : 's'}`;
      }

      return `${months} month${months === 1 ? '' : 's'}`;
    }

    return `${years} year${years === 1 ? '' : 's'}`;
  };
  const age = pet && pet.birthday ? getAge(pet.birthday) : 0;

  // const getGradientClass = () => {
  //   if(gradientClass) {
  //     return gradientClass;
  //   }

  //   if(theme === "light") {
  //     return "bg-linear-to-b from-pet-purple to-pet-darkPurple";
  //   } else if(theme === "dark") {
  //     return "bg-linear-to-b from-pet-darkPurple to-pet-navy";
  //   } else if(theme === "colored") {
  //     return "bg-transparent"; // Make header transparent in colored theme
  //   }

  //   return "bg-linear-to-b from-pet-purple to-pet-darkPurple";
  // };

  const handlePetSelect = (petId: string) => {
    if(petId !== selectedPetId) selectPet(petId);
    // navigate(`/dog/${selectedPetId}`);
  };

  {/* Top section with avatar and profile switcher */}
  return (
    <div className="w-full sticky top-0 z-10 mb-3 pt-10 pb-2 px-6 backdrop-blur-3xl">
      <div className="flex justify-between items-center">
        {/* Left side: Pet avatar with name/breed/age to the right */}
        <div className="flex items-start gap-0">
          {/* Pet name and details to the right of avatar */}
          <div
            // className="cursor-pointer text-white pt-2"
            className="text-white pt-2"
          // onClick={handleViewDogProfile}
          >
            <p className="text-black/80 text-sm uppercase">
              {pet.breed || 'Unknown breed'}&nbsp; • &nbsp;{pet.birthday ? age : 'Birthday not set'}
            </p>
            {/* Right side: Pet selector dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white flex gap-0 items-center mt-3 mb-2 h-4 p-0"
                >
                  <div className="flex justify-center">
                    <h1 className="text-black/80 text-2xl font-bold">{pet.name}</h1>&nbsp;
                    {/* <Dog className="h-5 w-5" /> */}
                    <ChevronDown className="text-black/80 h-8 w-4 size-8 mt-2" />
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-white ml-3 min-w-72 pt-2 pb-3">
                {pets.sort((a, b) => a.name.localeCompare(b.name)).map(pet => (
                  <DropdownMenuItem
                    key={pet.id}
                    onClick={() => handlePetSelect(pet.id)}
                    className="flex gap-2 items-center cursor-pointer"
                  >
                    <PetAvatar name={pet.name} src={pet.image} size="xs" />
                    <span>{pet.name}</span>
                  </DropdownMenuItem>
                ))}
                <hr className="my-2" />
                <DropdownMenuItem
                  onClick={() => navigate('/add-dog')}
                  className="mt-2 pt-1 cursor-pointer"
                >
                  + Add another dog
                </DropdownMenuItem>
                {/* <DropdownMenuItem
                      onClick={handleViewDogProfile}
                      className="pt-1 cursor-pointer"
                    >
                      View Profile
                    </DropdownMenuItem> */}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        <PetAvatar
          name={pet.name}
          src={pet.image}
          size="sm"
          onClick={() => navigate('/dog/')}
          className="cursor-pointer"
        />
      </div>

      {/* Add some bottom padding to the header section */}
      {/* <div className="px-5 pb-5"></div> */}
    </div>
  );
};


interface PageHeaderProps {
  title: string;
  showBackButton?: boolean;
  backPath?: string;
  children?: React.ReactNode;
  gradientClass?: string;
}

export const PageHeader = ({
  title,
  showBackButton = true,
  backPath = "/",
  children,
  gradientClass
}: PageHeaderProps) => {
  const {theme} = useTheme();

  const getGradientClass = () => {
    if(gradientClass) {
      return gradientClass;
    }

    if(theme === "light") {
      return "bg-linear-to-b from-pet-purple to-pet-darkPurple";
    } else if(theme === "dark") {
      return "bg-linear-to-b from-pet-darkPurple to-pet-navy";
    } else if(theme === "colored") {
      return "bg-transparent"; // Make header transparent in colored theme
    }

    return "bg-linear-to-b from-pet-purple to-pet-darkPurple";
  };

  return (
    <div className={cn(
      "sticky top-0 z-10 px-6 pt-12 pb-4 backdrop-blur-3xl",
      // getGradientClass()
    )}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {showBackButton && (
            <Link to={backPath} className="rounded-full p-2 bg-black hover:bg-black/90 transition-colors">
              <ArrowLeft className="h-5 w-5" />
            </Link>
          )}
          <h1 className={cn(
            "text-xl font-bold",
            showBackButton ? "ml-3" : "",
          )}>{title}</h1>
        </div>

        {children}
      </div>
    </div>
  );
};
