
import React from 'react';
import { Link } from 'react-router-dom';
import { Heart, PlusCircle, ChevronRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import DogList from '@/components/DogList';

const PetProfilesSection = () => {
  return (
    <Card className="mb-6">
      <CardContent className="p-5">
        <div className="flex items-center mb-4">
          <div className="bg-pet-pink p-2 rounded-full">
            <Heart className="h-5 w-5 text-pet-purple" />
          </div>
          <h2 className="text-lg font-bold ml-3">Pet Profiles</h2>
        </div>
        
        <div className="space-y-4">
          {/* Pet List with drag and drop */}
          <DogList />
          
          <Separator />
          
          <Link to="/add-dog" className="flex justify-between items-center">
            <div className="flex items-center">
              <PlusCircle className="h-4 w-4 text-pet-purple mr-2" />
              <p className="font-medium">Add New Pet</p>
            </div>
            <ChevronRight className="h-4 w-4 text-gray-400" />
          </Link>
          
          <Separator />
          
          <div className="flex justify-between items-center">
            <p className="font-medium">Breed Information</p>
            <Button variant="ghost" size="sm" className="text-pet-purple">View</Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PetProfilesSection;
