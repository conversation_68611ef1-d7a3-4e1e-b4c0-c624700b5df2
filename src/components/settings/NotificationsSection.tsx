
import React from 'react'
import {BellRing} from 'lucide-react'
import {Card, CardContent} from '@/components/ui/card'
import {Switch} from '@/components/ui/switch'
import {Separator} from '@/components/ui/separator'
import {UserProfile} from '@/data/types'
import NotificationPermission from '@/components/settings/NotificationPermission'

interface NotificationsSectionProps {
  userProfile: UserProfile | null;
  onNotificationToggle: (enabled: boolean) => void;
}

const NotificationsSection = ({userProfile, onNotificationToggle}: NotificationsSectionProps) => {
  return (
    <Card className="mb-10 shadow-none">
      <CardContent className="p-0">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-bold">Notifications</h2>
          {/* <div className="bg-pet-yellow p-2 rounded-full"> */}
          <BellRing className="h-5 w-5 text-primary" />
          {/* </div> */}
        </div>

        {/* Add the notification permission component */}
        <NotificationPermission />

        <h3 className="font-medium">Other Notifications</h3>
        <div className="mt-6 space-y-4">
          <div className="flex justify-between items-center">
            <p>Activity Reminders</p>
            <Switch
              defaultChecked={userProfile?.notificationsEnabled}
              onCheckedChange={(checked) => onNotificationToggle(checked)}
            />
          </div>

          <Separator />

          <div className="flex justify-between items-center">
            <p>Feeding Reminders</p>
            <Switch defaultChecked={userProfile?.notificationsEnabled} />
          </div>

          <Separator />

          <div className="flex justify-between items-center">
            <p>Weight Updates</p>
            <Switch defaultChecked={false} />
          </div>

          <Separator />

          <div className="flex justify-between items-center">
            <p>Health Alerts</p>
            <Switch defaultChecked={userProfile?.notificationsEnabled} />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default NotificationsSection;
