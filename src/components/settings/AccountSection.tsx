
import React from 'react'
import {User, UserCircle, LogOut, LogIn, Shield} from 'lucide-react'
import {Card, CardContent} from '@/components/ui/card'
import {Button} from '@/components/ui/button'
import {Separator} from '@/components/ui/separator'
import EmailVerificationStatus from './EmailVerificationStatus'

interface AccountSectionProps {
  user: any | null;
  isAuthenticated: boolean;
  onLogoutClick: () => void;
  onLoginClick: () => void;
}

const AccountSection = ({user, isAuthenticated, onLogoutClick, onLoginClick}: AccountSectionProps) => {
  return (
    <Card className="m-0 mb-10 shadow-none">
      <CardContent className="p-0">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-bold">Account</h2>
          <User className="h-5 w-5 text-primary" />
        </div>

        <div className="space-y-4">
          {isAuthenticated ? (
            <>
              <div className="flex items-center">
                <UserCircle className="h-6 w-6 mr-2 text-pet-purple" />
                <div>
                  <p className="font-medium">{user?.email}</p>
                  <EmailVerificationStatus user={user} />
                </div>
              </div>

              <Separator />

              <div className="flex justify-between items-center">
                <p className="font-medium">Profile Information</p>
                <Button variant="ghost" size="sm" className="text-pet-purple">Edit</Button>
              </div>

              <Separator />

              <div className="flex justify-between items-center">
                <p className="font-medium">Password</p>
                <Button variant="ghost" size="sm" className="text-pet-purple">Change</Button>
              </div>

              <Separator />

              <div className="flex justify-between items-center">
                <p className="font-medium">Connected Devices</p>
                <Button variant="ghost" size="sm" className="text-pet-purple">Manage</Button>
              </div>

              <Separator />

              <Button
                variant="destructive"
                className="w-full"
                onClick={onLogoutClick}
              >
                <LogOut className="h-4 w-4 mr-2" />
                Log Out
              </Button>
            </>
          ) : (
            <div className="space-y-5">
              <div className="flex items-center">
                {/* <Shield className="h-5 w-5 mr-2 text-pet-purple" /> */}
                <div>
                  <p className="text-md">Your data is stored locally only</p>
                  <p className="text-md text-gray-500">Sign in to backup and sync</p>
                </div>
              </div>

              <Button
                variant="default"
                size="lg"
                className="w-full flex items-center justify-center gap-2"
                onClick={onLoginClick}
              >
                <LogIn className="h-5 w-5" />
                Sign Up or Sign In
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default AccountSection;
