import React, {useState, useEffect} from 'react'
import {BellRing, AlertCircle, Info, Download} from 'lucide-react'
import {Button} from '@/components/ui/button'
import {useToast} from '@/hooks/use-toast'

// Improved standalone detection for iOS
const isIOSStandalone = () => {
  // Check if running on iOS
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;
  if(!isIOS) return false;

  // Multiple ways to detect standalone mode
  const standalone =
    // Standard way to detect standalone mode
    window.matchMedia('(display-mode: standalone)').matches ||
    // iOS-specific way
    (window.navigator as any).standalone === true ||
    // Check if the display-mode is standalone in any media query
    window.matchMedia('(display-mode: standalone)').matches ||
    // Check URL parameters (can be used for testing)
    window.location.search.includes('standalone=true');

  console.log('iOS standalone detection in component:', {
    mediaQuery: window.matchMedia('(display-mode: standalone)').matches,
    navigatorStandalone: (window.navigator as any).standalone,
    result: standalone
  });

  return standalone;
};

const NotificationPermission = () => {
  const [permissionStatus, setPermissionStatus] = useState<string>('default');
  const [platform, setPlatform] = useState<'ios' | 'android' | 'other'>('other');
  const [isStandalone, setIsStandalone] = useState(false);
  const {toast} = useToast();

  // Detect platform and check current permission status
  useEffect(() => {
    // Detect platform
    if(/iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream) {
      setPlatform('ios');
      // Check if app is installed as PWA with improved detection
      setIsStandalone(isIOSStandalone());
    } else if(/Android/.test(navigator.userAgent)) {
      setPlatform('android');
    } else {
      setPlatform('other');
    }

    // Check notification permission
    if('Notification' in window) {
      setPermissionStatus(Notification.permission);
      console.log('Current notification permission:', Notification.permission);
    }
  }, []);

  const requestPermission = async () => {
    if(!('Notification' in window)) {
      toast({
        title: "Not supported",
        description: "Notifications are not supported in your browser.",
        variant: "destructive",
      });
      return;
    }

    // iOS-specific handling with improved detection
    if(platform === 'ios') {
      // Re-check standalone status to be sure
      const standalone = isIOSStandalone();
      console.log('iOS standalone status before permission request:', standalone);

      if(!standalone) {
        toast({
          title: "Install as App First",
          description: "For notifications on iOS, add this app to your Home Screen (tap share icon → Add to Home Screen).",
          duration: 6000,
        });
        return;
      }
    }

    try {
      console.log('Requesting notification permission...');
      const permission = await Notification.requestPermission();
      console.log('Permission request result:', permission);
      setPermissionStatus(permission);

      if(permission === 'granted') {
        toast({
          title: "Notifications enabled",
          description: "You'll now receive notifications for walk tracking.",
        });

        // Show a test notification
        if('serviceWorker' in navigator && navigator.serviceWorker.controller) {
          // For iOS, register for push specifically
          if(platform === 'ios') {
            console.log('Sending REGISTER_IOS_PUSH message to service worker');
            navigator.serviceWorker.controller.postMessage({
              type: 'REGISTER_IOS_PUSH'
            });
          } else {
            // Create a simple test notification for other platforms
            navigator.serviceWorker.controller.postMessage({
              type: 'TEST_NOTIFICATION'
            });
          }
        } else {
          console.log('Service worker not available for test notification');
          // Fallback if service worker isn't available
          new Notification('Notifications enabled', {
            body: 'You will now receive updates about your dog walks.',
            icon: '/icons/icon-192x192.png'
          });
        }
      } else if(permission === 'denied') {
        // Platform-specific guidance
        if(platform === 'ios') {
          toast({
            title: "Notifications blocked",
            description: "Please enable notifications in iOS Settings → Notifications → My Dog In Fit.",
            variant: "destructive",
          });
        } else if(platform === 'android') {
          toast({
            title: "Notifications blocked",
            description: "Please enable notifications in Settings → Apps → My Dog In Fit → Notifications.",
            variant: "destructive",
          });
        } else {
          toast({
            title: "Notifications blocked",
            description: "Please enable notifications in your browser settings to receive walk updates.",
            variant: "destructive",
          });
        }
      }
    } catch(error) {
      console.error('Error requesting notification permission:', error);
      toast({
        title: "Error",
        description: "There was a problem enabling notifications.",
        variant: "destructive",
      });
    }
  };

  if(permissionStatus === 'granted') {
    return null;
  }

  return (
    <div className="flex flex-col gap-4 pt-1 mb-10">
      {/* <div className="flex items-center gap-2"> */}
      <h3 className="font-medium">Push Notifications</h3>
      {/* </div> */}

      <p className="text-md text-muted-foreground">
        Push notifications allow you to control your dog walks even when the app is in the background.
      </p>

      {platform === 'ios' && !isStandalone && (
        <div className="flex flex-col gap-2 bg-amber-50 p-3 rounded-md border border-amber-200">
          <div className="flex items-center gap-2 text-amber-700">
            <Download className="h-4 w-4" />
            <span className="font-medium">Install as App First</span>
          </div>
          <p className="text-xs text-amber-700">
            On iOS, notifications work best when you add this app to your Home Screen.
            Tap the share icon and select "Add to Home Screen".
          </p>
        </div>
      )}

      {permissionStatus === 'denied' && (<div className="flex flex-col mb-2">
        {/* <div className="flex items-center gap-2 text-pet-red text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>Notifications are blocked</span>
            </div> */}

        {platform === 'ios' && (
          <div className="flex items-start gap-2 text-sm bg-primary/15 p-4 rounded-md">
            <Info className="h-4 w-4 mt-0.5 shrink-0" />
            <span>To enable: go to iOS Settings → Notifications → find and enable this app. If not listed, try closing and reopening the app from your Home Screen.</span>
          </div>
        )}

        {platform === 'android' && (
          <div className="flex items-start gap-2 text-sm bg-gray-100 p-2 rounded-xs">
            <Info className="h-4 w-4 mt-0.5 shrink-0" />
            <span>To enable: go to Settings → Apps → My Dog In Fit → Notifications → Allow notifications</span>
          </div>
        )}

        {platform === 'other' && (
          <div className="flex items-start gap-2 text-sm bg-gray-100 p-2 rounded-xs">
            <Info className="h-4 w-4 mt-0.5 shrink-0" />
            <span>To enable: click the lock icon in your browser's address bar and allow notifications</span>
          </div>
        )}
      </div>)}
      <Button onClick={requestPermission} className="h-12">
        Enable Notifications
      </Button>
    </div >
  );
};

export default NotificationPermission;
