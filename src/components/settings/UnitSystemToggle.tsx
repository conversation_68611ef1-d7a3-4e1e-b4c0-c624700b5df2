
import React from 'react'

import {But<PERSON>} from '@/components/ui/button'
import {Ta<PERSON>, TabsList, TabsTrigger} from '@/components/ui/tabs'

import {useLanguage} from '@/hooks/useLanguage'

type UnitType = 'metric' | 'imperial';

interface UnitSystemToggleProps {
  unitSystem: UnitType;
  onUnitSystemChange: (system: UnitType) => void;
}

const UnitSystemToggle = ({unitSystem, onUnitSystemChange}: UnitSystemToggleProps) => {
  const {t} = useLanguage();

  return (
    <div className="flex space-x-2">
      <Tabs defaultValue={unitSystem} onValueChange={onUnitSystemChange} className="w-full">
        <TabsList>
          <TabsTrigger value="metric">{t.settings.metric}</TabsTrigger>
          <TabsTrigger value="imperial">{t.settings.imperial}</TabsTrigger>
        </TabsList>
      </Tabs>
      {/* <Button
        variant={unitSystem === 'metric' ? "default" : "outline"}
        size="sm"
        onClick={() => onUnitSystemChange('metric')}
        className={unitSystem === 'metric' ? "bg-pet-purple text-white" : ""}
      >
        {t.settings.metric}
      </Button>
      <Button
        variant={unitSystem === 'imperial' ? "default" : "outline"}
        size="sm"
        onClick={() => onUnitSystemChange('imperial')}
        className={unitSystem === 'imperial' ? "bg-pet-purple text-white" : ""}
      >
        {t.settings.imperial}
      </Button> */}
    </div>
  );
};

export default UnitSystemToggle;
