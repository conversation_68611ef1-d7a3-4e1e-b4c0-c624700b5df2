
import React from 'react'
import {Settings, Bug} from 'lucide-react'
import {Card, CardContent} from '@/components/ui/card'
import {Button} from '@/components/ui/button'
import {Separator} from '@/components/ui/separator'
import {ThemeToggle} from '@/components/ThemeToggle'
import UnitSystemToggle from './UnitSystemToggle'
import {useLanguage} from '@/hooks/useLanguage'
import {Language, languageNames} from '@/i18n'
import {useWalkTracker} from '@/hooks/useWalkTracker'
import {Switch} from '@/components/ui/switch'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

type UnitType = 'metric' | 'imperial';

interface AppSettingsSectionProps {
  unitSystem: UnitType;
  onUnitSystemChange: (system: UnitType) => void;
}

const AppSettingsSection = ({unitSystem, onUnitSystemChange}: AppSettingsSectionProps) => {
  const {language, setLanguage, t} = useLanguage();
  const {debugMode, toggleDebugMode} = useWalkTracker();

  const handleLanguageChange = (newLanguage: Language) => {
    setLanguage(newLanguage);
  };

  return (
    <Card className="mb-10 shadow-none">
      <CardContent className="p-0">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-bold">{t.settings.appSettings}</h2>
          {/* <div className="bg-pet-green p-2 rounded-full"> */}
          <Settings className="h-5 w-5 text-primary" />
          {/* </div> */}
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <p>{t.settings.units}</p>
            <UnitSystemToggle
              unitSystem={unitSystem}
              onUnitSystemChange={onUnitSystemChange}
            />
          </div>

          <Separator />

          <div className="flex justify-between items-center">
            <p>{t.settings.theme}</p>
            <ThemeToggle showLabel={true} />
          </div>

          <Separator />

          <div className="flex justify-between items-center">
            <p>{t.settings.language}</p>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="text-secondary">
                  {languageNames[language]}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="rounded-xl">
                <DropdownMenuItem onClick={() => handleLanguageChange('en')}>
                  {t.settings.english}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleLanguageChange('es')}>
                  {t.settings.spanish}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleLanguageChange('ru')}>
                  {t.settings.russian}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <Separator />

          {/* <div className="flex justify-between items-center">
            <div className="flex items-center">
              <p>{t.settings.about}</p>
            </div>
            <Button variant="ghost" size="sm" className="text-pet-purple">{t.settings.view}</Button>
          </div>

          <Separator /> */}

          {/* Debug Mode Toggle */}
          <div className="flex justify-between items-center">
            {/* <div className="flex items-center"> */}
            {/* <Bug className="h-4 w-4 mr-2 text-amber-500" /> */}
            <p>Debug Mode</p>
            {/* </div> */}
            <Switch
              checked={debugMode}
              onCheckedChange={toggleDebugMode}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AppSettingsSection;
