
import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { useTheme } from '@/components/ThemeProvider';
import { cn } from '@/lib/utils';

const PageHeader = ({ title }: { title: string }) => {
  const { theme } = useTheme();
  
  const getGradientClass = () => {
    if (theme === "light") {
      return "bg-linear-to-b from-pet-purple to-pet-darkPurple";
    } else if (theme === "dark") {
      return "bg-linear-to-b from-pet-darkPurple to-pet-navy";
    } else if (theme === "colored") {
      return "bg-transparent"; // Make header transparent in colored theme
    }
    
    return "bg-linear-to-b from-pet-purple to-pet-darkPurple";
  };
  
  return (
    <div className={cn(
      "text-white p-6 rounded-b-3xl shadow-md",
      getGradientClass()
    )}>
      <div className="flex items-center">
        <Link to="/" className="rounded-full p-2 bg-white/20 hover:bg-white/30 transition-colors">
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <h1 className="text-xl font-bold ml-3">{title}</h1>
      </div>
    </div>
  );
};

export default PageHeader;
