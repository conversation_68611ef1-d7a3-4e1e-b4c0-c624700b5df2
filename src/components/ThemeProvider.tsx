
import {createContext, useContext, useEffect, useState} from "react"

type Theme = "light" | "dark" | "colored";

type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
};

type ThemeProviderState = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
};

const initialState: ThemeProviderState = {
  theme: "light",
  setTheme: () => null,
};

const ThemeProviderContext = createContext<ThemeProviderState>(initialState);

export function ThemeProvider({
  children,
  defaultTheme = "dark",
  storageKey = "theme",
  ...props
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(
    () => (localStorage.getItem(storageKey) as Theme) || defaultTheme
  );

  useEffect(() => {
    const root = window.document.documentElement;

    // Remove all theme classes first
    root.classList.remove("light", "dark", "colored");

    // Add smooth transition class before changing theme
    root.classList.add("transition-colors", "duration-300");

    // Add the selected theme class
    root.classList.add(theme);

    // Persist theme in localStorage
    localStorage.setItem(storageKey, theme);

    // Remove transition class after theme change is complete
    const transitionTimeout = setTimeout(() => {
      root.classList.remove("transition-colors", "duration-300");
    }, 300);

    return () => clearTimeout(transitionTimeout);
  }, [theme, storageKey]);

  return (
    <ThemeProviderContext.Provider {...props} value={{theme, setTheme}}>
      {children}
    </ThemeProviderContext.Provider>
  );
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);

  if(context === undefined)
    throw new Error("useTheme must be used within a ThemeProvider");

  return context;
};
