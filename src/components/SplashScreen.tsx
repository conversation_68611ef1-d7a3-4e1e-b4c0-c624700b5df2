import React from 'react'
import {cn} from '@/lib/utils'
import {useTheme} from '@/components/ThemeProvider'

const SplashScreen = () => {
  const {theme} = useTheme();

  return (
    <div className={cn(
      "fixed inset-0 flex flex-col items-center justify-center z-50",
      theme === "light" ? "bg-background" :
        theme === "dark" ? "bg-background" :
          "bg-linear-to-b from-pet-acidGreen to-pet-blue"
    )}>
      <div className="w-32 h-32 mb-8 relative">
        <img
          src="/icons/icon-512x512.png"
          alt="My Dog In Fit"
          className="w-full h-full object-contain animate-pulse"
        />
      </div>

      <div className="flex flex-col items-center">
        <h1 className={cn(
          "text-2xl font-bold mb-2",
          theme === "colored" ? "text-white" : "text-primary"
        )}>
          My Dog In Fit
        </h1>

        <div className="flex space-x-2 mt-4">
          <div className="w-3 h-3 rounded-full bg-pet-pink animate-bounce" style={{animationDelay: "0ms"}}></div>
          <div className="w-3 h-3 rounded-full bg-pet-acidGreen animate-bounce" style={{animationDelay: "150ms"}}></div>
          <div className="w-3 h-3 rounded-full bg-pet-blue animate-bounce" style={{animationDelay: "300ms"}}></div>
        </div>
      </div>
    </div>
  );
};

export default SplashScreen;
