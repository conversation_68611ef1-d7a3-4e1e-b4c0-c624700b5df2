
import React from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import {Button} from '@/components/ui/button'
import {<PERSON>, Wind, Dumbbell} from 'lucide-react'

interface ActivityTypeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (type: string) => void;
}

const ActivityTypeModal: React.FC<ActivityTypeModalProps> = ({
  isOpen,
  onClose,
  onSelect,
}) => {
  const activityTypes = [
    {
      type: 'Walk',
      icon: <Dog className="h-5 w-5" />,
      description: 'A casual stroll with your dog',
      color: 'bg-pet-acidGreen',
    },
    {
      type: 'Run',
      icon: <Wind className="h-5 w-5" />,
      description: 'A faster-paced activity',
      color: 'bg-pet-pink',
    },
    {
      type: 'Training',
      icon: <Dumbbell className="h-5 w-5" />,
      description: 'Teaching new skills or tricks',
      color: 'bg-pet-purple',
    },
  ];

  const handleSelect = (type: string) => {
    onSelect(type);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>What type of activity was this?</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {activityTypes.map((activity) => (
            <Button
              key={activity.type}
              onClick={() => handleSelect(activity.type)}
              className={`flex items-center justify-start gap-3 h-16 ${activity.color} hover:opacity-90 text-black`}
            >
              <div className="bg-white/30 p-2 rounded-full">{activity.icon}</div>
              <div className="text-left">
                <div className="font-bold">{activity.type}</div>
                <div className="text-xs opacity-80">{activity.description}</div>
              </div>
            </Button>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ActivityTypeModal;
