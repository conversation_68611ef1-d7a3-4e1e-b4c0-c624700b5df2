
import React from 'react'
import {Avatar, AvatarFallback, AvatarImage} from '@/components/ui/avatar'
import {cn} from '@/lib/utils'

interface PetAvatarProps {
  src?: string;
  name: string;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: () => void;
}

const PetAvatar = ({src, name, size = 'md', className, onClick}: PetAvatarProps) => {
  const sizeClasses = {
    xs: 'h-10 w-10',
    sm: 'h-12 w-12',
    md: 'h-16 w-16',
    lg: 'h-24 w-24'
  };

  const initials = name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase();

  return (
    <Avatar
      className={cn(
        sizeClasses[size],
        'rounded-full shadow-md hover:shadow-lg transition-shadow duration-200',
        className
      )}
      onClick={onClick}
    >
      <AvatarImage src={src} alt={name} className="object-cover" />
      <AvatarFallback className="pet-gradient text-white">
        {initials}
      </AvatarFallback>
    </Avatar>
  );
};

export default PetAvatar;
