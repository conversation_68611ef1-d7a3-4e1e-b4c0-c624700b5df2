import React, { useState, useEffect } from 'react';
import { Download, X } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

const InstallPrompt = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showPrompt, setShowPrompt] = useState(false);

  useEffect(() => {
    // Check if already installed or recently dismissed
    const hasPrompted = localStorage.getItem('installPromptDismissed');
    const lastPromptTime = parseInt(localStorage.getItem('lastPromptTime') || '0');
    const now = Date.now();
    const threeDaysInMs = 3 * 24 * 60 * 60 * 1000;
    
    // Only show prompt if not dismissed in the last 3 days
    const shouldPrompt = !hasPrompted || (now - lastPromptTime > threeDaysInMs);
    
    if (!shouldPrompt) return;

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent Chrome 76+ from automatically showing the prompt
      e.preventDefault();
      // Stash the event so it can be triggered later
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      // Show our custom prompt after a short delay
      setTimeout(() => setShowPrompt(true), 2000);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;
    
    // Show the install prompt
    deferredPrompt.prompt();
    
    // Wait for the user to respond to the prompt
    const { outcome } = await deferredPrompt.userChoice;
    
    // We no longer need the prompt regardless of outcome
    setDeferredPrompt(null);
    setShowPrompt(false);
    
    // Track the outcome
    if (outcome === 'accepted') {
      console.log('User accepted the install prompt');
    } else {
      console.log('User dismissed the install prompt');
      // Store that user dismissed the prompt
      localStorage.setItem('installPromptDismissed', 'true');
      localStorage.setItem('lastPromptTime', Date.now().toString());
    }
  };

  const handleDismiss = () => {
    setShowPrompt(false);
    localStorage.setItem('installPromptDismissed', 'true');
    localStorage.setItem('lastPromptTime', Date.now().toString());
  };

  if (!showPrompt) return null;

  return (
    <div className="fixed bottom-20 left-4 right-4 bg-white p-4 rounded-lg shadow-lg z-50 border border-gray-200">
      <div className="flex items-start">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <Download className="h-5 w-5 text-pet-purple" />
            <h3 className="font-medium">Install My Dog In Fit</h3>
          </div>
          <p className="text-sm text-gray-600 mb-3">
            Add to your home screen for the best experience with walk tracking and notifications.
          </p>
          <div className="flex gap-2">
            <Button 
              onClick={handleInstallClick} 
              className="bg-pet-purple hover:bg-purple-700 text-white"
            >
              Install
            </Button>
            <Button 
              variant="outline" 
              onClick={handleDismiss}
            >
              Not now
            </Button>
          </div>
        </div>
        <button 
          onClick={handleDismiss} 
          className="p-1 text-gray-400 hover:text-gray-600"
          aria-label="Dismiss"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
};

export default InstallPrompt;