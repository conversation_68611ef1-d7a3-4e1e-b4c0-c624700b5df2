import React from 'react'
import {
  FormItem,
  FormLabel,
  FormControl,
  FormMessage
} from '@/components/ui/form';
import {
  ToggleGroup,
  ToggleGroupItem,
} from "@/components/ui/toggle-group";
import {FormField} from "@/components/ui/form"
import {<PERSON>} from "lucide-react"

interface SizeSelectorProps {
  control: any;
  name: string;
}

const SizeSelector = ({control, name}: SizeSelectorProps) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({field}) => (
        <FormItem>
          <FormLabel>Size <span className="text-red-500">*</span></FormLabel>
          <FormControl>
            <ToggleGroup
              type="single"
              value={field.value || ""}
              onValueChange={(value) => {
                if(value) field.onChange(value);
              }}
              className="justify-between"
            >
              <ToggleGroupItem
                value="small"
                aria-label="Small dog"
                className="flex flex-col items-center gap-1 w-[32%] h-24 data-[state=on]:bg-pet-green data-[state=on]:text-black"
              >
                <Dog className="h-6 w-6" />
                <span className="text-sm">Small</span>
                <span className="text-xs text-gray-500">up to 20 pounds</span>
              </ToggleGroupItem>

              <ToggleGroupItem
                value="medium"
                aria-label="Medium dog"
                className="flex flex-col items-center gap-1 w-[32%] h-24 data-[state=on]:bg-pet-yellow data-[state=on]:text-black"
              >
                <Dog className="h-8 w-8" />
                <span className="text-sm">Medium</span>
                <span className="text-xs text-gray-500">20-60 pounds</span>
              </ToggleGroupItem>

              <ToggleGroupItem
                value="large"
                aria-label="Large dog"
                className="flex flex-col items-center gap-1 w-[32%] h-24 data-[state=on]:bg-pet-orange data-[state=on]:text-black"
              >
                <Dog className="h-10 w-10" />
                <span className="text-sm">Large</span>
                <span className="text-xs text-gray-500">60+ pounds</span>
              </ToggleGroupItem>
            </ToggleGroup>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default SizeSelector;
