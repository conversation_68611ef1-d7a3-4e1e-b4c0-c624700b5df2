
import React, { useState, useEffect, useRef } from 'react';
import { dogBreeds } from '@/data/dogBreeds';
import { BreedInput } from './breed-autocomplete/BreedInput';
import { BreedDropdown } from './breed-autocomplete/BreedDropdown';
import { useBreedRecognition } from '@/hooks/use-breed-recognition';
import { useLanguage } from '@/hooks/useLanguage';

interface BreedAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  imageUrl?: string;
}

export function BreedAutocomplete({ 
  value, 
  onChange, 
  disabled,
  imageUrl 
}: BreedAutocompleteProps) {
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const containerRef = useRef<HTMLDivElement>(null);
  const [processedImageUrl, setProcessedImageUrl] = useState('');
  const { t } = useLanguage();
  
  // Update input value when value prop changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (
        containerRef.current && 
        !containerRef.current.contains(event.target as Node)
      ) {
        setOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleOutsideClick);
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
    };
  }, []);
  
  // Filter breeds based on input value for the dropdown
  const filteredBreeds = dogBreeds.filter(breed =>
    breed.name.toLowerCase().includes(inputValue.toLowerCase()) ||
    (t.breeds?.[breed.name.toLowerCase()]?.toLowerCase() || '').includes(inputValue.toLowerCase())
  );
  
  const { isRecognizing, recognizeBreed } = useBreedRecognition({
    onBreedDetected: (breedName) => {
      setInputValue(breedName);
      onChange(breedName);
    }
  });
  
  // Only run breed recognition when imageUrl changes and hasn't been processed yet
  useEffect(() => {
    if (imageUrl && imageUrl !== processedImageUrl) {
      recognizeBreed(imageUrl);
      setProcessedImageUrl(imageUrl);
    }
  }, [imageUrl, recognizeBreed, processedImageUrl]);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    
    // If the input is cleared or doesn't match any breed, update the value
    if (newValue === '') {
      onChange('');
    }
  };
  
  const handleBreedSelection = (breedName: string) => {
    setInputValue(breedName);
    onChange(breedName);
    setOpen(false);
  };
  
  const handleClear = () => {
    setInputValue('');
    onChange('');
  };

  // Get localized breed name if available
  const getLocalizedBreedName = (breedName: string) => {
    return t.breeds?.[breedName.toLowerCase()] || breedName;
  };
  
  return (
    <div className="relative w-full" ref={containerRef}>
      <BreedInput
        value={inputValue}
        onChange={handleInputChange}
        onClick={() => !disabled && setOpen(true)}
        onFocus={() => !disabled && setOpen(true)}
        onClear={handleClear}
        disabled={disabled || isRecognizing}
        isRecognizing={isRecognizing}
      />

      {open && !disabled && (
        <BreedDropdown
          breeds={filteredBreeds}
          selectedBreed={value}
          onBreedSelect={handleBreedSelection}
          getLocalizedBreedName={getLocalizedBreedName}
        />
      )}
    </div>
  );
};
