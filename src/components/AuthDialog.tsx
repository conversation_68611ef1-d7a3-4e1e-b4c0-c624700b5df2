
import React, {useState, useEffect} from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {Button} from '@/components/ui/button'
import {Input} from '@/components/ui/input'
import {Label} from '@/components/ui/label'
import {Ta<PERSON>, TabsContent, TabsList, TabsTrigger} from '@/components/ui/tabs'
import {Mail, Lock, Apple, LucideGithub} from 'lucide-react'
import {handleLogin, handleSignup, handleGoogleLogin, handleAppleLogin} from '@/lib/authHelpers'
import {useToast} from '@/hooks/use-toast'

interface AuthDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

const AuthDialog = ({open, onOpenChange, onSuccess}: AuthDialogProps) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isAppleSupported, setIsAppleSupported] = useState(false);
  const {toast} = useToast();

  useEffect(() => {
    // Check if on iOS or macOS device
    const isAppleDevice = /iPad|iPhone|iPod|Mac/.test(navigator.userAgent) && !(window as any).MSStream;
    setIsAppleSupported(isAppleDevice);
  }, []);

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if(!email || !password) {
      toast({
        title: "Missing Fields",
        description: "Please enter both email and password.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    const success = await handleLogin(email, password);
    setIsLoading(false);

    if(success) {
      onOpenChange(false);
      if(onSuccess) onSuccess();
    }
  };

  const handleEmailSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    if(!email || !password) {
      toast({
        title: "Missing Fields",
        description: "Please enter both email and password.",
        variant: "destructive",
      });
      return;
    }

    if(password.length < 8) {
      toast({
        title: "Password Too Short",
        description: "Password must be at least 8 characters long.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    const success = await handleSignup(email, password);
    setIsLoading(false);

    if(success) {
      toast({
        title: "Check your email",
        description: "We've sent you a verification link to complete sign-up.",
      });
    }
  };

  const handleGoogleAuth = async () => {
    setIsLoading(true);
    await handleGoogleLogin();
    setIsLoading(false);
  };

  const handleAppleAuth = async () => {
    setIsLoading(true);
    await handleAppleLogin();
    setIsLoading(false);
  };

  const renderSocialButtons = () => (
    <div className="grid grid-cols-1 gap-2">
      {isAppleSupported && (
        <Button variant="outline" type="button" onClick={handleAppleAuth} disabled={isLoading} className="w-full">
          <Apple className="h-5 w-5 mr-2" />
          Continue with Apple
        </Button>
      )}

      <Button variant="outline" type="button" onClick={handleGoogleAuth} disabled={isLoading} className="w-full">
        <svg viewBox="0 0 24 24" className="h-5 w-5 mr-2">
          <path
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            fill="#4285F4"
          />
          <path
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            fill="#34A853"
          />
          <path
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            fill="#FBBC05"
          />
          <path
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            fill="#EA4335"
          />
        </svg>
        Continue with Google
      </Button>

      <div className="relative flex items-center justify-center mt-2 mb-2">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative bg-background px-2 text-xs uppercase text-muted-foreground">
          Or continue with email
        </div>
      </div>
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px] rounded-2xl">
        <DialogHeader>
          <DialogTitle>Authentication</DialogTitle>
          <DialogDescription>
            Sign in or create a new account to sync your pet's data across devices.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="signup" className="w-full">
          <TabsList className="grid grid-cols-2 w-full">
            <TabsTrigger value="signup">Sign Up</TabsTrigger>
            <TabsTrigger value="signin">Sign In</TabsTrigger>
          </TabsList>

          <TabsContent value="signup" className="space-y-4 mt-4">
            {renderSocialButtons()}

            <form onSubmit={handleEmailSignup} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="signup-email">Email</Label>
                <div className="relative">
                  <Mail className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    id="signup-email"
                    type="email"
                    placeholder="<EMAIL>"
                    className="pl-8"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isLoading}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="signup-password">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    id="signup-password"
                    type="password"
                    placeholder="••••••••"
                    className="pl-8"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    disabled={isLoading}
                  />
                </div>
              </div>

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Signing up..." : "Sign Up with Email"}
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="signin" className="space-y-4 mt-4">
            {renderSocialButtons()}

            <form onSubmit={handleEmailLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="login-email">Email</Label>
                <div className="relative">
                  <Mail className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    id="login-email"
                    type="email"
                    placeholder="<EMAIL>"
                    className="pl-8"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isLoading}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="login-password">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    id="login-password"
                    type="password"
                    placeholder="••••••••"
                    className="pl-8"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    disabled={isLoading}
                  />
                </div>
              </div>

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Signing in..." : "Sign In with Email"}
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default AuthDialog;
