
import React from 'react';
import { format } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card } from '@/components/ui/card';
import { useLanguage } from '@/hooks/useLanguage';
import { formatWeight } from '@/utils/unitConversion';

interface WeightHistoryTableProps {
  petId: string;
  unitSystem: 'metric' | 'imperial';
}

const WeightHistoryTable = ({ petId, unitSystem }: WeightHistoryTableProps) => {
  const { t } = useLanguage();

  // Mock weight log data (would come from API/database in a real app)
  const weightLogs = [
    { id: '1', date: new Date(2023, 5, 15), weight: 30.0, notes: 'Monthly checkup' },
    { id: '2', date: new Date(2023, 5, 1), weight: 30.4, notes: 'After diet change' },
    { id: '3', date: new Date(2023, 4, 15), weight: 30.8, notes: 'Vet visit' },
    { id: '4', date: new Date(2023, 4, 1), weight: 31.2, notes: 'Started new exercise routine' },
    { id: '5', date: new Date(2023, 3, 15), weight: 32.0, notes: 'Regular weigh-in' },
    { id: '6', date: new Date(2023, 3, 1), weight: 32.5, notes: 'Initial measurement' },
  ];
  
  // Filter logs for selected pet
  const filteredLogs = weightLogs.filter(log => log.id === petId || petId === '1');

  return (
    <Card className="overflow-hidden border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t.common.date}</TableHead>
            <TableHead>{t.common.weight} ({unitSystem === 'metric' ? t.units.kg : t.units.lb})</TableHead>
            <TableHead className="hidden sm:table-cell">{t.common.notes}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredLogs.length > 0 ? (
            filteredLogs.map((log) => (
              <TableRow key={log.id}>
                <TableCell>{format(log.date, 'MMM dd, yyyy')}</TableCell>
                <TableCell>
                  {formatWeight(log.weight, unitSystem)}
                </TableCell>
                <TableCell className="hidden sm:table-cell">{log.notes}</TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={3} className="text-center py-4 text-gray-500">
                {t.common.noWeightLogs}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </Card>
  );
};

export default WeightHistoryTable;
