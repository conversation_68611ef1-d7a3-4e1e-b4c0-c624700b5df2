
import React, {useState} from 'react'
import {Card, CardContent} from '@/components/ui/card'
import {Button} from '@/components/ui/button'
import {Info, Check, ChevronDown, ChevronUp, Cross} from 'lucide-react'
import {cn} from '@/lib/utils'
import {DogRecommendation} from '@/data/types'

interface DogRecommendationCardProps {
  recommendation: DogRecommendation;
  onMarkAsSeen: (id: string) => void;
  className?: string;
}

const DogRecommendationCard = ({
  recommendation,
  onMarkAsSeen,
  className
}: DogRecommendationCardProps) => {
  const [expanded, setExpanded] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  if(!isVisible) {
    return null;
  }

  const getCategoryColor = (category: string) => {
    switch(category) {
      case 'diet':
        return 'bg-pet-yellow';
      case 'activity':
        return 'bg-pet-green';
      case 'health':
        return 'bg-pet-purple';
      default:
        return 'bg-pet-lightPurple';
    }
  };

  return (
    // <Card className={cn("border border-gray-200 overflow-hidden  bg-pet-orange/15", className)}>
    // <Card className={cn("shadow-xs overflow-hidden  bg-pet-teal/5", className)}>
    // <Card className={cn("shadow-xs border border-gray-50 overflow-hidden bg-gray-50", className)}>
    // <Card className={cn("shadow-md border border-gray-100 overflow-hidden bg-gray-100", className)}>
    // <Card className={cn("shadow-lg border border-gray-100 overflow-hidden bg-white", className)}>
    // <Card className={cn("shadow-lg border border-gray-200 overflow-hidden bg-pet-darkPurple/5 backdrop-blur-2xl", className)}>
    // <Card className={cn("shadow-lg border border-gray-200 overflow-hidden bg-secondary-background/25 backdrop-blur-2xl", className)}>
    <Card className={cn("shadow-lg border border-gray-200 overflow-hidden bg-primary/20 backdrop-blur-2xl", className)}>
      <div className="absolute top-2 right-2 cursor-pointer" onClick={() => setIsVisible(false)}>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          className="w-5 h-5 mt-1 mr-1"
        >
          <path
            fillRule="evenodd"
            d="M5.47 5.47a.75.75 0 011.06 0L12 10.94l5.47-5.47a.75.75 0 111.06 1.06L13.06 12l5.47 5.47a.75.75 0 11-1.06 1.06L12 13.06l-5.47 5.47a.75.75 0 01-1.06-1.06L10.94 12 5.47 6.53a.75.75 0 010-1.06z"
            clipRule="evenodd"
          />
        </svg>
      </div>
      {/* <Card className={cn("shadow-xs overflow-hidden bg-pet-lightPurple/70", className)}> */}
      {/* <Card className={cn("shadow-xs overflow-hidden bg-pet-teal/20", className)}> */}
      <CardContent className="px-4 pt-6 pb-2">
        <div className="flex items-start">
          {/* <div className={cn("p-1 rounded-full mt-0.5", getCategoryColor(recommendation.category))}>
            <Info className="h-1 w-1 text-white" />
          </div> */}

          <div className="flex flex-col">
            {recommendation.text.length > 100 ? <>
              {/* <Button
              //   variant="link"
              //   size="sm"
              //   className="p-0 h-auto text-xs text-pet-purple mt-1"
              //   onClick={() => setExpanded(!expanded)}
              // > */}
              <p className={cn(
                "text-md font-light",
                expanded ? "" : "line-clamp-2"
              )}
                onClick={() => setExpanded(!expanded)}
              >
                {recommendation.text}
                {expanded && <ChevronUp className="h-4 w-4 inline-block ml-1" />}
                {/* {expanded ? <ChevronUp className="h-4 w-4  inline-block" /> : <ChevronDown className="h-4 w-4  inline-block" />} */}
              </p>
              {/* </Button> */}
            </> :
              <p className="text-md font-medium">
                {recommendation.text}
              </p>
            }

            {/* {recommendation.text.length > 100 && (
              <Button
                variant="link"
                size="sm"
                className="p-0 h-auto text-xs text-pet-purple mt-1"
                onClick={() => setExpanded(!expanded)}
              >
                {expanded ? "" : "Read"}
                {expanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </Button>
            )} */}
            {!recommendation.seen && (
              <Button
                variant="ghost"
                size="sm"
                className="mt-0 text-md self-end"
                onClick={() => onMarkAsSeen(recommendation.id)}
              >
                {/* <Check className="h-3 w-3 mr-1" /> */}
                Next tip
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DogRecommendationCard;
