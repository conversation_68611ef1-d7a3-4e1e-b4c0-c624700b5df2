import React, {useState} from 'react'
import {PetFood} from '@/data/types'
import {Button} from '@/components/ui/button'
import {Input} from '@/components/ui/input'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import {useForm} from 'react-hook-form'
import {
  Apple, Beef, Carrot, Fish, Milk, Wheat, Soup, LeafyGreen,
  Plus, X, CalendarIcon
} from 'lucide-react';
import {cn} from '@/lib/utils'
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/popover'
import {useLanguage} from '@/hooks/useLanguage'
import {LocalizedCalendar} from '@/components/LocalizedCalendar'
import {type DateRange} from 'react-day-picker'
import {formatDate} from '@/lib/utils'

const foodIcons: Record<string, React.ReactNode> = {
  apple: <Apple className="h-5 w-5" />,
  beef: <Beef className="h-5 w-5" />,
  carrot: <Carrot className="h-5 w-5" />,
  fish: <Fish className="h-5 w-5" />,
  milk: <Milk className="h-5 w-5" />,
  wheat: <Wheat className="h-5 w-5" />,
  soup: <Soup className="h-5 w-5" />,
  'leafy-green': <LeafyGreen className="h-5 w-5" />
};

// Brand logos (in a real app, these would be actual image URLs)
const brandLogos: Record<string, string> = {
  'Royal Canin': 'https://placehold.co/100x40?text=Royal+Canin',
  'Purina': 'https://placehold.co/100x40?text=Purina',
  'Hill\'s': 'https://placehold.co/100x40?text=Hills',
  'Blue Buffalo': 'https://placehold.co/100x40?text=Blue+Buffalo',
  'Pedigree': 'https://placehold.co/100x40?text=Pedigree',
  'Merrick': 'https://placehold.co/100x40?text=Merrick',
  'Taste of the Wild': 'https://placehold.co/100x40?text=ToTW',
  'Wellness': 'https://placehold.co/100x40?text=Wellness',
};

interface FoodManagementProps {
  petFoods: PetFood[];
  foodHistory?: Array<{foodId: string; name: string; brand: string; startDate: Date | string; endDate?: Date | string}>;
  onFoodAdded: (food: PetFood) => void;
  onFoodRemoved: (foodId: string) => void;
  onFoodPreferred: (foodId: string, isPreferred: boolean, startDate?: Date) => void;
}

const FoodManagement = ({
  petFoods,
  foodHistory = [],
  onFoodAdded,
  onFoodRemoved,
  onFoodPreferred
}: FoodManagementProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIcon, setSelectedIcon] = useState<string>('beef');
  const [startDate, setStartDate] = useState<Date>(new Date());
  const {t, format: formatTranslation} = useLanguage();

  const form = useForm({
    defaultValues: {
      name: '',
      brand: '',
      caloriesPerCup: 300,
    }
  });

  const handleSubmit = (values: {name: string; brand: string; caloriesPerCup: number}) => {
    const newFood: PetFood = {
      id: `food-${Date.now()}`,
      name: values.name,
      brand: values.brand,
      caloriesPerCup: values.caloriesPerCup,
      icon: selectedIcon,
      logo: brandLogos[values.brand] || null,
      isPreferred: true,
      startDate: startDate
    };
    onFoodAdded(newFood);
    form.reset();
    setSelectedIcon('beef');
    setStartDate(new Date());
    setIsOpen(false);
  };

  // Handle date selection for the calendar
  const handleDateSelect = (date: Date | Date[] | DateRange | undefined) => {
    if(date && date instanceof Date) {
      setStartDate(date);
    }
  };

  // Only allow one preferred food
  const handleFoodPreferred = (foodId: string, isPreferred: boolean) => {
    if(isPreferred) {
      onFoodPreferred(foodId, true, startDate);

      // Set all other foods to not preferred
      petFoods.forEach(food => {
        if(food.id !== foodId && food.isPreferred) {
          onFoodPreferred(food.id, false);
        }
      });
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">{t.common.food}</h3>
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button size="sm" variant="outline" className="flex items-center gap-1">
              <Plus className="h-4 w-4" />
              {t.common.add} {t.common.food}
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md rounded-2xl">
            <DialogHeader>
              <DialogTitle>{t.foods.addFood}</DialogTitle>
              <DialogDescription>{t.foods.addFoodPrompt}</DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({field}) => (
                    <FormItem>
                      <FormLabel>{t.foods.foodName}</FormLabel>
                      <FormControl>
                        <Input placeholder="Premium Dry Dog Food" {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="brand"
                  render={({field}) => (
                    <FormItem>
                      <FormLabel>{t.foods.brand}</FormLabel>
                      <FormControl>
                        <Input placeholder="Royal Canin" list="brand-options" {...field} />
                      </FormControl>
                      <datalist id="brand-options">
                        {Object.keys(brandLogos).map((brand) => (
                          <option key={brand} value={brand} />
                        ))}
                      </datalist>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="caloriesPerCup"
                  render={({field}) => (
                    <FormItem>
                      <FormLabel>{t.foods.caloriesPerCup}</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} onChange={(e) => field.onChange(parseInt(e.target.value))} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <div className="space-y-2">
                  <FormLabel>{t.foods.selectIcon}</FormLabel>
                  <div className="grid grid-cols-4 gap-2">
                    {Object.entries(foodIcons).map(([name, icon]) => (
                      <div
                        key={name}
                        className={`p-2 flex justify-center items-center rounded-full cursor-pointer ${selectedIcon === name ? 'bg-primary text-white' : 'text-primary-foreground'}`}
                        onClick={() => setSelectedIcon(name)}
                      >
                        {icon}
                      </div>
                    ))}
                  </div>
                </div>

                <FormItem className="flex flex-col">
                  <FormLabel>{t.foods.startDate}</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !startDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {startDate ? formatDate(startDate) : t.foods.selectDate}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent>
                      <LocalizedCalendar
                        mode="single"
                        selected={startDate}
                        onSelect={handleDateSelect}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </FormItem>

                <div className="flex justify-end gap-2 pt-4">
                  <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                    {t.common.cancel}
                  </Button>
                  <Button type="submit">{t.common.add}</Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {petFoods.length === 0 ? (
        <div className="text-center p-6 border border-dashed rounded-md">
          <p className="text-muted-foreground">{t.foods.noFoods}</p>
          <p className="text-xs text-muted-foreground">{t.foods.addFoodPrompt}</p>
        </div>
      ) : (
        <div className="space-y-2">
          {petFoods.map((food) => (
            <div key={food.id} className={`flex justify-between items-center p-3 border rounded-md ${food.isPreferred ? 'border-primary bg-primary bg-black/10' : ''}`}>
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-full ${food.isPreferred ? 'bg-primary' : 'bg-gray-300'}`}>
                  {foodIcons[food.icon] && React.cloneElement(foodIcons[food.icon] as React.ReactElement<{className?: string}>, {className: "h-5 w-5 text-white"})}
                </div>
                <div>
                  <p className="font-medium">{food.name}</p>
                  <div className="flex items-center space-x-1">
                    {food.logo && (
                      <img src={food.logo} alt={food.brand} className="h-4 mr-1" />
                    )}
                    <p className="text-xs text-gray-500">{food.brand} • {food.caloriesPerCup} kcal/cup</p>
                  </div>
                  {food.startDate && food.isPreferred && (
                    <p className="text-xs text-pet-purple">
                      {formatTranslation('foods.usedSince', {
                        date: food.startDate ? formatDate(food.startDate) : ''
                      })}
                    </p>
                  )}
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={food.isPreferred ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleFoodPreferred(food.id, !food.isPreferred)}
                >
                  {food.isPreferred ? t.foods.active : t.foods.use}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => onFoodRemoved(food.id)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}

      {foodHistory.length > 0 && (
        <div className="mt-6">
          <h4 className="text-sm font-medium mb-2">{t.foods.foodHistory}</h4>
          <div className="space-y-1">
            {foodHistory.map((entry, index) => (
              <div key={index} className="text-xs border-l-2 border-gray-200 pl-3 py-1">
                <p className="font-medium">{entry.name}</p>
                <p className="text-gray-500">{entry.brand}</p>
                <p className="text-gray-400 text-[10px]">
                  {formatDate(entry.startDate)}
                  {entry.endDate && ` - ${formatDate(entry.endDate)}`}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FoodManagement;
