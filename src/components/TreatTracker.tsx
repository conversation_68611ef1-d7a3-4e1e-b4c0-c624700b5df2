
import React, {useState} from 'react'
import {Button} from '@/components/ui/button'
import {Card, CardContent} from '@/components/ui/card'
import {<PERSON><PERSON>} from 'lucide-react'
import {useToast} from '@/hooks/use-toast'
import {useLanguage} from '@/hooks/useLanguage'

interface TreatTrackerProps {
  treatsCount: number;
  onTreatAdded: () => void;
  onTreatRemoved: () => void;
  dietImpact: string;
}

const TreatTracker = ({
  treatsCount,
  onTreatAdded,
  onTreatRemoved,
  dietImpact
}: TreatTrackerProps) => {
  const {toast} = useToast();
  const {t} = useLanguage();

  const handleAddTreat = () => {
    onTreatAdded();
    toast({
      title: t.common.treatAdded,
      description: t.common.treatAddedMsg,
    });
  };

  return (
    <Card className="border-none">
      <CardContent className="p-5">
        <div className="flex justify-between items-center mb-4">
          <p className="text-md font-light flex items-center">
            <Cookie className="h-5 w-5 mr-2 text-primary" />
            {t.treats.treatsToday}
          </p>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              className="h-7 w-7 rounded-full"
              disabled={treatsCount === 0}
              onClick={onTreatRemoved}
            >
              -
            </Button>
            <span className="w-8 text-center font-bold">{treatsCount}</span>
            <Button
              variant="outline"
              size="icon"
              className="h-7 w-7 rounded-full"
              onClick={handleAddTreat}
            >
              +
            </Button>
          </div>
        </div>

        {treatsCount > 0 && (
          <div className="mt-2 text-sm">
            <div className="flex justify-between mb-1">
              <span>{t.treats.dietImpact}:</span>
              <span className={dietImpact.startsWith('-') ? 'text-green-600' : 'text-amber-600'}>
                {dietImpact}
              </span>
            </div>
            <p className="text-xs text-gray-600">
              {treatsCount > 2
                ? t.treats.reduceTreats
                : t.treats.withinAllowance
              }
            </p>
          </div>
        )}

        {/* <Button
          variant="secondary"
          size="sm"
          className="mt-4 w-full"
          onClick={handleAddTreat}
        >
          {t.treats.addTreat}
        </Button> */}
      </CardContent>
    </Card>
  );
};

export default TreatTracker;
