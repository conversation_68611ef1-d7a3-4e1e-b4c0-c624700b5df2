
import React from 'react';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { useLanguage } from '@/hooks/useLanguage';
import { type DateRange } from 'react-day-picker';

interface LocalizedCalendarProps {
  mode: "single" | "multiple" | "range";
  selected?: Date | Date[] | DateRange | undefined;
  onSelect: (date: Date | Date[] | DateRange | undefined) => void;
  initialFocus?: boolean;
}

export function LocalizedCalendar({
  mode,
  selected,
  onSelect,
  initialFocus
}: LocalizedCalendarProps) {
  const { language } = useLanguage();
  
  // Create a handler to adapt between the component props and the required format
  const handleSelect = (date: Date | Date[] | DateRange | undefined) => {
    onSelect(date);
  };
  
  // Pass props differently based on the mode to satisfy TypeScript
  if (mode === "range") {
    return (
      <CalendarComponent 
        mode={mode}
        selected={selected as DateRange | undefined}
        onSelect={handleSelect as (date: DateRange | undefined) => void}
        initialFocus={initialFocus}
        numberOfMonths={2}
      />
    );
  } else if (mode === "multiple") {
    return (
      <CalendarComponent 
        mode={mode}
        selected={selected as Date[] | undefined}
        onSelect={handleSelect as (date: Date[] | undefined) => void}
        initialFocus={initialFocus}
      />
    );
  } else {
    return (
      <CalendarComponent 
        mode="single"
        selected={selected as Date | undefined}
        onSelect={handleSelect as (date: Date | undefined) => void}
        initialFocus={initialFocus}
      />
    );
  }
}
