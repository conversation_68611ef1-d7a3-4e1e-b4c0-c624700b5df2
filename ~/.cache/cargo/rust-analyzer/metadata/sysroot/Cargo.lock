# This file is automatically @generated by Car<PERSON>.
# It is not intended for manual editing.
version = 4

[[package]]
name = "addr2line"
version = "0.25.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9acbfca36652500c911ddb767ed433e3ed99b032b5d935be73c6923662db1d43"
dependencies = [
 "gimli",
 "rustc-std-workspace-alloc",
 "rustc-std-workspace-core",
]

[[package]]
name = "adler2"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "320119579fcad9c21884f5c4861d16174d0e06250625266f50fe6898340abefa"
dependencies = [
 "rustc-std-workspace-core",
]

[[package]]
name = "alloc"
version = "0.0.0"
dependencies = [
 "compiler_builtins",
 "core",
]

[[package]]
name = "alloctests"
version = "0.0.0"
dependencies = [
 "rand",
 "rand_xorshift",
]

[[package]]
name = "cc"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1aeb932158bd710538c73702db6945cb68a8fb08c519e6e12706b94263b36db8"
dependencies = [
 "shlex",
]

[[package]]
name = "cfg-if"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9555578bc9e57714c812a1f84e4fc5b4d21fcb063490c624de019f7464c91268"
dependencies = [
 "rustc-std-workspace-core",
]

[[package]]
name = "compiler_builtins"
version = "0.1.160"
dependencies = [
 "cc",
 "core",
]

[[package]]
name = "core"
version = "0.0.0"

[[package]]
name = "coretests"
version = "0.0.0"
dependencies = [
 "rand",
 "rand_xorshift",
]

[[package]]
name = "dlmalloc"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d01597dde41c0b9da50d5f8c219023d63d8f27f39a27095070fd191fddc83891"
dependencies = [
 "cfg-if",
 "libc",
 "rustc-std-workspace-core",
 "windows-sys",
]

[[package]]
name = "fortanix-sgx-abi"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57cafc2274c10fab234f176b25903ce17e690fca7597090d50880e047a0389c5"
dependencies = [
 "compiler_builtins",
 "rustc-std-workspace-core",
]

[[package]]
name = "getopts"
version = "0.2.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cba6ae63eb948698e300f645f87c70f76630d505f23b8907cf1e193ee85048c1"
dependencies = [
 "rustc-std-workspace-core",
 "rustc-std-workspace-std",
 "unicode-width",
]

[[package]]
name = "gimli"
version = "0.32.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93563d740bc9ef04104f9ed6f86f1e3275c2cdafb95664e26584b9ca807a8ffe"
dependencies = [
 "rustc-std-workspace-alloc",
 "rustc-std-workspace-core",
]

[[package]]
name = "hashbrown"
version = "0.15.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5971ac85611da7067dbfcabef3c70ebb5606018acd9e2a3903a0da507521e0d5"
dependencies = [
 "rustc-std-workspace-alloc",
 "rustc-std-workspace-core",
]

[[package]]
name = "hermit-abi"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc0fef456e4baa96da950455cd02c081ca953b141298e41db3fc7e36b1da849c"
dependencies = [
 "rustc-std-workspace-alloc",
 "rustc-std-workspace-core",
]

[[package]]
name = "libc"
version = "0.2.172"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d750af042f7ef4f724306de029d18836c26c1765a54a6a3f094cbd23a7267ffa"
dependencies = [
 "rustc-std-workspace-core",
]

[[package]]
name = "memchr"
version = "2.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a282da65faaf38286cf3be983213fcf1d2e2a58700e808f83f4ea9a4804bc0"
dependencies = [
 "rustc-std-workspace-core",
]

[[package]]
name = "miniz_oxide"
version = "0.8.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fa76a2c86f704bdb222d66965fb3d63269ce38518b83cb0575fca855ebb6316"
dependencies = [
 "adler2",
 "rustc-std-workspace-alloc",
 "rustc-std-workspace-core",
]

[[package]]
name = "object"
version = "0.37.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03fd943161069e1768b4b3d050890ba48730e590f57e56d4aa04e7e090e61b4a"
dependencies = [
 "memchr",
 "rustc-std-workspace-alloc",
 "rustc-std-workspace-core",
]

[[package]]
name = "panic_abort"
version = "0.0.0"
dependencies = [
 "alloc",
 "compiler_builtins",
 "core",
 "libc",
]

[[package]]
name = "panic_unwind"
version = "0.0.0"
dependencies = [
 "alloc",
 "cfg-if",
 "compiler_builtins",
 "core",
 "libc",
 "unwind",
]

[[package]]
name = "proc_macro"
version = "0.0.0"
dependencies = [
 "core",
 "rustc-literal-escaper",
 "std",
]

[[package]]
name = "profiler_builtins"
version = "0.0.0"
dependencies = [
 "cc",
]

[[package]]
name = "r-efi"
version = "5.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74765f6d916ee2faa39bc8e68e4f3ed8949b48cccdac59983d287a7cb71ce9c5"
dependencies = [
 "compiler_builtins",
 "rustc-std-workspace-core",
]

[[package]]
name = "r-efi-alloc"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e43c53ff1a01d423d1cb762fd991de07d32965ff0ca2e4f80444ac7804198203"
dependencies = [
 "compiler_builtins",
 "r-efi",
 "rustc-std-workspace-core",
]

[[package]]
name = "rand"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fbfd9d094a40bf3ae768db9361049ace4c0e04a4fd6b359518bd7b73a73dd97"
dependencies = [
 "rand_core",
]

[[package]]
name = "rand_core"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99d9a13982dcf210057a8a78572b2217b667c3beacbf3a0d8b454f6f82837d38"

[[package]]
name = "rand_xorshift"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "513962919efc330f829edb2535844d1b912b0fbe2ca165d613e4e8788bb05a5a"
dependencies = [
 "rand_core",
]

[[package]]
name = "rustc-demangle"
version = "0.1.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "989e6739f80c4ad5b13e0fd7fe89531180375b18520cc8c82080e4dc4035b84f"
dependencies = [
 "rustc-std-workspace-core",
]

[[package]]
name = "rustc-literal-escaper"
version = "0.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0041b6238913c41fe704213a4a9329e2f685a156d1781998128b4149c230ad04"
dependencies = [
 "rustc-std-workspace-std",
]

[[package]]
name = "rustc-std-workspace-alloc"
version = "1.99.0"
dependencies = [
 "alloc",
]

[[package]]
name = "rustc-std-workspace-core"
version = "1.99.0"
dependencies = [
 "compiler_builtins",
 "core",
]

[[package]]
name = "rustc-std-workspace-std"
version = "1.99.0"
dependencies = [
 "std",
]

[[package]]
name = "shlex"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fda2ff0d084019ba4d7c6f371c95d8fd75ce3524c3cb8fb653a3023f6323e64"

[[package]]
name = "std"
version = "0.0.0"
dependencies = [
 "addr2line",
 "alloc",
 "cfg-if",
 "compiler_builtins",
 "core",
 "dlmalloc",
 "fortanix-sgx-abi",
 "hashbrown",
 "hermit-abi",
 "libc",
 "miniz_oxide",
 "object",
 "panic_abort",
 "panic_unwind",
 "r-efi",
 "r-efi-alloc",
 "rand",
 "rand_xorshift",
 "rustc-demangle",
 "std_detect",
 "unwind",
 "wasi",
 "windows-targets 0.0.0",
]

[[package]]
name = "std_detect"
version = "0.1.5"
dependencies = [
 "cfg-if",
 "libc",
 "rustc-std-workspace-alloc",
 "rustc-std-workspace-core",
]

[[package]]
name = "sysroot"
version = "0.0.0"
dependencies = [
 "proc_macro",
 "profiler_builtins",
 "std",
 "test",
]

[[package]]
name = "test"
version = "0.0.0"
dependencies = [
 "core",
 "getopts",
 "libc",
 "std",
]

[[package]]
name = "unicode-width"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a1a07cc7db3810833284e8d372ccdc6da29741639ecc70c9ec107df0fa6154c"
dependencies = [
 "rustc-std-workspace-core",
 "rustc-std-workspace-std",
]

[[package]]
name = "unwind"
version = "0.0.0"
dependencies = [
 "cfg-if",
 "compiler_builtins",
 "core",
 "libc",
 "unwinding",
]

[[package]]
name = "unwinding"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d80f6c2bfede213d9a90b4a14f3eb99b84e33c52df6c1a15de0a100f5a88751"
dependencies = [
 "compiler_builtins",
 "gimli",
 "rustc-std-workspace-core",
]

[[package]]
name = "wasi"
version = "0.11.1+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ccf3ec651a847eb01de73ccad15eb7d99f80485de043efb2f370cd654f4ea44b"
dependencies = [
 "rustc-std-workspace-alloc",
 "rustc-std-workspace-core",
]

[[package]]
name = "windows-sys"
version = "0.59.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e38bc4d79ed67fd075bcc251a1c39b32a1776bbe92e5bef1f0bf1f8c531853b"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-targets"
version = "0.0.0"

[[package]]
name = "windows-targets"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b724f72796e036ab90c1021d4780d4d3d648aca59e491e6b98e725b84e99973"
dependencies = [
 "windows_aarch64_gnullvm",
 "windows_aarch64_msvc",
 "windows_i686_gnu",
 "windows_i686_gnullvm",
 "windows_i686_msvc",
 "windows_x86_64_gnu",
 "windows_x86_64_gnullvm",
 "windows_x86_64_msvc",
]

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a4622180e7a0ec044bb555404c800bc9fd9ec262ec147edd5989ccd0c02cd3"

[[package]]
name = "windows_aarch64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09ec2a7bb152e2252b53fa7803150007879548bc709c039df7627cabbd05d469"

[[package]]
name = "windows_i686_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e9b5ad5ab802e97eb8e295ac6720e509ee4c243f69d781394014ebfe8bbfa0b"

[[package]]
name = "windows_i686_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0eee52d38c090b3caa76c563b86c3a4bd71ef1a819287c19d586d7334ae8ed66"

[[package]]
name = "windows_i686_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "240948bc05c5e7c6dabba28bf89d89ffce3e303022809e73deaefe4f6ec56c66"

[[package]]
name = "windows_x86_64_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "147a5c80aabfbf0c7d901cb5895d1de30ef2907eb21fbbab29ca94c5b08b1a78"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24d5b23dc417412679681396f2b49f3de8c1473deb516bd34410872eff51ed0d"

[[package]]
name = "windows_x86_64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "589f6da84c646204747d1270a2a5661ea66ed1cced2631d546fdfb155959f9ec"
