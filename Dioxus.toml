# * Android permissions
# *   https://github.com/DioxusLabs/dioxus/issues/3870#issuecomment-3095645100

# * iOS deploy
# *   https://github.com/DioxusLabs/dioxus/issues/3817

# * File picker
# *   https://github.com/DioxusLabs/dioxus/issues/3850
# *   iOS
# *     https://github.com/DioxusLabs/dioxus/issues/3849#issuecomment-2769470919
# *     https://crates.io/crates/apple-utils
# *   Android
# *     https://github.com/project-robius/robius/tree/main/crates/directories

# * Open URIs
# * https://github.com/project-robius/robius/tree/main/crates/open

# * Purchases
# *   Android (Tauri)
# *     https://github.com/NexelOfficial/tauri-plugin-billing
# *   iOS (Tauri)
# *     https://github.com/inKibra/tauri-plugins/tree/main/packages/tauri-plugin-iap
# *   RevenueCat API
# *    https://www.revenuecat.com/docs/api-v1

# * Other
# *   https://crates.io/crates/crossbow

[application]
# Web `build` & `serve` dist path
out_dir = "dist"

# resource (static) file folder
asset_dir = "assets"

[bundle]
identifier = "com.mydoginfit.app"
name = "My Dog in Fit"
publisher = "DvaPlus d.o.o."
# * Icons and other resources workaround for Android
# *   https://github.com/DioxusLabs/dioxus/issues/3685
# *   https://github.com/DioxusLabs/dioxus/issues/3397#issuecomment-2552669727
icon = [
  "assets/logo.png",
  "assets/icons/icon-32x32.png",
  "assets/icons/icon-48x48.png",
  "assets/icons/icon-72x72.png",
  "assets/icons/icon-96x96.png",
  "assets/icons/icon-128x128.png",
  "assets/icons/icon-144x144.png",
  "assets/icons/icon-152x152.png",
  "assets/icons/icon-167x167.png",
  "assets/icons/icon-180x180.png",
  "assets/icons/icon-192x192.png",
  "assets/icons/icon-256x256.png",
  "assets/icons/icon-384x384.png",
  "assets/icons/icon-512x512.png",
]
# Bundle resources
resources = ["assets/*"]
# Bundle copyright
copyright = ""
# Bundle category
category = "Pets"
# Bundle short description
short_description = "My Dog in Fit"
# Bundle long description
long_description = """
Track your dog's fitness and health.
"""

[web.app]
title = "My Dog in Fit"

# include `assets` in web platform
[web.resource]

# Additional CSS style files
style = []

# Additional JavaScript files
script = []

[web.resource.dev]

# Javascript code file
# serve: [dev-server] only
script = []

[web.wasm_opt]
# The level wasm-opt should target. z is the smallest. 4 is the fastest.
# level = "4"

[web.watcher]
index_on_404 = true
watch_path = ["src", "assets"]

# Android configuration
[android]
# Use the proper Android NDK linker
linker = "x86_64-linux-android24-clang"

# Android permissions required for file picker
permissions = [
  "android.permission.READ_EXTERNAL_STORAGE",
  "android.permission.READ_MEDIA_IMAGES",
  "android.permission.CAMERA",
]

# Android manifest additions
[android.manifest]
# Add activity configuration for file picker
activity = """
<activity
    android:name=".MainActivity"
    android:exported="true"
    android:theme="@style/Theme.AppCompat.Light.NoActionBar">
    <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
    </intent-filter>
</activity>
"""
