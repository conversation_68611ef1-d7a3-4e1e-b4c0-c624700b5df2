import type { Config } from "tailwindcss"
import animatePlugin from "tailwindcss-animate"

export default {
  experimental: {
    optimizeUniversalDefaults: true,
  },
  darkMode: ["class", ".dark"],
  content: [
    // "./pages/**/*.{ts,tsx}",
    // "./components/**/*.{ts,tsx}",
    // "./app/**/*.{ts,tsx}",
    "./src/**/*.{html,ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        sm: "640px",
        md: "768px",
        lg: "1024px",
        xl: "1280px",
        "2xl": "1400px",
      },
    },
  },
  plugins: [
    animatePlugin,
    function ({ addVariant }) {
      // Add a custom variant for the "colored" theme
      addVariant("colored", ".colored &");
    },
  ],
} satisfies Config;
