fn main() {
    #[cfg(target_os = "android")]
    {
        // Link Android system libraries
        println!("cargo:rustc-link-lib=log");
        println!("cargo:rustc-link-lib=android");
        println!("cargo:rustc-link-lib=c");
        println!("cargo:rustc-link-lib=m");
        println!("cargo:rustc-link-lib=dl");

        // Add Android NDK paths
        if let Ok(ndk_home) = std::env::var("ANDROID_NDK_HOME") {
            let target_arch =
                std::env::var("CARGO_CFG_TARGET_ARCH").unwrap_or_else(|_| "x86_64".to_string());
            let android_target = match target_arch.as_str() {
                "aarch64" => "aarch64-linux-android",
                "arm" => "arm-linux-androideabi",
                "x86" => "i686-linux-android",
                _ => "x86_64-linux-android",
            };

            println!(
                "cargo:rustc-link-search=native={}/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/\
                 {}",
                ndk_home, android_target
            );
        }

        // Handle missing symbols for android_properties
        println!("cargo:rustc-link-arg=-Wl,--undefined-version");
        println!("cargo:rustc-link-arg=-Wl,--allow-shlib-undefined");

        // Set Android API level
        if let Ok(api_level) = std::env::var("ANDROID_API_LEVEL") {
            println!("cargo:rustc-env=ANDROID_API_LEVEL={}", api_level);
        } else {
            println!("cargo:rustc-env=ANDROID_API_LEVEL=28");
        }
    }
}
