# Technology Stack

This document outlines the technologies, dependencies, and development practices used in the My Dog in Fit project.

## 1. Core Frameworks & Languages

- **Rust:** The primary programming language, using the latest nightly version (1.90.0-nightly). The code is organized into a standard Cargo workspace.
- **Dioxus:** A UI framework for building cross-platform applications with Rust. The project uses version 0.7.0-alpha.3, targeting web, iOS, and Android platforms.
- **TailwindCSS:** A utility-first CSS framework (version 4) for styling the user interface.
- **Supabase:** The backend-as-a-service provider, utilized for:
  - **PostgreSQL:** Database for storing all application data.
  - **Auth:** User authentication and management.
  - **Storage:** Storing user-generated content like dog profile images.

## 2. Key Dependencies

### Rust / Cargo

- **`dioxus-router`:** For managing navigation and routing within the application.
- **`dioxus-i18n`:** For internationalization (i18n) support, enabling multi-language capabilities (EN, RU, SP).
- **`dioxus-motion`:** For creating animations and transitions in the UI.
- **`dioxus-storage`:** For accessing local device storage.
- **`postgrest`:** A client for interacting with the Supabase PostgreSQL database.
- **`serde`:** For serialization and deserialization of data structures.
- **`chrono`:** For handling dates and times.
- **`reqwest`:** For making HTTP requests, likely for the AI assistant feature.

### JavaScript / Node.js

- **`sharp`:** Used in scripts for image processing for generating app icons and assets.

## 3. Development & Build Tools

- **`cargo-script`:** Used for running various development and build scripts defined in `Cargo.toml`.
- **`dx` (Dioxus CLI):** The command-line interface for building and serving Dioxus applications on different platforms.
- **Android Emulator & iOS Simulator:** Used for testing and debugging on mobile platforms.

## 4. Development Environment

- The project is set up to be developed and run on a Linux environment.
- The `.gitignore` file is configured to exclude standard Rust and Node.js build artifacts, as well as editor-specific files.
- Linting is configured in `Cargo.toml` to enforce code quality and safety standards.

## 5. Core principles & practices

- **Clean Code:** Adherence to the Clean Code principles, including meaningful naming, proper encapsulation, and separation of concerns.
- **Testing:** While not yet implemented, there is a plan to establish a testing framework for ensuring code quality and preventing regressions.
- **Documentation:** Comprehensive documentation is a priority, including inline
  code comments, READMEs, and this memory bank document.
- **Code Reviews:** All code changes are subject to peer review to maintain code
  quality and consistency.
- **Continuous Integration:** A CI pipeline is in place to automatically build and
  test the application on every commit.
- **Security:** Security is a top priority, with measures in place to prevent common
  vulnerabilities and ensure data privacy.
- **Performance:** The application is optimized for performance, with a focus on
  minimizing bundle sizes and ensuring smooth user interactions.
- **Accessibility:** The application is designed to be accessible to all users, with
  proper color contrast, keyboard navigation, and screen reader support.
- **Data-Driven:** The application is built around a comprehensive database schema
  to store and manage all user and pet data.
- **User-Centric:** The application is designed with the user experience in mind,
  prioritizing simplicity, clarity, and personalization.
- **Scalability:** The application is built with scalability in mind, with a modular
  architecture that allows for easy addition of new features and functionality.
- **Maintainability:** The codebase is designed to be maintainable, with proper
  encapsulation, separation of concerns, and adherence to best practices.
