# Product: My Dog in Fit

## 1. Vision & Purpose

My Dog in Fit is a comprehensive mobile application designed to empower dog
owners to proactively manage their pet's health and fitness. It serves as a
centralized hub for tracking key metrics related to nutrition, exercise, and
weight, ultimately helping owners maintain their dog's optimal well-being.

## 2. Problem Solved

Modern dog owners often struggle to:

- Accurately track their dog's caloric intake and ensure a balanced diet.
- Monitor exercise levels to prevent both inactivity and overexertion.
- Keep a consistent record of their dog's weight and health progress over time.
- Access reliable, personalized recommendations for their specific dog's needs.

My Dog in Fit addresses these challenges by providing an easy-to-use,
data-driven tool for holistic pet care.

## 3. Core User Experience

The application is designed to be intuitive, visually appealing, and
encouraging. The user experience revolves around:

- **Simplicity:** Quick and easy logging of meals and activities.
- **Clarity:** Clear visualization of data through charts and progress trackers.
- **Personalization:** Tailored content and recommendations based on the dog's
  profile (breed, gender, age, weight, etc.).
- **Engagement:** A supportive and motivating environment with reminders and
  insights.
- **Cross-Platform Accessibility:** A seamless experience on both iOS and
  Android devices.

## 4. Key Features

The application's functionality is built around the following core features:

- **Onboarding:** A simple and interactive onboarding process to set up the
  first dog's profile. Right after the app provides daily calories intake and
  expenditure recommendations, daily diet recommendations, and a list of all
  suitable foods. The owner optionally indicates, which foods he/she uses or
  will use.
- **Dog Profile Management:** Create and manage detailed profiles for one or
  more dogs - name, photo (the app tries to detect the breed and other info by
  this photo), gender, breed (optional - if available and known), size (small,
  middle, large), weight (a tip with visual instructions on "how to correctly
  measure dog’s weight at home" are necessary), age, Body Condition Score (BCS,
  range 1-9, a 5-step scale with images and descriptions for each step),
  typical kind of dog's activity (walking, running, mixed) and preferred foods
  (from the list of all suitable foods, user leaves a comment on food removal).
- **Meal & Calorie Recommendations:** Automatically calculate recommended daily
  caloric intake and diet (food-weight pairs) adjustments based on activity and
  treats.
- **Activity Monitoring:** Track walks and other activities using a built-in
  timer or through fitness tracker integration.
- **Weight & Health Analytics:** Log weight entries and visualize weight, BCS
  and BMI trends over time.
- **AI-Powered Recommendations:** Receive personalized, non-medical advice on
  nutrition and exercise.
- **AI-Powered assistant:** Chat with the AI assistant to get answers to
  questions and recommendations related to the dog's health and well-being.
- **Multi-Language Support:** Available in English, Russian, and Spanish.
- **Metric/imperial units support:** The app supports both metric and imperial
  units for weight and size.

## 5. App flow and screens

### Standard flow

Onboarding -> Home

### Navigation

Bottom navbar:

1. Home,
2. Dog profile,
3. Activity start/stop button,
4. AI Assistant,
5. Settings

### Screens

#### Home

1. Dog selection header
2. BCS smooth line chart
3. Health metrics (weight, BCS, BMI, health score), history in subscreens
4. Today's diet (calories intake, recommended vs. actual, food
   recommendations, treats tracker)
5. Today's activity (recommended and actual duration, kind, calories burnt),
   history in subscreen
6. Today's random AI recommendations

#### Dog profile

1. Dog selection header
2. Editable fields:

- Photo
- Name
- Breed
- Gender
- Birthday
- Size (small, medium, large visual selector)
- Weight
- BCS (visual selector)
- Typical activity (walking, running, mixed)
- Preferred food (add from the suitable foods list, remove)
- Food choices history (subscreen)
- Delete dog profile button

#### AI Assistant

1. Dog selection header
   Chat box
2.
3. Send a picture of a dog's food label to get calories and components
   information
4. Get recommendations for the dog's diet and activity
5. Get answers to questions about the dog's health and well-being

#### Settings

1. User's preferred language
2. User's preferred units
3. User's preferred theme
4. User's notifications settings
5. User's account settings (signup/signin, logout, delete account)

## 6. Other features

- On the second day of use the app asks a user to enter his/her email to receive
  surveys and news from the app.
- Every 2-3 weeks the app asks a user to update a dog's weight data. The app
  recalculates calories and diet, informs a user.
- Achievements
- Dog photo update once a month, comparison with previous ones in a timeline.
- Share an image with dogs photo and achievements
- Refer a friend (link copying)
- For different activities in the application, a user receives discounts on
  goods and services for a dog.
- Publication of a short video (1 min) review of the app by a user. The app
  attaches "before and after" photos of the dog to the video, the user gets
  perks for this. A checkbox "allow public sharing" is necessary.
- Leaderboards
- Notes on the food change (by a user decision or based on the recommendation by
  the app), a change in a diet and activity recommendations based on this.
- In A/B testing mode within an onboarding ask a user to answer a few questions
  (choices), e.g. "How do you plan to use this app?", "What is the main problem
  you want to solve?"
- Ask to enable push notification in the right app context, i.e. when explaining
  that we can send daily diet updates.
