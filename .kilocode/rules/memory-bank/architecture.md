# System Architecture

This document provides an overview of the My Dog in Fit application's architecture, including its structure, key components, and design patterns.

## 1. High-Level Overview

The application is a cross-platform mobile app built with Rust and Dioxus, targeting iOS, Android, and web platforms. It follows a component-based architecture, where the UI is composed of reusable and modular components. The backend is powered by Supabase, which provides database, authentication, and storage services.

```mermaid
graph TD
    subgraph "Client (Dioxus)"
        direction TB
        A[UI Components] --> B[Pages]
        B --> C[Router]
        C --> D[State Management]
        D --> E[Hooks]
        E --> F[API Integration]
    end

    subgraph "Backend (Supabase)"
        direction TB
        G[PostgreSQL]
        H[Authentication]
        I[Storage]
    end

    F --> G
    F --> H
    F --> I

    subgraph "Build & Tooling"
        direction RL
        J[Rust/Cargo]
        K[Dioxus CLI]
        L[TailwindCSS]
        M[Node.js Scripts]
    end

    J --> A;
    K --> A;
    L --> A;
    M --> A;

```

## 2. Source Code Structure

The source code is organized into several key directories within the `src` folder:

- **`main.rs`**: The main entry point of the application, responsible for initializing the logger, setting up the environment, and launching the Dioxus application.
- **`config`**: Contains application-level configuration settings.
- **`models`**: Defines the core data structures used throughout the application, such as `Dog`, `Meal`, and `User`.
- **`hooks`**: Contains custom Dioxus hooks that encapsulate reusable logic, such as data fetching, authentication, and state management.
- **`integrations`**: Handles communication with external services, primarily Supabase for backend operations.
- **`i18n`**: Manages internationalization and localization.
- **`ui`**: Contains all UI-related code, including components, pages, layouts, and routing.
  - **`components`**: Reusable UI elements, categorized into `base` (simple, foundational components) and `advanced` (more complex, feature-specific components).
  - **`pages`**: Top-level UI views that correspond to different routes in the application.
  - **`layout`**: Defines the overall structure and layout of the application, such as the navigation bar and page containers.
  - **`routes.rs`**: Defines the application's routing structure using `dioxus-router`.
- **`state`**: Manages the global application state.
- **`utils`**: Contains utility functions and helper modules.

## 3. Key Technical Decisions & Design Patterns

- **Cross-Platform Development:** The application is built using Dioxus, which
  allows for a single codebase to target multiple platforms (iOS, Android, web)
  with minimal platform-specific code.
- **Code architecture:** The application follows a hexagonal architecture pattern, where the core business logic is separated from the UI and external dependencies.
- **Component-Based UI:** The user interface is built as a tree of Dioxus components, promoting reusability and separation of concerns.
- **State Management:** The application uses a combination of local component
  state (provided by Dioxus hooks) and global state (provided by `dioxus-radio`) management solutions to
  manage the application's data flow.
- **Routing:** Navigation is handled by `dioxus-router`, which maps URL paths to specific page components.
- **Hooks for Reusable Logic:** Custom hooks are used extensively to abstract away complex logic, such as data fetching, authentication, and interactions with local storage.
- **Styling with TailwindCSS:** The UI is styled using TailwindCSS, a utility-first CSS framework that allows for rapid and consistent styling.
- **Backend as a Service (BaaS):** Supabase is used as the backend, simplifying the development process by providing ready-to-use services for database, authentication, and file storage.
- **Offline First:** The application is designed to work offline as much as possible,
  with local data storage and synchronization with the backend when online.
- **Internationalization:** The application supports multiple languages (EN, RU, SP)
  to cater to a global user base.
- **AI-Powered:** The application integrates with Gemini Flash 2.5 for
  AI-powered recommendations and assistance.
