# npx tailwindcss -i ./tailwind.css -o ./assets/tailwind.css --watch

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[package]
name = "my-dog-in-fit"
version = "0.1.0"
edition = "2024"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]


[workspace.metadata.scripts]
_css = "npx tailwindcss -i ./tailwind.css -o ./assets/tailwind.css --optimize --watch=always"

dx-android = "dx serve --hot-patch --android"
_dx-android = "cargo run-script log-android & pid=$! && cargo run-script _dx-android && kill $pid"
dx-ios = "dx serve --hot-patch --ios --target x86_64-apple-ios"
dx-web = "dx serve --web"
__dx-android = "cargo run-script _css &> /dev/null & pid=$! && cargo run-script _dx-android && kill $pid"

emu-android = "QT_QPA_PLATFORM='' emulator -avd Medium_Phone_API_36_2 -netdelay none -netspeed full -no-metrics &"
emu-ios = "xcrun simctl boot 'iPhone 16e'"

# * To filter by the currently running app, use the following parameter to `adb logcat`:
# *   --pid=$(adb shell pidof -s $(grep identifier Dioxus.toml | cut -d'\"' -f 2))
# * Logs stop coming after a Dioxus reload command though.
# *
# * Unicode symbols to use: 🔴🟠🟡🟢🔵🟣
log-android = "adb logcat -s -v time -v year RustStdoutStderr | sed -E '/(s_glBind|FORTIFY: |\\.cc:[0-9]+]|\\.cc\\([0-9]+\\)]|\\.h:[0-9]+])/d; s|([0-9-]+) ([0-9:]+)\\.([0-9]+) I/RustStdoutStderr\\(\\s*[0-9]+\\):(.*)|\\2 \\4|g; s/DEBUG/🔵 /g; s/ INFO/🟢 /g; s/ WARN/🟡 /g; s/ERROR/🔴 /g'"

_generate-icons = "node scripts/generate-app-icon.js && node scripts/generate-icons.js"
generate-icons = "node scripts/generate-icons.js"


[dependencies]
base64 = "0.22"
# async-trait = "0.1.80"
chrono = { version = "0.4", features = ["serde"] }
# daisy_rsx = { git = "https://github.com/tsr8/daisy-rsx.git", branch = "daisy-5.0.6" }
# dioxus = { version = "0.6.3", features = [
dioxus = { version = "0.7.0-rc.0", features = [
  "lib",
  "router",
  # "fullstack",
  "logger",
  # "mobile",
  # "native",
] }
dioxus-free-icons = { git = "https://github.com/Raflos10/dioxus-free-icons", features = [
  "font-awesome-solid",
  "lucide",
] }
dioxus-i18n = "0.4.4"
# dioxus-motion = { git = "https://github.com/wheregmis/dioxus-motion.git", branch = "main", default-features = false, features = [
#   "desktop",     # To let Rust Analyzer be happy
#   "transitions",
# ] }
dioxus-primitives = { git = "https://github.com/dioxuslabs/components", version = "0.0.1" }
dioxus-radio = "0.6.0"
# dioxus_storage = "0.0.4"
dioxus_storage = { git = "https://github.com/ealmloff/dioxus-std", branch = "0.7" }
dotenvy = "0.15.7"
dotenv_codegen = "0.15.0"
gloo-timers = { version = "0.3", features = ["futures"] }
# dioxus-time = { version = "0.7.0-alpha.3" }
# fluent = "0.17.0"
# fluent-bundle = "0.16.0"
# fluent-templates = "0.13.0"
image = { version = "0.25", features = ["jpeg", "png"], optional = true }
postgrest = "1.6.0"
# rand = "0.9.1"
reqwest = { version = "0.12.22", features = ["json", "multipart"] }
# rustls = { version = "0.23.31", features = ["ring"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
thiserror = "2.0.14"
tokio = { version = "1.0", features = ["time"] }
tracing = "0.1"
unic-langid = "0.9.6"
uuid = { version = "1.8.0", features = [
  "v4",
  "fast-rng",
  "macro-diagnostics",
  "js",
] }
# Mobile file handling - platform-specific implementations (base64 already included above)
# modx = "0.1.4"

# reaxive = "1.0.3"

[target.'cfg(target_family = "wasm")'.dependencies]
web-sys = { version = "0.3", features = [
  "Window",
  "Storage",
  "console",
  "File",
  "FileReader",
  "Event",
  "EventTarget",
  "Blob",
  "HtmlInputElement",
  "FileList",
] }
wasm-bindgen = "0.2"
wasm-bindgen-futures = "0.4"
js-sys = "0.3"

[target.'cfg(target_os = "android")'.dependencies]
# Native Android file picker and permissions
jni = "0.21"
# Use a simpler approach without android-activity for now
android-activity = { version = "0.6", features = [
  "native-activity",
], default-features = false }
# Fix for android_properties linking issues
ndk-context = "0.1"
# Additional Android system dependencies
libc = "0.2"
# Note: rfd doesn't support Android - using JNI-based approach

[target.'cfg(target_os = "ios")'.dependencies]
# Native iOS file picker using Objective-C bindings
objc = "0.2"
cocoa = "0.25"
core-foundation = "0.9"
# Note: rfd doesn't support iOS - using Objective-C bridge approach


[patch.crates-io]
# aloe-3p = { path = '../../../Modules/aloe-rs/aloe-3p' }
# aloe-url = { path = '../../../Modules/aloe-rs/aloe-url' }
dioxus-i18n = { path = '../../../Modules/dioxus-i18n' }
dioxus-radio = { path = '../../../Modules/dioxus-radio' }
# dioxus-sdk = { git = "https://github.com/ealmloff/dioxus-std", branch = "0.7" }
# dioxus_storage = { git = "https://github.com/ealmloff/dioxus-std", branch = "0.7" }
# dioxus-sync = { git = "https://github.com/ealmloff/dioxus-std", branch = "0.7" }
dioxus-time = { git = "https://github.com/ealmloff/dioxus-std", branch = "0.7" }
# dioxus-util = { git = "https://github.com/ealmloff/dioxus-std", branch = "0.7" }
# Patch android-properties to avoid linking issues - use a version that doesn't require system properties
# android-properties = { version = "0.2", default-features = false }

# [replace]
# "cocoa:0.25.0" = { git = "https://github.com/servo/core-foundation-rs.git", branch = "main", package = "cocoa" }


[features]
default = ["dotenv"]
dotenv = []
# mobile = ["dioxus/native"]
server = ["dioxus/server"]
web = [
  "dioxus/web",
  # "dioxus-motion/web"
]
desktop = [
  "dioxus/desktop",
  # "dioxus-motion/desktop"
]
mobile = [
  "dioxus/mobile",
  # "dioxus-motion/desktop"
]


[workspace]
members = ["."]
# members = [".", "scripts"]


[lints.rust]
# unsafe_code = "forbid"
# unused = "allow"
unused_imports = "allow"
unused_variables = "allow"

[lints.clippy]
# * https://rust-lang.github.io/rust-clippy/master
# dead_code = "allow"


[profile.release]
# opt-level = "z"
opt-level = 3
debug = false
lto = true
codegen-units = 1
panic = "abort"
strip = true
incremental = false

[profile.dev]
opt-level = 0
debug = 1
incremental = true
lto = "off"
codegen-units = 256
# TODO codegen-backend = "cranelift"

[profile.dev.package."*"]
opt-level = 1 # Page transitions do not work with 2 or 3
debug = 2

# * For build scripts and proc-macros.
[profile.dev.build-override]
opt-level = 3


# * Web
[profile.wasm-dev]
inherits = "dev"
# opt-level = 1

[profile.wasm-dev.package."*"]
opt-level = 1 # Page transitions do not work with 2 or 3
debug = 2

[profile.wasm-dev.build-override]
opt-level = 3


# * Server
[profile.server-dev]
inherits = "dev"
# opt-level = 1

[profile.server-dev.package."*"]
opt-level = 3
debug = 2

[profile.server-dev.build-override]
opt-level = 3


# * Android
[profile.android-dev]
inherits = "dev"

[profile.android-dev.package."*"]
opt-level = 1 # Page transitions do not work with 2 or 3
debug = 2

[profile.android-dev.build-override]
opt-level = 3


# * iOS
[profile.ios-dev]
inherits = "dev"

[profile.ios-dev.package."*"]
opt-level = 1 # Page transitions do not work with 2 or 3
debug = 2

[profile.ios-dev.build-override]
opt-level = 3
