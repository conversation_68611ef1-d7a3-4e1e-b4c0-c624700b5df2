import { defineConfig } from "vite"
import react from "@vitejs/plugin-react-swc"
import tailwindcss from "@tailwindcss/vite"
import path from "path"

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    allowedHosts: [
      "29803045-c95b-4c61-82ee-c83dcc16fb3e.lovableproject.com",
      // Allow any Lovable project domains
      ".lovableproject.com",
    ],
  },
  plugins: [
    react(),
    // mode === "development" && componentTagger(),
    tailwindcss(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
