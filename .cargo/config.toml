# # Android linker configuration - let <PERSON><PERSON><PERSON> handle the linker
# # [target.x86_64-linux-android]
# # linker = "x86_64-linux-android28-clang"
# # rustflags = "-C link-arg=-Wl,--allow-shlib-undefined -C link-arg=-Wl,--undefined-version"

# # [target.aarch64-linux-android]
# # linker = "aarch64-linux-android28-clang"
# # rustflags = "-C link-arg=-Wl,--allow-shlib-undefined -C link-arg=-Wl,--undefined-version"

# # [target.armv7-linux-androideabi]
# # linker = "armv7a-linux-androideabi28-clang"
# # rustflags = "-C link-arg=-Wl,--allow-shlib-undefined -C link-arg=-Wl,--undefined-version"

# # [target.i686-linux-android]
# # linker = "i686-linux-android28-clang"
# # rustflags = "-C link-arg=-Wl,--allow-shlib-undefined -C link-arg=-Wl,--undefined-version"

# # Android-specific environment variables
# [env]
# ANDROID_API_LEVEL = "28"

# # Build flags for Android
# [build]
# target-dir = "target"
