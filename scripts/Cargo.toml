# CHROME=/var/lib/flatpak/exports/bin/com.google.Chrome ~/.cache/cargo/debug/scrap_breeds

# geckodriver --port=4444 -b ~/.local/share/flatpak/exports/bin/org.mozilla.firefox --profile-root ~/.var/app/org.mozilla.firefox/cache/tmp

# CHROME=/var/lib/flatpak/exports/bin/com.google.Chrome chromedriver --port=4444

#  FIREFOX_PROFILE_DIR="$HOME/.var/app/app.zen_browser.zen/.zen/webdriver" geckodriver --port=4444 -b /var/lib/flatpak/exports/bin/app.zen_browser.zen

# cargo build && ~/.cache/cargo/debug/scrap_breeds

[package]
name = "scripts"
version = "1.0.0"
edition = "2024"
authors = ["Alexandr Priezzhev <<EMAIL>>"]

[dependencies]
anyhow = "1.0.98"
chrono = { version = "0.4", features = ["serde"] }
clap = { version = "4.5.45", features = ["derive"] }
futures = "0.3"
hashlink = "0.10.0"
# polars = { version = "0.50.0", features = ["ndarray", "lazy", "parquet"] }
# rayon = "1.10.0"
regex = "1.11.1"
scraper = "0.23.1"
# serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
slog = "2.7.0"
# slog-async = "2.8.0"
slog-term = "2.9.1"
thiserror = "2.0.12"
tokio = { version = "1.47.1", features = [
  "full",
  # "rt-multi-thread",
  # "time",
  # "macros",
] } # tokio = { version = "1.0", features = ["time"] }

[lib]
name = "scripts"
path = "src/lib.rs"

# [[bin]]
# name = "scraper"
# path = "src/bin/scraper.rs"


# [lints.rust]
# unused = "allow"
# unused_imports = "allow"
# unused_variables = "allow"
