/**
 * @fileoverview Generates the main application icon as an SVG-based PNG image.
 *
 * This script creates a simple text-based app icon with the application name
 * "my DOG in FIT" rendered as black text on a transparent background.
 * The generated icon is saved as a 512x512 PNG file for use as the base
 * application icon.
 */

import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import {fileURLToPath} from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const text = `my DOG\\nin   FIT`;
const width = 512;
const height = 512;

const svg = `
<svg width="${width}" height="${height}">
  <style>
    .title { fill: black; font-size: 60px; font-weight: bold; text-anchor: middle; font-family: sans-serif }
  </style>
  <text x="${width / 2}" y="${height / 2 - 30}" class="title">${text.split('\\n')[0]}</text>
  <text x="${width / 2}" y="${height / 2 + 40}" class="title">${text.split('\\n')[1]}</text>
</svg>
`;

const buffer = Buffer.from(svg);

sharp(buffer, {
  svg: {
    dpi: 300
  }
})
  .png()
  .toFile(path.join(__dirname, '../src/assets/app-icon.png'))
  .then(() => console.log('App icon generated successfully!'))
  .catch(err => console.error('Error generating app icon:', err));
