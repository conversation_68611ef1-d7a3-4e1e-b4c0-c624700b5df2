# Database Deployment Guide

This guide explains how to deploy the My Dog in Fit database schema to Supabase.

## Prerequisites

1. A Supabase project created at https://supabase.com
2. Supabase CLI installed: `npm install -g @supabase/cli`
3. Environment variables configured

## Setup Steps

### 1. Configure Environment Variables

Copy `.env.example` to `.env` and fill in your Supabase credentials:

```bash
cp .env.example .env
```

Update `.env` with your actual Supabase project details:
- `SUPABASE_URL`: Your project URL from Supabase dashboard
- `SUPABASE_ANON_KEY`: Your project's anon/public key

### 2. Deploy Database Schema

#### Option A: Using Supabase Dashboard (Recommended for initial setup)

1. Go to your Supabase project dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of `database/schema.sql`
4. Execute the SQL script

#### Option B: Using Supabase CLI

```bash
# Initialize Supabase project (if not already done)
supabase init

# Link to your remote project
supabase link --project-ref your-project-id

# Apply the schema
supabase db push

# Or apply migrations directly
psql -h your-db-host -U postgres -d postgres -f database/schema.sql
```

### 3. Verify Deployment

After deployment, verify these tables exist:
- `user_profiles`
- `breeds`
- `dogs`
- `weight_logs`
- `bcs_logs`
- `activities`
- `foods`
- `dog_preferred_foods`
- `meals`
- `meal_items`
- `recommendations`
- `ai_chat`

### 4. Test Authentication

The app will automatically use the configured Supabase Auth. Test by:
1. Running the app
2. Going through the onboarding flow
3. Verifying user creation in Supabase Auth dashboard

### 5. Seed Data (Optional)

For development/testing, you can add sample breed and food data:

```sql
-- Example breed data
INSERT INTO breeds (name, slug, size_category, male_weight_min, male_weight_max, female_weight_min, female_weight_max) VALUES
('Labrador Retriever', 'labrador-retriever', 'large', 29.0, 36.0, 25.0, 32.0),
('Golden Retriever', 'golden-retriever', 'large', 30.0, 34.0, 25.0, 29.0),
('German Shepherd', 'german-shepherd', 'large', 30.0, 40.0, 22.0, 32.0);

-- Example food data
INSERT INTO foods (name, brand, food_type, calories_per_100g, protein_percentage, fat_percentage) VALUES
('Adult Dry Food', 'Royal Canin', 'dry', 380, 23.0, 13.0),
('Puppy Formula', 'Hill''s Science', 'dry', 420, 25.0, 15.0),
('Wet Food Chicken', 'Blue Buffalo', 'wet', 120, 8.0, 5.0);
```

## Troubleshooting

- **Connection Error**: Verify SUPABASE_URL and SUPABASE_ANON_KEY are correct
- **Permission Error**: Ensure RLS policies are properly configured
- **Missing Tables**: Re-run the schema deployment script
- **Auth Issues**: Check Supabase Auth settings and email templates

## Security Notes

- Never commit real credentials to version control
- Use environment-specific `.env` files
- Regularly rotate API keys
- Monitor RLS policy effectiveness in production